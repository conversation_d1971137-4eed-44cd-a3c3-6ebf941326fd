pipeline {
    agent {
        docker {
            image 'node:22'
            args '-v /var/jen<PERSON>_home/.npm:/root/.npm'
        }
    }
    
    environment {
        // Common environment variables
        NODE_OPTIONS = '--no-warnings'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Setup Environment') {
            steps {
                script {
                    sh '''
                        echo "🛠️ Setting up build environment..."

                        # Clean up any existing node_modules with permission issues
                        if [ -d "node_modules" ]; then
                            echo "Removing existing node_modules..."
                            rm -rf node_modules || true
                        fi

                        # Remove package-lock.json to avoid conflicts
                        rm -f package-lock.json || true

                        # Clean npm cache
                        npm cache clean --force || true

                        echo "✅ Environment setup completed"
                    '''
                }
            }
        }

        stage('Install Dependencies') {
            steps {
                script {
                    sh '''
                        echo "📦 Installing dependencies..."

                        # Use npm install with specific flags to avoid permission issues
                        npm install --legacy-peer-deps \
                                   --no-optional \
                                   --prefer-offline \
                                   --no-audit \
                                   --progress=false \
                                   --loglevel=warn

                        echo "✅ Dependencies installed successfully"
                    '''
                }
            }
        }
        
        stage('Lint') {
            steps {
                sh 'npm run lint'
            }
        }
        
        stage('Build') {
            parallel {
                stage('Build Staging') {
                    when {
                        branch 'develop'
                    }
                    steps {
                        // Create staging environment file and build
                        sh '''
                            chmod +x generate-env.sh
                            npm run generate-env:staging
                            npm run build:staging
                        '''
                        // Archive the build artifacts
                        sh 'tar -czf dist-staging.tar.gz dist/'
                        archiveArtifacts artifacts: 'dist-staging.tar.gz', fingerprint: true
                    }
                }
                
                stage('Build Production') {
                    when {
                        branch 'main'
                    }
                    steps {
                        // Create production environment file and build
                        sh '''
                            chmod +x generate-env.sh
                            npm run generate-env:prod
                            npm run build:prod
                        '''
                        // Archive the build artifacts
                        sh 'tar -czf dist-production.tar.gz dist/'
                        archiveArtifacts artifacts: 'dist-production.tar.gz', fingerprint: true
                    }
                }
            }
        }
        
        stage('Deploy') {
            parallel {
                stage('Deploy to Staging') {
                    when {
                        branch 'develop'
                    }
                    steps {
                        // Deploy to staging server
                        sh '''
                            mkdir -p /var/lib/jenkins/workspace/Nex-ticketing-frontend-staging
                            tar -xzf dist-staging.tar.gz -C /var/lib/jenkins/workspace/Nex-ticketing-frontend-staging --strip-components=1
                            echo "Deployed to staging environment"
                        '''
                    }
                }
                
                stage('Deploy to Production') {
                    when {
                        branch 'main'
                    }
                    steps {
                        // Deploy to production server
                        sh '''
                            mkdir -p /var/lib/jenkins/workspace/Nex-ticketing-frontend-production
                            tar -xzf dist-production.tar.gz -C /var/lib/jenkins/workspace/Nex-ticketing-frontend-production --strip-components=1
                            echo "Deployed to production environment"
                        '''
                    }
                }
            }
        }
    }
    
    post {
        always {
            script {
                sh '''
                    echo "🧹 Cleaning up workspace..."

                    # Remove node_modules to prevent permission issues in future builds
                    if [ -d "node_modules" ]; then
                        rm -rf node_modules || true
                    fi

                    # Remove package-lock.json
                    rm -f package-lock.json || true

                    # Clean npm cache
                    npm cache clean --force || true

                    echo "✅ Cleanup completed"
                '''
            }

            // Clean workspace but keep important artifacts
            cleanWs(
                cleanWhenAborted: true,
                cleanWhenFailure: true,
                cleanWhenNotBuilt: true,
                cleanWhenSuccess: true,
                cleanWhenUnstable: true,
                deleteDirs: true,
                disableDeferredWipeout: true,
                notFailBuild: true,
                patterns: [
                    [pattern: 'node_modules/**', type: 'INCLUDE'],
                    [pattern: 'package-lock.json', type: 'INCLUDE'],
                    [pattern: '.npm/**', type: 'INCLUDE']
                ]
            )
        }
        success {
            echo '✅ Build and deployment completed successfully!'
        }
        failure {
            echo '❌ Build or deployment failed!'

            script {
                sh '''
                    echo "📋 Debug information:"
                    echo "Working directory: $(pwd)"
                    echo "User: $(whoami)"
                    echo "User ID: $(id)"
                    echo "Node version: $(node --version)"
                    echo "NPM version: $(npm --version)"

                    if [ -d "node_modules" ]; then
                        echo "node_modules permissions:"
                        ls -la node_modules/ | head -10
                    fi
                '''
            }
        }
    }
}
