# Environment Configuration Guide

This document explains how to use the `generate-env.sh` script to manage environment-specific configurations for the Ticketing System Tool frontend application.

## Overview

The application uses different environment configurations for:
- Development (local development)
- Staging (testing environment)
- Production (live environment)

Each environment has its own set of API endpoints and WebSocket URLs that are configured through environment files (`.env.development`, `.env.staging`, `.env.production`).

> **Note:** This document complements the existing `JENKINS_ENV_SETUP.md` which focuses specifically on Jenkins integration. For Jenkins-specific environment setup, please refer to that document.

## The `generate-env.sh` Script

The `generate-env.sh` script is a bash script that generates the appropriate environment file based on the specified environment.

### Usage

```bash
./generate-env.sh [environment]
```

Where `[environment]` is one of:
- `development` (default if no environment is specified)
- `staging`
- `production`

### Examples

```bash
# Generate development environment file
./generate-env.sh development
# or simply
./generate-env.sh

# Generate staging environment file
./generate-env.sh staging

# Generate production environment file
./generate-env.sh production
```

### Environment Variables

The script creates environment files with the following variables:

#### Development Environment (`.env.development`)
```
VITE_API_BASE_URL=http://127.0.0.1:8000/api/
VITE_WS_URL=ws://localhost:8000/ws/ticket/
```

#### Staging Environment (`.env.staging`)
```
VITE_API_BASE_URL=http://internal-project.nexware-global.com:9019/api/
VITE_WS_URL=ws://localhost:8000/ws/ticket/
```

#### Production Environment (`.env.production`)
```
VITE_API_BASE_URL=https://ticket.nexware-global.com:9049/api/
VITE_WS_URL=wss://internal-project.nexware-global.com:8001/ws/ticket/
```

## How Environment Variables Are Used

The environment variables are used in the application through Vite's environment variable system. Vite exposes environment variables on the `import.meta.env` object.

### Accessing Environment Variables in Code

In the application code, the environment variables are accessed like this:

```javascript
// Example from src/api/apiconfig.ts
export const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8000/api/";
```

### Environment Variable Naming

All environment variables must be prefixed with `VITE_` to be exposed to the client-side code. This is a security feature of Vite to prevent accidentally exposing sensitive server-side environment variables.

### Environment Modes

Vite has built-in support for different modes:

- `development`: Used during development with `npm run dev`
- `staging`: Used for staging builds with `npm run build:staging`
- `production`: Used for production builds with `npm run build:prod`

When running the application in a specific mode, Vite will load the corresponding `.env.[mode]` file.

## Integration with npm Scripts

The script is integrated with npm scripts in `package.json` for easier usage:

```json
"scripts": {
  "generate-env:dev": "bash generate-env.sh development",
  "generate-env:staging": "bash generate-env.sh staging",
  "generate-env:prod": "bash generate-env.sh production"
}
```

You can run these scripts using npm:

```bash
# Generate development environment file
npm run generate-env:dev

# Generate staging environment file
npm run generate-env:staging

# Generate production environment file
npm run generate-env:prod
```

## Building with Environment Configurations

After generating the environment file, you can build the application for the specific environment:

```bash
# Build for development
npm run build

# Build for staging
npm run build:staging

# Build for production
npm run build:prod
```

These build commands will use the corresponding environment file to configure the application.

## CI/CD Integration

The environment setup is integrated with CI/CD pipelines to automate the build and deployment process.

### Jenkins Integration

The Jenkinsfile is configured to automatically generate the appropriate environment file based on the branch:
- `develop` branch: Uses staging configuration
- `main` branch: Uses production configuration

The Jenkins pipeline performs the following steps:
1. Checks out the code from the repository
2. Installs dependencies with `npm install --legacy-peer-deps`
3. Runs linting with `npm run lint`
4. Generates the environment file based on the branch:
   - For `develop` branch: `npm run generate-env:staging`
   - For `main` branch: `npm run generate-env:prod`
5. Builds the application:
   - For `develop` branch: `npm run build:staging`
   - For `main` branch: `npm run build:prod`
6. Archives and deploys the build to the appropriate server

For more details on Jenkins setup, see `JENKINS_ENV_SETUP.md` and `jenkins-webhook-config.md`.

### GitLab CI Integration

The `.gitlab-ci.yml` file is configured to automatically generate the appropriate environment file based on the branch:
- `develop` branch: Uses staging configuration
- `main` or `master` branch: Uses production configuration

The GitLab CI pipeline includes:
1. Installing dependencies with `npm install --legacy-peer-deps`
2. Creating the environment file directly in the pipeline:
   ```yaml
   # For staging (develop branch)
   - echo "VITE_API_BASE_URL=http://internal-project.nexware-global.com:9019/api/" > .env.staging
   - echo "VITE_WS_URL=ws://localhost:8000/ws/ticket/" >> .env.staging

   # For production (main/master branch)
   - echo "VITE_API_BASE_URL=https://ticket.nexware-global.com:9049/api/" > .env.production
   - echo "VITE_WS_URL=wss://internal-project.nexware-global.com:8001/ws/ticket/" >> .env.production
   ```
3. Building the application with the appropriate command
4. Deploying the build to the server

## Modifying Environment Variables

If you need to change the environment variables:

1. Edit the `generate-env.sh` script
2. Update the appropriate section for the environment you want to modify
3. Run the script to generate the updated environment file
4. Commit and push the changes

## Troubleshooting

If you encounter issues with the environment configuration, here are some common problems and their solutions:

### Environment File Not Being Generated

1. Check that the script has execute permissions:
   ```bash
   chmod +x generate-env.sh
   ```

2. Verify that you're running the script from the project root directory.

3. Check for any error messages when running the script.

### Environment Variables Not Being Applied

1. Ensure the environment file is being generated in the correct location (project root).

2. Verify that the environment variables are prefixed with `VITE_` (required for Vite to expose them to the client).

3. Check that you're using the correct build command for your target environment:
   - `npm run build:staging` for staging
   - `npm run build:prod` for production

4. Inspect the generated environment file to ensure it contains the expected values.

### API Connection Issues

1. Verify that the API URLs in the environment files are correct and accessible.

2. Check for CORS issues if the API is hosted on a different domain.

3. Ensure that the WebSocket URLs are correct for real-time features.

### Build Errors

1. Check the build logs for any errors related to environment variables.

2. Ensure that all required dependencies are installed:
   ```bash
   npm install --legacy-peer-deps
   ```

3. Verify that the TypeScript types are correctly defined for the environment variables.

### CI/CD Pipeline Issues

1. Check that the CI/CD pipeline is correctly generating the environment file.

2. Verify that the pipeline has the necessary permissions to create files.

3. Ensure that the build command is being executed after the environment file is generated.

4. Check the pipeline logs for any errors related to the environment setup.

## Best Practices for Environment Management

### Organizing Environment Variables

1. **Group Related Variables**: Keep related variables together in the environment files.

2. **Use Descriptive Names**: Use clear, descriptive names for environment variables to make their purpose obvious.

3. **Document Variables**: Add comments in the `generate-env.sh` script to explain the purpose of each variable.

4. **Consistent Naming**: Use a consistent naming convention for all environment variables.

### Version Control

1. **Don't Commit Environment Files**: Keep `.env.*` files in `.gitignore` to prevent accidental commits.

2. **Provide Examples**: Include an `.env.example` file in the repository with placeholder values.

3. **Document Changes**: When changing environment variables, update documentation and notify the team.

### Security Considerations

1. **No Secrets in Source Control**: Never commit sensitive information like API keys or passwords to the repository.

2. **Use Environment-Specific Secrets**: Use different credentials for development, staging, and production environments.

3. **Limit Access**: Restrict access to production environment variables to authorized personnel only.

4. **Validate Input**: Validate and sanitize any data derived from environment variables before using it in the application.

5. **Audit Regularly**: Regularly review environment variables for sensitive information and unnecessary variables.

## Security Considerations

- The environment files (`.env.*`) are included in `.gitignore` to prevent sensitive information from being committed to the repository
- The `generate-env.sh` script is included in the repository to ensure consistent environment configuration
- Sensitive information should not be stored in environment files that are committed to the repository
- For production deployments, consider using a secure secrets management system instead of plain text environment files

## Further Reading

- [Vite Environment Variables](https://vitejs.dev/guide/env-and-mode.html)
- [Vue.js Environment Variables](https://cli.vuejs.org/guide/mode-and-env.html)
- [The Twelve-Factor App: Config](https://12factor.net/config)
- [OWASP Environment Variable Security](https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-use-environment-variables-for-sensitive-information)
