import { createRouter, createWebHistory } from "vue-router";
import dashboard from "@/pages/dashboard.vue";
import AllTicket from "@/pages/TicketManagement/AllTicket.vue";
import CreateTicket from "@/pages/TicketManagement/createTicket.vue";
import EditTicket from "@/pages/TicketManagement/editTicket.vue";
import ManageUsers from "@/pages/User Management/manageUsers.vue";
import AddNewUser from "@/pages/User Management/addNewUser.vue";
import EditUser from "@/pages/User Management/editUser.vue";
import ViewUser from "@/pages/User Management/viewUser.vue";
import PendingAprovel from "@/pages/TicketManagement/pendingAprovel.vue";
import PendingEditTicket from "@/pages/TicketManagement/pendingEditTicket.vue";
import AssignTicket from "../pages/TicketManagement/assignticket.vue";
import Ticketlist from "@/pages/TicketManagement/ticketlist.vue";
import Ticketcategories from "@/pages/ItSupportConfig/ticketcategories.vue";
// import Subcategory from "@/pages/ItSupportConfig/subcategory.vue";
// import ConnectionManager from "@/components/ConnectionManager.vue";
// import Sockets from "@/pages/sockets.vue";
import Login from "@/pages/Auth/login.vue"; // Login Page
import Forgotpassword from "@/pages/Auth/forgotpassword.vue";
import resetpassword from "@/pages/Auth/resetpassword.vue";
// import jwtDecode from "jwt-decode";
import { jwtDecode } from "jwt-decode";
const isAuthenticated = (): boolean => {
  const token = localStorage.getItem("authToken");
  if (!token) return false;

  try {
    const decoded: any = jwtDecode(token);
    const currentTime = Math.floor(Date.now() / 10000); // Current time in seconds
    return decoded.exp > currentTime; // Token is valid if expiration is in the future
  } catch {
    return false; // Invalid token
  }
};
import ProjectMapping from "@/pages/User Management/projectMapping.vue";
import TechnicianPerformance from "@/pages/Report And Analytics/technicianPerformance.vue";
import TicketsAndReports from "@/pages/Report And Analytics/ticketsAndReports.vue";
import UserFeedback from "@/pages/Report And Analytics/userFeedback.vue";
import RoleAssignment from "@/pages/User Management/roleAssignment.vue";
import NotFound404 from "@/pages/NotFound404.vue"; // Import your 404 component
import sessionExpired from "@/pages/sessionExpired.vue"; 
import ChangePassword from "@/pages/Auth/changePassword.vue"

const router = createRouter({
  history: createWebHistory(""),

  routes: [
    // Login Route
    {
      path: "/login",
      name: "login",
      component: Login,
      meta: { requiresAuth: false }, // No authentication required
    },
    //Forgotpassword
    {
      path: "/forgotpassword",
      name: "forgotpassword",
      component: Forgotpassword,
      meta: { requiresAuth: false },
    },
    {
      path: "/resetpassword/:user_id/:token",
      name: "resetpassword",
      component: resetpassword,
      meta: { requiresAuth: false },
    },
    {
      path: "/changepassword",
      name: "change-password",
      component: ChangePassword,
      meta: { requiresAuth: true },
    },
    // Protected Routes
    {
      path: "/",
      name: "dashboard",
      component: dashboard,
      meta: { requiresAuth: true },
    },
    {
      path: "/all-tickets",
      name: "AllTicket",
      component: AllTicket,
      meta: { requiresAuth: true },
    },
    {
      path: "/create-tickets",
      name: "createTicket",
      component: CreateTicket,
      meta: { requiresAuth: true },
    },
   
    {
      path: "/all-tickets/view-tickets/:ticket_id/edit",
      name: "/TicketManagement/editTicket",
      component: EditTicket,
      props: true,
      meta: { requiresAuth: true },
    },
    {
      path: "/pending-approval",
      name: "pendingAprovel",
      component: PendingAprovel,
      meta: { requiresAuth: true },
    },
 
    {
      path: "/pending-approvel/view-tickets/:ticket_id/edit",
      name: "/TicketManagement/pendingEditTicket",
      component: PendingEditTicket,
      props: false,
      meta: { requiresAuth: true },
    },
    {
      path: "/assign-ticket",
      name: "assignTicket",
      component: AssignTicket,
      meta: { requiresAuth: true },
    },
   
 
    {
      path: "/ticket-list",
      name: "/TicketManagement/ticketlist",
      component: Ticketlist,
      props: false,
      meta: { requiresAuth: true },
    },
    {
      path: "/manage-users",
      name: "/User Management/manageUsers",
      component: ManageUsers,
      meta: { requiresAuth: true },
    },
    {
      path: "/add-new-user",
      name: "/User Management/addNewUser",
      component: AddNewUser,
      meta: { requiresAuth: true },
    },
    {
      path: "/edit-user:id",
      name: "/User Management/editUser",
      component: EditUser,
      props: false,
      meta: { requiresAuth: true },
    },
    {
      path: "/view-user/:id",
      name: "/User Management/viewUser",
      component: ViewUser,
      props: true,
      meta: { requiresAuth: true },
    },
    // {
    //   path: "/ConnectionManager",
    //   name: "connectionmanger",
    //   component: ConnectionManager,
    //   props: true,
    // },
    {
      path: "/ticketcategories",
      name: "ticketcategories",
      component: Ticketcategories,
      props: true,
      meta: { requiresAuth: true },
    },
    // {
    //   path: "/subcategory",
    //   name: "subcategory",
    //   component: Subcategory,
    //   props: true,
    // },
    // {
    //   path: "/sockets",
    //   name: "MyForm",
    //   component: Sockets,
    //   props: true,
    // },
    {
      path: "/role-assignement",
      name: "roleAssignement",
      component: RoleAssignment,
      meta: { requiresAuth: true },
    },
    {
      path: "/project-mapping",
      name: "projectMapping",
      component: ProjectMapping,
      meta: { requiresAuth: true },
    },
    {
      path: "/technicianPerformance",
      name: "technicianPerformance",
      component: TechnicianPerformance,
      meta: { requiresAuth: true },
    },
    {
      path: "/ticketsAndReports",
      name: "ticketsAndreports",
      component: TicketsAndReports,
      meta: { requiresAuth: true },
    },
    {
      path: "/userFeedback",
      name: "userFeedback",
      component: UserFeedback,
      meta: { requiresAuth: true },
    },

    //Not found page
    {
      path: '/404',
      name: 'NotFound',
      component: NotFound404 // your custom 404 page
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/404'
    },
    //session expired page
    {
      path: '/session-expired',
      name: 'SessionExpired',
      component: sessionExpired 
    }
  ],

});

// Global navigation guard
router.beforeEach((to, _, next) => {
  const loggedIn = isAuthenticated();

  if (to.meta.requiresAuth && !loggedIn) {
    // Redirect to login if not authenticated
    next({ path: "/login" });
  } else if (to.path === "/login" && loggedIn) {
    next({ path: "/" }); // Redirect to dashboard if already logged in
  } else if (to.path === "/forgotpassword" && loggedIn) {
    next({ path: "/" }); // Redirect to dashboard if already logged in
  } else {
    next(); // Proceed to the route
  }
});

export default router;
