/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/Auth/changePassword': RouteRecordInfo<'/Auth/changePassword', '/Auth/changePassword', Record<never, never>, Record<never, never>>,
    '/Auth/forgotpassword': RouteRecordInfo<'/Auth/forgotpassword', '/Auth/forgotpassword', Record<never, never>, Record<never, never>>,
    '/Auth/login': RouteRecordInfo<'/Auth/login', '/Auth/login', Record<never, never>, Record<never, never>>,
    '/Auth/resetpassword': RouteRecordInfo<'/Auth/resetpassword', '/Auth/resetpassword', Record<never, never>, Record<never, never>>,
    '/dashboard': RouteRecordInfo<'/dashboard', '/dashboard', Record<never, never>, Record<never, never>>,
    '/ItSupportConfig/subcategory': RouteRecordInfo<'/ItSupportConfig/subcategory', '/ItSupportConfig/subcategory', Record<never, never>, Record<never, never>>,
    '/ItSupportConfig/ticketcategories': RouteRecordInfo<'/ItSupportConfig/ticketcategories', '/ItSupportConfig/ticketcategories', Record<never, never>, Record<never, never>>,
    '/NotFound404': RouteRecordInfo<'/NotFound404', '/NotFound404', Record<never, never>, Record<never, never>>,
    '/Report And Analytics/technicianPerformance': RouteRecordInfo<'/Report And Analytics/technicianPerformance', '/Report And Analytics/technicianPerformance', Record<never, never>, Record<never, never>>,
    '/Report And Analytics/ticketsAndReports': RouteRecordInfo<'/Report And Analytics/ticketsAndReports', '/Report And Analytics/ticketsAndReports', Record<never, never>, Record<never, never>>,
    '/Report And Analytics/userFeedback': RouteRecordInfo<'/Report And Analytics/userFeedback', '/Report And Analytics/userFeedback', Record<never, never>, Record<never, never>>,
    '/sessionExpired': RouteRecordInfo<'/sessionExpired', '/sessionExpired', Record<never, never>, Record<never, never>>,
    '/sockets': RouteRecordInfo<'/sockets', '/sockets', Record<never, never>, Record<never, never>>,
    '/TicketManagement/AllTicket': RouteRecordInfo<'/TicketManagement/AllTicket', '/TicketManagement/AllTicket', Record<never, never>, Record<never, never>>,
    '/TicketManagement/assignticket': RouteRecordInfo<'/TicketManagement/assignticket', '/TicketManagement/assignticket', Record<never, never>, Record<never, never>>,
    '/TicketManagement/createTicket': RouteRecordInfo<'/TicketManagement/createTicket', '/TicketManagement/createTicket', Record<never, never>, Record<never, never>>,
    '/TicketManagement/editTicket': RouteRecordInfo<'/TicketManagement/editTicket', '/TicketManagement/editTicket', Record<never, never>, Record<never, never>>,
    '/TicketManagement/pendingAprovel': RouteRecordInfo<'/TicketManagement/pendingAprovel', '/TicketManagement/pendingAprovel', Record<never, never>, Record<never, never>>,
    '/TicketManagement/pendingEditTicket': RouteRecordInfo<'/TicketManagement/pendingEditTicket', '/TicketManagement/pendingEditTicket', Record<never, never>, Record<never, never>>,
    '/TicketManagement/ticketlist': RouteRecordInfo<'/TicketManagement/ticketlist', '/TicketManagement/ticketlist', Record<never, never>, Record<never, never>>,
    '/User Management/addNewUser': RouteRecordInfo<'/User Management/addNewUser', '/User Management/addNewUser', Record<never, never>, Record<never, never>>,
    '/User Management/editUser': RouteRecordInfo<'/User Management/editUser', '/User Management/editUser', Record<never, never>, Record<never, never>>,
    '/User Management/manageUsers': RouteRecordInfo<'/User Management/manageUsers', '/User Management/manageUsers', Record<never, never>, Record<never, never>>,
    '/User Management/projectMapping': RouteRecordInfo<'/User Management/projectMapping', '/User Management/projectMapping', Record<never, never>, Record<never, never>>,
    '/User Management/roleAssignment': RouteRecordInfo<'/User Management/roleAssignment', '/User Management/roleAssignment', Record<never, never>, Record<never, never>>,
    '/User Management/viewUser': RouteRecordInfo<'/User Management/viewUser', '/User Management/viewUser', Record<never, never>, Record<never, never>>,
  }
}
