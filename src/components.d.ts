/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppFooter: typeof import('./components/AppFooter.vue')['default']
    ApprovelDialog: typeof import('./components/ApprovelDialog.vue')['default']
    Batch: typeof import('./components/Batch.vue')['default']
    Button: typeof import('./components/Button.vue')['default']
    Card: typeof import('./components/Card.vue')['default']
    ChartComponent: typeof import('./components/ChartComponent.vue')['default']
    Chatbox: typeof import('./components/Chatbox.vue')['default']
    ConnectionManager: typeof import('./components/ConnectionManager.vue')['default']
    ConnectionState: typeof import('./components/ConnectionState.vue')['default']
    DashboardCard: typeof import('./components/DashboardCard.vue')['default']
    DropdownMenu: typeof import('./components/DropdownMenu.vue')['default']
    Filter: typeof import('./components/Filter.vue')['default']
    HelloWorld: typeof import('./components/HelloWorld.vue')['default']
    InfiniteScroll: typeof import('./components/InfiniteScroll.vue')['default']
    MyForm: typeof import('./components/MyForm.vue')['default']
    Nodata: typeof import('./components/Nodata.vue')['default']
    Pagination: typeof import('./components/Pagination.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TextEditor: typeof import('./components/TextEditor.vue')['default']
    TicketFilterSwitch: typeof import('./components/TicketFilterSwitch.vue')['default']
  }
}
