:root {
  body {
    background-color: #f0f0f0;
    font-family: "<PERSON>";
    color: #2e2e2e;
    font-size: 16px;
    font-weight: normal;
    line-height: normal;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  .h1,
  .h2,
  .h3,
  .h4,
  .h5,
  .h6 {
    font-family: "<PERSON>";
    color: inherit;
  }
  // Page styles begin here
  .v-navigation-drawer--active {
    & ~ .app-page-layout {
      .page-wrapper {
        margin-left: 0;
        width: calc(100% + 10px);
        &.page-change-password {
          text-align: center;
        }
      }
    }
  }
  .app-layout-root {
    padding: 0 20px 0;
    background-color: transparent;
  }
  .app-page-layout {
    margin: 0;
    height: 100%;
    .page-wrapper {
      display: block;
      background-color: #fff;
      padding: 20px;
      width: calc(100% + 20px);
      height: calc(100vh - 99px);
      overflow: auto;
      border-radius: 5px;
      margin-left: -10px;
      margin-top: 20px;
      .page-header {
        display: block;
        position: fixed;
        width: calc(80% + 33px);
        background: #fff;
        z-index: 1;
        padding: 0 0 10px 0;
        top: 104px;
        & > .v-row {
          & > .v-col {
            &:first-child {
              min-width: 58vw;
              max-width: 58vw;
            }
          }
        }
        &::before {
          content: ("");
          height: 15px;
          height: 20px;
          background: #ffffff;
          position: absolute;
          width: 100%;
          left: 0;
          top: -20px;
          z-index: 1;
        }
        .page-title {
          font-size: 26px;
          font-weight: 700;
          margin-bottom: 0;
          color: #333;
          text-align: left;
          text-transform: capitalize;
          max-width: 70%;
          .title-badge {
            background-color: #f3fff8;
            border: 1px solid #bfe9d1;
            color: #2dcc70;
            font-size: 12px;
            font-weight: normal;
            line-height: normal;
            display: inline-flex;
            padding: 4px 10px;
            border-radius: 30px;
            white-space: nowrap;
          }
        }
        .v-breadcrumbs {
          background: none;
          padding: 0;
          margin: 0;
          font-size: 0.813rem;
          color: #575757;
          .v-breadcrumbs-item {
            a {
              color: #575757;
              &:hover {
                color: #026bb1;
              }
            }
            &.active {
              color: #c1c1c1;
            }
            & + .breadcrumb-item {
              padding-left: 0.25rem;
              &::before {
                color: #e1e1e1;
                padding-right: 0.25rem;
              }
            }
            &.v-breadcrumbs-item--disabled {
              opacity: 1;
              color: #a3a3a3;
            }
          }
          .v-breadcrumbs-divider {
            padding: 0 3px;
            opacity: 0.4;
          }
        }
        .page-action {
          position: fixed;
          right: 20px;
          z-index: 1;
          .v-btn {
            i {
              &.mdi-chevron-left {
                --v-icon-size-multiplier: 1;
              }
            }
          }
        }
      }
      .upload-section {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }
      .custom-list-group {
        margin: 0;
        padding: 0;
        list-style: none;
        border: 1px solid #d8d8d8;
        border-radius: 5px;
        min-width: 420px;
        & > li {
          padding: 25px 40px;
          border-bottom: 1px solid #d8d8d8;
          &:last-child {
            border: 0;
          }
          .user-info {
            .title,
            .user-value {
              display: block;
            }
          }
        }
        .v-chip {
          &.user-active-badge {
            background-color: #fff;
            color: #2dcc70;
            border: 1px solid #2dcc70;
            border-radius: 30px;
            padding: 5px 10px;
            height: auto;
            min-width: 100px;
            display: inline-block;
            text-align: center;
            font-weight: 600;
            .v-chip__underlay {
              background: none;
            }
          }
        }
        &.default-list-group {
          & > li {
            padding: 20px;
            .label-name {
              color: #8c8c8c;
              font-size: 95%;
            }
            .label-value {
              font-weight: 600;
              text-transform: capitalize;
            }
          }
        }
      }
      .ticket-desc-primary {
        .custom-list-group {
          margin: 0;
          padding: 0;
          list-style: none;
          border: 1px solid #d8d8d8;
          border-radius: 5px;
          & > li {
            padding: 20px;
            border-bottom: 1px solid #d8d8d8;
            position: relative;
            &:last-child {
              border: 0;
            }
          }
          &.ticket-desc-approve-info {
            border: 1px dashed #2fcc70;
            background: #ffffff;
            margin-top: -35px;
            box-shadow: inset 0 0 30px 0 rgba(47, 204, 112, 0.25);
            & > li {
              border-color: #2fcc70;
              border-bottom-style: dashed;
              padding: 30px 20px 20px;
              .label-name {
                color: #58a879;
              }
              .label-value {
                color: #0a9343;
              }
              & + li {
                padding-top: 20px;
              }
            }
          }
          &.ticket-desc-cancel-info {
            border: 1px dashed #e02b34;
            background: #ffffff;
            margin-top: -35px;
            box-shadow: inset 0 0 30px 0 rgba(224, 43, 52, 0.15);
            & > li {
              border-color: #e02b34;
              border-bottom-style: dashed;
              padding: 30px 20px 20px;
              .label-name {
                color: #cf8286;
              }
              .label-value {
                color: #be3f46;
              }
              & + li {
                padding-top: 20px;
              }
            }
          }
        }
      }
      .ticket-watchers-list {
        background: #026bb1;
        font-size: 13px;
        padding: 6px 12px;
        border-radius: 0 0 5px 5px;
        display: inline-flex;
        align-items: center;
        color: #fff;
        position: absolute;
        top: -7px;
        right: 15px;
        gap: 8px;
        &::after,
        &::before {
          content: "";
          position: absolute;
          right: -7px;
          top: 0;
          width: 0px;
          height: 0px;
          border-bottom: 7px solid #004573;
          border-left: 7px solid transparent;
          border-right: 0 solid transparent;
        }
        &::before {
          right: auto;
          left: -7px;
        }
        &::after {
          border-left: 0 solid transparent;
          border-right: 7px solid transparent;
        }
        h6 {
          font-weight: 700;
          text-transform: uppercase;
          font-size: 12px;
          color: #fff;
          margin: 0;
          padding: 0;
        }
        .ticket-watcher-content {
          display: flex;
          gap: 10px;
          .watchers-edit-btn{
            height: 25px;
            width: 25px;
            font-size: 10px;
            border: 0;
            background: #ffffff;
            color: #016bb1;
            &:hover{
              background: #003152;
              color: #fff;
            }
          }
        }
      }

      /* Profile Picture Header */
      .profile-pic-header {
        background-color: #2e2e2e;
        color: #fff;
        padding: 6px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
      }
      /* Labels */
      .profile-label {
        font-weight: bold;
        font-size: 16px;
      }
      .optional-label {
        font-size: 12px;
        color: #8e8e8e;
      }
      .profile-image-container {
        padding: 20px;
        border: 1px solid #2e2e2e;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
      }
      .profile-image {
        padding: 5px;
        border: 1px solid #d9d9d9;
        // width: 170px;
        // height: 170px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        .v-img {
          background-color: #d9d9d9;
          svg {
            fill: rgb(77, 10, 235);
          }
        }
      }
    }
  }
  .dialog-custom-list-group {
    list-style: none;
    margin: 0;
    padding: 20px 0;
    & > li {
      display: block;
      padding: 20px 20px 0;
      border-top: 1px solid #d8d8d8;
      &:first-child {
        padding-top: 0;
        border: 0;
      }
    }
    &.role-mgmt-list {
      .custom-checkbox {
        &.v-input--density-default {
          --v-input-control-height: auto;
          --v-input-padding-top: auto;
        }
        div[id*="-messages"] {
          display: none;
        }
      }
      .v-input--disabled .v-input__details,
      .v-input--disabled .v-input__prepend,
      .v-input--disabled .v-input__append {
        display: none;
      }
      & > li {
        & > .v-row {
          & > .v-col {
            padding-top: 0;
          }
        }
        .module-name {
          h6 {
            margin: 0;
          }
        }
      }
      .v-select {
        &.v-input--disabled {
          margin-bottom: 20px;
        }
      }
    }
  }
  .filter-wrapper {
    background-color: #f3f3f3;
    padding: 10px 22px;
    border-radius: 5px;
    margin: 0 0 15px;
    position: fixed;
    top: 165px;
    width: calc(100% - 332px);
    z-index: 1;
    .row {
      div[class*="col-"] {
        padding: 0 6px;
      }
    }
    input,
    .form-input,
    select {
      padding: 10px;
    }
    .btn-toolbar {
      .v-btn {
        width: 37px;
        height: 37px;
        font-size: 13px;
      }
    }
  }
  .filter-wrapper {
    & + .page-content-wrapper {
      top: 133px;
    }
  }
  .page-content-wrapper {
    position: relative;
    top: 66px;
    padding: 0;
    .app-page {
      display: block;
      .page-header {
      }
      .page-actions {
        .btn {
          padding: 0.6rem 0.85rem;
        }
      }
      .chart-wrapper {
        padding: 1rem 0;
        display: block;
        text-align: center;
        img {
          max-width: 100%;
        }
        .status-progressbar {
          display: block;
          margin: 0;
          padding: 0;
          list-style: none;
          & > li {
            label {
              display: flex;
              justify-content: space-between;
              color: #808080;
              font-size: 0.875rem;
              margin-bottom: 0.25rem;
            }
            .progress {
              border-radius: 0;
            }
            & + li {
              margin-top: 1.875rem;
            }
          }
        }
      }
      .table-wrapper {
        display: block;
        .more-info-col {
          display: flex;
          align-items: center;
          & > label {
            margin: 0 0.5rem 0 0;
            white-space: nowrap;
            width: 368px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          & > .btn {
            width: 26px;
            height: 26px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            border-color: #d7d7d7;
            color: #d7d7d7;
            border-radius: 100%;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.03);
            &:hover,
            &[aria-describedby] {
              background-color: #2e2e2e;
              border-color: #2e2e2e;
              color: #fff;
              box-shadow: none;
            }
          }
        }
      }
      .quick-confirmation {
        .popover {
          .popover-body {
            h6 {
              font-size: 0.938rem;
              margin-bottom: 0;
            }
            small {
              font-size: 0.75rem;
              color: #c9c9c9;
              display: block;
            }
            .btn-toolbar {
              margin-top: 0.75rem;
              & > .btn {
                font-size: 0.7rem;
                font-weight: normal;
                padding: 0.25rem 0.5rem;
                &.btn-danger {
                  font-weight: 500;
                }
                &.btn-light {
                  color: #7d7d7d;
                  background-color: #f1f1f1;
                  &:hover,
                  &:active {
                    background-color: #fff;
                  }
                }
              }
            }
          }
        }
      }
      .section-title {
        font-family: "Barlow";
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0 0 0.625rem 0;
      }
    }
  }
  .card-list-wrapper {
    .app-primary-card {
      box-shadow: none;
      margin: 0;
      padding: 10px;
      & + .app-primary-card {
        margin-top: 10px;
      }
      .btn-toolbar {
        .v-btn {
          width: 30px;
          height: 30px;
        }
      }
    }
    .name-section {
      display: flex;
      gap: 5px;
    }
    .user-data-header {
      color: #8c8c8c;
    }
    .user-data-details {
      font-size: 14px;
      color: #2e2e2e;
      font-weight: 500;
    }
    .user-data-details p:first-child {
      color: #8c8c8c;
      text-align: left;
      margin-bottom: 4px;
    }
    .user-data-btn {
      display: flex;
      justify-content: center;
      gap: 10px;
    }
    .user-projects-wrapper{
      display: flex;
      gap: 5px;
      margin: 0;
      padding: 0;
      list-style: none;
      & > li{
        position: relative;
        &.more-projects-root{
          .v-btn{
            display: inline-flex;
            width: 19px;
            height: 19px;
            background: #016bb1;
            align-items: center;
            justify-content: center;
            border-radius: 100%;
            min-width: auto;
            color: #fff;
            font-size: 11px;
            font-weight: 700;
          }
        }
      }
    }

    /* Profile-Pic */
    .initials {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: #fff;
      font-weight: 700;
      font-size: 95%;
      border-radius: 50%;
    }
    .name-section {
      .v-chip {
        border: 0;
        font-size: 12px;
        font-weight: 600;
        height: auto;
        padding: 2px 10px 4px;
        line-height: normal;
        margin-top: -1px;
        &.user-active-badge {
          background-color: #e2fced;
          color: #1cb65d;
        }
        &.user-inactive-badge {
          background-color: #e8e8e8;
          color: #aaaaaa;
        }
      }
    }
  }
  .current-watchers-list{
    display: block;
    margin: 0 0 30px 0;
    ul{
      margin: 0;
      padding: 0;
      list-style: none;
      border-radius: 6px;
      border: 1px solid #ddd;
      & > li{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px;
        border-top: 1px solid #ddd;
        &:first-child{
          border-top: 0;
        }
      }
    }
  }
  .user-main-info {
    display: block;
    label {
      display: block;
      color: #808080;
      font-size: 0.938rem;
      margin-bottom: 0.25rem;
    }
    strong {
      font-family: "SF Pro Display";
      font-size: 1.063rem;
      font-weight: 600;
    }
  }
  .ticket-current-info {
    border: 1px solid #d8effd;
    color: #016bb1;
    padding: 20px;
    border-radius: 5px;
    background: rgb(239, 249, 255);
    background: linear-gradient(
      90deg,
      rgba(239, 249, 255, 1) 25%,
      rgba(249, 253, 255, 1) 50%,
      rgba(239, 249, 255, 1) 75%
    );
    .ticket-data-label {
      display: block;
      opacity: 0.85;
    }
    .ticket-data-value {
      display: block;
      font-weight: 700;
      text-transform: capitalize;
    }
  }
  .fixed-right-sidebar {
    .page-inner-layout {
      display: flex;
      margin-top: 20px;
    }
    .page-left-column {
      width: calc(100% - 280px);
    }
    .page-right-column {
      position: fixed;
      width: 250px;
      right: 30px;
      height: calc(100vh - 213px);
      overflow-y: auto;
      background: #ffffff;
      border-radius: 5px;
      box-shadow: inset 0 0 15px 0 rgba(0, 0, 0, 0.15);
      border: 1px solid #dcdcdc;
    }
  }
  .edit-ticket-fixed-card {
    box-shadow: none;
    border: 0;
    background: none;
  }
  .chat-container {
    .chat-box {
      .chat-single-container {
        // height: 400px;
        // overflow-y: scroll;
        // position: fixed;
        & > ul {
          margin: 0;
          padding: 0;
          list-style: none;
          & > li {
            display: inline-flex;
            gap: 15px;
            border: 1px solid #ededed;
            background: #fff;
            padding: 15px 15px 15px 44px;
            border-radius: 5px;
            box-shadow: inset 0 0 15px 0 rgba(0, 0, 0, 0.05);
            position: relative;
            margin-left: 25px;
            // width: calc(100% - 40px);
            width: auto;
            .user-comment-picture {
              position: absolute;
              left: -25px;
              top: 10px;
              .user-dp {
                background: #8f15bd;
                border-radius: 100%;
                color: #fff;
                display: flex;
                width: 50px;
                height: 50px;
                align-items: center;
                justify-content: center;
                font-weight: 900;
                font-size: 20px;
                text-transform: uppercase;
              }
            }
            .user-comment-wrapper {
              width: 100%;
              h6 {
                font-size: 15px;
                font-weight: 600;
              }
              .user-comment-misc-info {
                margin: 0;
                padding: 0;
                list-style: none;
                display: flex;
                align-items: center;
                gap: 15px;
                font-size: 12px;
                & > li {
                  display: inline-flex;
                  align-items: center;
                  i {
                    font-size: 90%;
                    padding-right: 4px;
                  }
                }
              }
              .user-comment-description {
                p {
                  margin: 0;
                  padding: 0;
                  text-align: left;
                  opacity: 0.85;
                }
              }
            }
            & + li {
              margin-top: 20px;
            }
            &.received-message {
              margin-left: 0;
              padding: 15px 44px 15px 15px;
              margin-right: 25px;
              .user-comment-picture {
                left: auto;
                right: -25px;
                .user-dp {
                  background-color: #ea5e5f;
                }
              }
              .user-comment-wrapper {
                .user-comment-info {
                  flex-direction: row-reverse;
                }
                .user-comment-description {
                  text-align: right;
                  justify-content: flex-end;
                  p {
                    text-align: left;
                    color: black;
                  }
                }
              }
            }
          }
        }
      }
      .chat-form-wrapper {
        margin-top: 15px;
        .form-group {
          & > .form-label {
            font-size: 15px;
            display: block;
            font-weight: 600;
            margin: 0 0 3px 0;
            small {
              font-weight: 400;
            }
          }
          .form-input {
            width: 100%;
            height: 90px;
            border: 1px solid #d8d8d8;
            padding: 15px;
            border-radius: 4px;
          }
        }
      }
    }
  }
  .v-timeline {
    .v-timeline-item {
      .v-timeline-item__body {
        padding-inline-start: 0 !important;
        padding-block-start: 0 !important;
        .v-card {
          margin: 0 0 0 10px;
          border: 1px solid #d8d8d8;
          padding: 15px 23px 20px;
          border-radius: 4px;
          box-shadow: inset 0 0 15px 0 rgba(0, 0, 0, 0.15);
          .v-card-title {
            letter-spacing: 0;
            padding: 0;
          }
          .v-card-subtitle {
            padding: 0;
          }
          .timeline-card-text {
            padding: 0;
            min-height: auto;
            border-radius: 0;
            border: 0;
            box-shadow: none;
            strong {
              font-weight: 600;
            }
          }
        }
      }
    }
  }
  .v-timeline .v-timeline-divider__dot {
    background: #fff;
  }

  /* Widgets - Stats */
  .widget-stats-wrapper {
    display: block;
    padding: 0 0 0 15px;
    .widget-stats {
      border-radius: 10px;
      display: flex;
      align-items: center;
      padding: 12px;
      position: relative;
      min-width: 164px;
      height: 100%;
      background-color: #e9f6ff;
      cursor: pointer;
      transition: background-color 0.5s ease;
      .widget-stats-icon {
        margin-left: -30px;
        border: 3px solid #fff;
        background-color: #e9f6ff;
        color: #fff;
        font-size: 21px;
        width: 48px;
        height: 48px;
      }
      .widget-stats-info {
        padding-left: 12px;
        h6 {
          margin: 0;
        }
        h3 {
          margin: 0;
          font-weight: bold;
          font-size: 32px;
        }
      }
      &.widget-stats-tickets {
        background-color: #fcecff;
        color: #a131b2;
        .widget-stats-icon {
          background-color: #a131b2;
        }
        &:hover {
          background-color: #a131b2;
          color: #fff;
        }
      }
      &.widget-stats-open {
        background-color: #e7f2f3;
        color: #199cc7;
        .widget-stats-icon {
          background-color: #199cc7;
        }
        &:hover {
          background-color: #199cc7;
          color: #fff;
        }
      }
      &.widget-stats-unassigned {
        background-color: #f0f3fc;
        color: #1e49d3;
        .widget-stats-icon {
          background-color: #1e49d3;
        }
        &:hover {
          background-color: #1e49d3;
          color: #fff;
        }
      }
      &.widget-stats-pending {
        background-color: #fff5ea;
        color: #fb8903;
        .widget-stats-icon {
          background-color: #fb8903;
        }
        &:hover {
          background-color: #fb8903;
          color: #fff;
        }
      }
      &.widget-stats-solved {
        background-color: #e5fdea;
        color: #28a745;
        .widget-stats-icon {
          background-color: #28a745;
        }
        &:hover {
          background-color: #28a745;
          color: #fff;
        }
      }
      &.widget-stats-closed {
        background-color: #ffeced;
        color: #ef4d56;
        .widget-stats-icon {
          background-color: #ef4d56;
        }
        &:hover {
          background-color: #ef4d56;
          color: #fff;
        }
      }
      &.widget-stats-approval {
        background-color: #e6fcff;
        color: #00a7be;
        .widget-stats-icon {
          background-color: #00a7be;
        }
        &:hover {
          background-color: #00a7be;
          color: #fff;
        }
      }
      &.widget-stats-response {
        background-color: #f1f5df;
        color: #97aa40;
        .widget-stats-icon {
          background-color: #97aa40;
        }
        &:hover {
          background-color: #97aa40;
          color: #fff;
        }
      }
      &.widget-stats-observation {
        background-color: #efefef;
        color: #797979;
        .widget-stats-icon {
          background-color: #797979;
        }
        &:hover {
          background-color: #797979;
          color: #fff;
        }
      }
      &.widget-stats-pending-approval {
        background-color: #f3e8df;
        color: #e76a0a;
        .widget-stats-icon {
          background-color: #e76a0a;
        }
        &:hover {
          background-color: #e76a0a;
          color: #fff;
        }
      }
      .widget-more-info {
        position: absolute;
        right: 10px;
        bottom: 10px;
        opacity: 0.25;
        transition: all 1s;
      }
      &:hover {
        .widget-more-info {
          opacity: 0.6;
          transition: all 0.5s;
          transform: translateY(-10px);
          bottom: 18px;
        }
      }
      &.widget-stats-my-ticket {
        background-color: #e1f5fe !important;
        color: #0277bd;

        .widget-stats-icon {
          background-color: #0277bd !important;
        }

        .widget-stats-info h3 {
          // color: #0277bd !important;
        }
        &:hover {
          background-color: #0277bd !important;
          color: #fff !important;
        }
      }
      &.widget-stats-team-ticket {
        background-color: #e3f3e9;
        color: #2cbe6a;

        .widget-stats-icon {
          background-color: #2cbe6a;
        }

        .widget-stats-info h3 {
          // color: #fff !important;
        }
        &:hover {
          background-color: #2cbe6a;
          color: #fff;
        }
      }
      &.widget-stats-cancel-tickets {
        background-color: #ffe7f0;
        color: #e05186;

        .widget-stats-icon {
          background-color: #e05186;
        }

        .widget-stats-info h3 {
          // color: #fff !important;
        }
        &:hover {
          background-color: #e05186;
          color: #fff;
        }
      }
    }
    .text-purple {
      color: #9c27b0;
    }
    .text-blue {
      color: #2196f3;
    }
    .text-orange {
      color: #fb8903;
    }
    .text-green {
      color: #28a745;
    }
    .text-red {
      color: #ef4d56;
    }
  }

  /* Widgets - CHarts */
  .widget-charts-wrapper,
  .widget-section-wrapper {
    .chart-container,
    .widget-card {
      border: 1px solid #d8d8d8;
      box-shadow: inset 0 0 10px 0 rgba(0, 0, 0, 0.05);
      padding: 15px;
      border-radius: 5px;
      .chart-header,
      .widget-header {
        display: flex;
        justify-content: space-between;
      }
      h3,
      .chart-title,
      .widget-title {
        font-size: 18px;
        font-weight: 700;
        margin: 0;
      }
      .widget-actions {
        display: flex;
        align-items: center;
        .v-btn {
          padding: 7px 10px;
          font-size: 14px;
          height: auto;
        }
      }
      .chart-canvas-wrapper {
        min-height: 260px;
        margin-top: 15px;
        padding: 10px;
      }

      /* Dropdown */
      select {
        padding: 6px 10px;
        font-size: 14px;
        border: 1px solid #d8d8d8;
        border-radius: 4px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.15);
        font-weight: 500;
        color: #333333;
        outline: none;
      }
    }
  }
}
