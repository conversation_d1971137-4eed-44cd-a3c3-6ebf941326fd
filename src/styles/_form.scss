:root {
    input, select, textarea{
        outline: 0;
    }
    .form-input{
        outline: 0;
        &:focus{
            border-color: none;
        }
    }
    .custom-form-input{
        font-size: 15px;
        border: 1px solid #ccc;
        padding: 15px;
        border-radius: 4px;
        box-sizing: border-box;
        background-color: #fff;
        width: 100%;
    }
    .v-input__details{
        display: none;
    }
    .v-input--error{
        .v-input__details{
            display: block;
            padding-inline: 0;
        } 
    }
    .custom-form-field-wrapper{
        display: block;
        & > label{
            font-weight: 600;
        }
        input{
            border: 1px solid #ababab;
            width: 100%;
            padding: 12px;
            border-radius: 4px;
        }
    }
    .custom-due-date-field{
        .v-field--active
        {
            input{
            display: block;
            }
        }
    }
    select{
        &.placeholder{
            background: #fff;
            opacity: 1;
        }
    }
    .create-ticket-title-field{
        .v-counter{
            background: #b00020;
            padding: 3px 10px;
            border-radius: 10px;
            color: #fff !important;
            margin: 0px 0px 0px 6px;
        }
        .v-input--error {
            .v-input__details{
                display: inline-flex;
                padding-inline: 0;
                justify-content: flex-start;
                align-items: center;
            }
        }
    }
}
