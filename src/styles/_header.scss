:root {
  .v-navigation-drawer--active{
    display: block;
    &+.v-app-bar{
        &.app-header-wrapper{
        left: 276px !important;
        width: calc(100% - 286px) !important;
        i.app-sidebar-action-collapse{
          display: inline-block;
        }
        i.app-sidebar-action-open{
          display: none;
        }
      }
    }
  }
  .v-app-bar{
    &.app-header-wrapper{
      background: #fff;
      box-shadow: none !important;
      z-index: 3;
      width: calc(100% - 20px) !important;
      padding: 0 15px;
      margin: 0;
      top: 10px !important;
      left: 10px !important;
      i[class*="app-sidebar-action"]{
        color: #E1E1E1;
        font-size: 38px;
        display: none;
      }
      i.app-sidebar-action-open{
        display: inline-block;
      }
    }
  }
  .app-header-wrapper {
    .global-search {
      min-width: 420px;
      .v-input{
        background: #F8F8F8;
        border: 1px solid transparent;
        border-radius: 5px;
        transition: 0.5s;
        .v-input__control{
          .v-field{
            .v-field__input{
              min-height: 44px;
            }
            .v-field__outline{
              .v-field__outline__start, .v-field__outline__end{
                border: 0;
              }
            }
          }
        }
        &:focus, &:hover{
          border-color: #dedede;
          background-color: #fff;
        }
      }
      .v-input__details{
        display: none;
      }
      .input-group {
        .form-control {
          border-radius: 0.25rem;
          background-color: #f8f8f8;
          border-color: #f8f8f8;
          min-height: 44px;
          &:focus,
          &:hover {
            background-color: #fff;
            border-color: #70a2ff;
          }
        }
        .input-group-append {
          position: absolute;
          top: 0;
          right: 0;
          height: 100%;
          z-index: 9;
          display: flex;
          align-items: center;
          margin: 0;
          .btn {
            &:hover {
              color: #005aff;
            }
            &:focus {
              box-shadow: none;
              border: 0;
            }
          }
        }
        &:focus,
        &:hover {
          .form-control {
            background-color: #fff;
            border-color: #70a2ff;
          }
        }
      }
    }
    /* Notify Panel */
    .notify-panel{
      .v-btn{
        min-width: auto;
        padding: 7px;
        min-height: auto;
        background: #E9F6FF;
        color: #026BB1;
        height: auto;
        border-radius: 6px;
        .v-btn__content{
          font-size: 16px;
          .v-icon{
            --v-icon-size-multiplier: 1.05;
          }
          .v-badge{
            .v-badge__wrapper{
              .v-badge__badge{
                border: 2px solid #fff;
                font-size: 11px;
                letter-spacing: 0;
                top: calc(0% - 17px);
                left: calc(100% - 9px);
                width: 23px;
                height: 23px;
                border-radius: 100%;
                display: flex;
                align-items: center;
                background-color: #026BB1 !important;
              }
            }
          }
        }
      }
    }
    /* User Panel */
    .user-panel{
      .v-list{
        padding: 0;
        .v-list-item{
          padding-inline: 0;
          gap: 0;
          .v-list-item__prepend{
            .v-avatar{
              background: #EB648B;
              margin-right: 8px;
              .user-initials{

              }
            }
            .user-login-info{
              label{
                color: #8C8B90;
                font-weight: normal;
                font-size: 14px;
                display: block;
              }
              strong{
                font-weight: 600;
              }
            }
            .v-btn{
              background: none;
              color: #c1c1c1;
              .v-btn__content{
                font-size: 16px;
              }
              &:hover, &:focus{
                color: #2e2e2e;
              }
            }
            .v-list-item__spacer{
              display: none;
            }
          }
        }
      }
    }
    .user-actions {
      & > ul {
        display: flex;
        align-items: center;
        margin: 0;
        padding: 0;
        list-style: none;
        & > li {
          display: inline-block;
          &.user-dropdown,
          &.user-notification {
            .dropdown {
              .dropdown-toggle {
                background: none;
                border: 0;
                padding: 0;
                &:focus {
                  border: 0;
                  box-shadow: none;
                }
                .user-info {
                  display: flex;
                  align-items: center;
                  figure {
                    background-color: #efefef;
                    display: inline-flex;
                    align-items: center;
                    width: 38px;
                    height: 38px;
                    border-radius: 100%;
                    overflow: hidden;
                    margin: 0;
                    i {
                      color: #6c6c6c;
                      font-size: 38px;
                      opacity: 0.2;
                      margin-bottom: -9px;
                      margin-left: 2px;
                    }
                  }
                  label {
                    cursor: pointer;
                    font-family: "Archivo";
                    margin: 0;
                    color: #2e2e2e;
                    text-align: left;
                    font-size: 0.85rem;
                    line-height: 1rem;
                    padding: 0 1.5rem 0 0.5rem;
                    position: relative;
                    span {
                      font-weight: 200;
                      color: #8c8b90;
                      display: block;
                    }
                    strong {
                      font-weight: 500;
                    }
                    &::after {
                      font-family: "Font Awesome 6 Free";
                      content: "\f142";
                      color: #e1e1e1;
                      display: flex;
                      align-items: center;
                      height: 100%;
                      position: absolute;
                      font-size: 1.5rem;
                      right: 0;
                      top: 0;
                      font-weight: 900;
                    }
                  }
                }
              }
            }
          }
          &.user-notification {
            .dropdown {
              &.show {
                .dropdown-toggle {
                  background-color: #005aff;
                  color: #fff;
                }
              }
              .dropdown-toggle {
                background-color: #f0f5ff;
                border-radius: 0.625rem;
                color: #005aff;
                padding: 0.35rem 0.7rem;
                line-height: normal;
                font-size: 1.25rem;
                &:hover {
                  background-color: #005aff;
                  color: #fff;
                }
                &::after {
                  display: none;
                }
                .badge {
                  font-family: "SF Pro Display";
                  font-size: 0.7rem;
                  font-weight: normal;
                  position: absolute;
                  top: -10px;
                  right: -10px;
                  width: 22px;
                  background-color: #ef4d56;
                  color: #fff;
                  display: flex;
                  height: 22px;
                  border-radius: 100%;
                  align-items: center;
                  justify-content: center;
                  &::before {
                    content: "";
                    position: absolute;
                    top: -1px;
                    left: -1px;
                    width: 24px;
                    height: 24px;
                    border: 2px solid #fff;
                    border-radius: 100%;
                  }
                }
              }
            }
          }
        }
      }
    }
    .dropdown-menu {
      margin-top: 0.75rem;
    }
  }

  /* Responsive */
  @media screen and (max-width: 820px) {
    .app-header {
      position: static;
      height: auto;
      & > div[class*="container-"] {
        & > .row {
          & > div[class*="col-"] {
            flex: 0 0 100%;
          }
        }
      }
      .user-actions {
        margin-top: 1.5rem;
        & > ul {
          justify-content: space-between;
        }
      }
    }
  }

  @media screen and (max-width: 576px) {
    .app-header {
      .user-actions {
        & > ul {
          flex-flow: column-reverse;
          margin-top: -1.5rem;
          & > li {
            margin-top: 1.5rem;
            &.user-dropdown,
            &.user-notification {
              .dropdown {
                .dropdown-toggle {
                  .user-info {
                    label {
                      padding-right: 0;
                      &::after {
                        display: none;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
