:root{
    .v-overlay-container{
        .v-overlay{
            .v-overlay__scrim{
                background: rgba(0,0,0,0.7);
                opacity: 1;
            }
            .v-overlay__content{
                & > .v-card{
                    & > .v-card-title{
                        background-color: #026BB1;
                        color: #fff;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 15px 20px;
                        h5{
                            font-size: 22px;
                            font-weight: 600;
                            letter-spacing: 0;
                            margin: 0;
                        }
                        .v-btn{
                            &.dialog-close-btn{
                                background: none;
                                box-shadow: none;
                                padding: 0;
                                font-size: 15px;
                                transition: none;
                                color: rgba(0, 0, 0, 0.25);
                                width: auto;
                                height: auto;
                                &:hover, &:focus{
                                    background: none;
                                }
                                i{
                                    font-size: 22px !important;
                                }
                            }
                        }
                    }
                    & > .v-card-text{
                        padding: 25px 20px;
                    }
                    & > .v-card-actions{
                        justify-content: center;
                        gap: 10px;
                        padding-bottom: 30px;
                        & > .v-btn{
                            padding: 10px 20px;
                            height: auto;
                        }
                    }
                    &.dialog-confirmation{
                        overflow: visible;
                        .dialog-close{
                            position: absolute;
                            right: -16px;
                            top: -16px;
                            .v-btn{
                                width: 32px;
                                height: 32px;
                                font-size: 13px;
                                color: #7a7a7a;
                                background: #ffffff;
                                border: 2px solid #454a4c;
                                &:hover{
                                    background-color: #EF4D56;
                                    color: #fff;
                                }
                            }
                        }
                        & > .v-card-text{
                            text-align: center;
                            padding: 30px;
                            font-weight: 500;
                            .dialog-confirmation-icon{
                                font-size: 50px;
                                .v-icon{
                                    font-size: 50px;
                                }
                            }
                            h3, .dialog-confirmation-title{
                                font-weight: 600;
                                font-size: 36px;
                                margin: 0;
                            }
                            .dialog-confirmation-info{
                                font-size: 17px;
                                line-height: 24px;
                                color: #7D7D7D;
                                margin-top: 8px;
                            }
                        }
                        & > .v-card-actions{
                            padding-bottom: 40px;
                            padding-top: 10px;
                        }
                        &.negative-dialog-confirmation{
                            .dialog-confirmation-icon{
                                color: #EF4D56;
                            }
                        }
                    }
                }
            }
        }
    }
}