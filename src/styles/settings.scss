/**
 * src/styles/settings.scss
 *
 * Configures SASS variables and Vuetify overwrites
 */

// https://vuetifyjs.com/features/sass-variables/`
// @use 'vuetify/settings' with (
//   $color-pack: false
// );
/* Scrollbar track */
::-webkit-scrollbar {
  width: 6px; /* Width of the scrollbar */
  height: 6px; /* Height of the scrollbar for horizontal scrollbars */
}

/* Scrollbar thumb */
::-webkit-scrollbar-thumb {
  background: #9e9e9e; /* Scrollbar thumb color */
  border-radius: 6px; /* Rounded corners */
}

/* Scrollbar thumb on hover */
::-webkit-scrollbar-thumb:hover {
  background: #616161; /* Darker gray color on hover */
}

/* Scrollbar track */
::-webkit-scrollbar-track {
  background: #f1f1f1; /* Scrollbar track background */
  border-radius: 6px;
}
a {
  text-decoration: none !important;
  color: white;
}
h1 {
  font-size: 27px !important;
  font-weight: 700 !important;
  margin: 0;
  line-height: 36px;
}

