/**
 * plugins/vuetify.ts
 *
 * Framework documentation: https://vuetifyjs.com
 */

// Styles
import "@mdi/font/css/materialdesignicons.css";
import "vuetify/styles";
import "@fortawesome/fontawesome-free/css/all.css";
import { VDateInput } from 'vuetify/labs/VDateInput'
import { VFileUpload } from 'vuetify/labs/VFileUpload';

// Composables
import { createVuetify } from "vuetify";

// Import colors utility
import colors from "vuetify/util/colors";
import { mdi, aliases } from "vuetify/iconsets/mdi";
import { fa } from "vuetify/iconsets/fa";

const myCustomTheme = {
  colors: {
    
  }
}

export default createVuetify({
  icons: {
    defaultSet: "mdi",
    aliases,
    sets: {
      mdi,
      fa,
    },
  },
  components:{
    VDateInput,
    VFileUpload,
  },
  theme: {
    themes: {
      light: {
        dark: false,
        colors: {
          primary: colors.red.darken1, 
          secondary: colors.red.lighten4, 
          customGrey: "#9E9E9E", 
        },
      },
    },
    // defaultTheme: colors.grey.darken4, // Ensures the "light" theme is the default
  },
});
