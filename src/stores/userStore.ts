// stores/userStore.ts
import { defineStore } from "pinia";
import { ref } from "vue";

export const useUserStore = defineStore(
  "user",
  () => {
    const user = ref<any>(null);
    const isLoggedIn = ref(false);

    function setUser(userData: any) {
      user.value = userData;
      isLoggedIn.value = true;
    }

    function clearUser() {
      user.value = null;
      isLoggedIn.value = false;
    }

    return {
      user,
      isLoggedIn,
      setUser,
      clearUser,
    };
  },
  {
    persist: {
      storage: sessionStorage,
    },
  }
);
