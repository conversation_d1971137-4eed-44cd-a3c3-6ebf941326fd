<template>
  <div class="page-wrapper page-create-ticket">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row align="center" justify="space-between">
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">
              {{ strings.createTicket.title }}
            </h2>
          </div>
          <v-breadcrumbs :items="items" class="custom-breadcrumbs">
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
      </v-row>
    </div>

    <!-- Page Content Section -->
    <section class="page-inner-content page-content-wrapper">
      <v-row>
        <v-col sm="12" md="9" lg="9">
          <div class="form-wrapper">
            <form>
              <v-row>
                <v-col class="create-ticket-title-field">
                  <v-text-field
                    v-model="state.title"
                    :error-messages="
                      v$.title.$errors.map((e) => String(e.$message))
                    "
                    :label="strings.createTicket.ticketLabel"
                    :placeholder="strings.createTicket.ticketPlaceholder"
                    variant="outlined"
                    required
                    @blur="v$.title.$touch"
                    @input="v$.title.$touch"
                  />
                  <div
                    class="char-counter px-2"
                    :class="{
                      'limit-warning': isTitleNearLimit,
                      'limit-exceeded': isTitleExceeded,
                    }"
                  >
                    {{ state.title.length }} / 50 characters
                    <span v-if="isTitleExceeded" class="char-remaining">
                      ({{ titleCharsOverLimit }} over limit)
                    </span>
                    <span
                      v-else-if="isTitleNearLimit"
                      class="char-remaining warning"
                    >
                      ({{ 50 - state.title.length }} remaining)
                    </span>
                    <v-icon
                      v-else-if="state.title.length > 0"
                      color="success"
                      size="small"
                      class="ml-1"
                    >
                      mdi-check-circle
                    </v-icon>
                  </div>
                </v-col>
              </v-row>
              <v-row>
                <v-col sm="12" md="6" lg="6">
                  <div class="custom-select">
                    <v-select
                      v-model="state.project"
                      :error-messages="
                        v$.project.$errors.map((e) => String(e.$message))
                      "
                      :items="projectItems"
                      :label="strings.createTicket.projects"
                      :placeholder="strings.createTicket.projectPlaceholder"
                      variant="outlined"
                      item-title="text"
                      item-value="value"
                      required
                      class="select-field"
                      @blur="v$.project.$touch"
                      @update:model-value="onProjectChange"
                    />
                  </div>
                </v-col>
                <v-col sm="12" md="6" lg="6">
                  <div class="custom-select">
                    <v-select
                      v-model="state.priority"
                      :error-messages="
                        v$.priority.$errors.map((e) => String(e.$message))
                      "
                      :items="priorityItems"
                      :label="strings.createTicket.priority"
                      :placeholder="strings.createTicket.priorityPlaceholder"
                      item-title="text"
                      item-value="value"
                      variant="outlined"
                      required
                      class="select-field"
                      @blur="v$.priority.$touch"
                      @change="v$.priority.$touch"
                    />
                  </div>
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <v-textarea
                    v-model="state.description"
                    :error-messages="
                      v$.description.$errors.map((e) => String(e.$message))
                    "
                    label="Ticket Description"
                    variant="outlined"
                    required
                    @blur="v$.description.$touch"
                    @input="v$.description.$touch"
                  />
                  <div
                    class="char-counter px-2"
                    :class="{ 'requirement-met': isDescriptionValid }"
                  >
                    {{ state.description.length }} /
                    {{ minDescriptionLength }} characters
                    <span v-if="!isDescriptionValid" class="char-remaining">
                      ({{ descriptionCharsRemaining }} more needed)
                    </span>
                    <v-icon v-else color="success" size="small" class="ml-1">
                      mdi-check-circle
                    </v-icon>
                  </div>
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <!-- Multiple Email Input -->
                  <v-combobox
                    v-model="state.watchers"
                    :items="emailDropdownOptions"
                    item-title="email"
                    item-value="id"
                    :error-messages="
                      v$.watchers.$errors.map((e) => String(e.$message))
                    "
                    chips
                    multiple
                    closable-chips
                    outlined
                    :label="strings.createTicket.emailLabel"
                    :placeholder="strings.createTicket.emailPlaceholder"
                    variant="outlined"
                    return-object
                    :hide-no-data="true"
                    :no-filter="false"
                    @update:model-value="onWatcherUpdate"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <!-- Document Upload -->
                  <!-- Hidden file input -->
                  <input
                    ref="fileInput"
                    type="file"
                    multiple
                    accept="*"
                    style="display: none"
                    @change="handleFileSelect"
                  />

                  <!-- Upload Button -->
                  <v-btn variant="outlined" @click="triggerFilePicker">
                    {{ strings.createTicket.uploadDocuments }}
                  </v-btn>
                  <span class="file-size-limit">{{
                    strings.createTicket.uploadDocumentsText
                  }}</span>

                  <!-- Show selected file list -->
                  <!-- Show selected files -->
                  <div
                    v-if="state.attachement.length"
                    class="file-preview-wrapper"
                  >
                    <div
                      v-for="(file, index) in state.attachement"
                      :key="index"
                      class="file-preview"
                    >
                      <!-- Show image preview -->
                      <img
                        v-if="file && file.type.startsWith('image/')"
                        :src="getImageUrl(file)"
                        alt="Preview"
                        class="preview-image"
                      />

                      <!-- Show icon for non-images -->
                      <div v-else class="file-icon">
                        <v-icon size="40">
                          {{
                            file.type.includes("pdf")
                              ? "mdi-file-pdf"
                              : file.type.includes("word")
                              ? "mdi-file-word"
                              : file.type.includes("spreadsheetml")
                              ? "mdi-file-excel"
                              : "mdi-file-document"
                          }}
                        </v-icon>
                      </div>

                      <v-tooltip location="bottom">
                        <template #activator="{ props }">
                          <div class="file-name" v-bind="props">
                            {{ file.name }}
                          </div>
                        </template>
                        {{ file.name }}
                      </v-tooltip>

                      <!-- Remove Button -->
                      <v-btn
                        icon
                        size="x-small"
                        class="remove-btn"
                        @click="removeFile(index)"
                      >
                        <v-icon size="14"> mdi-close </v-icon>
                      </v-btn>
                    </div>
                  </div>

                  <!-- Show validation error if any -->
                  <label
                    v-if="v$.attachement.$errors.length"
                    class="error-text"
                  >
                    {{ v$.attachement.$errors[0].$message }}
                  </label>
                </v-col>
              </v-row>
              <v-row v-if="showJustification">
                <v-col>
                  <v-textarea
                    v-model="state.justification"
                    :error-messages="
                      v$.justification.$errors.map((e) => String(e.$message))
                    "
                    label="Justification"
                    variant="outlined"
                    required
                    @blur="v$.justification.$touch"
                    @input="v$.justification.$touch"
                  />
                  <div
                    class="char-counter px-2"
                    :class="{ 'requirement-met': isJustificationValid }"
                  >
                    {{ state.justification.length }} /
                    {{ minJustificationLength }} characters
                    <span v-if="!isJustificationValid" class="char-remaining">
                      ({{ justificationCharsRemaining }} more needed)
                    </span>
                    <v-icon v-else color="success" size="small" class="ml-1">
                      mdi-check-circle
                    </v-icon>
                  </div>
                </v-col>
              </v-row>
              <!-- <v-row>
                <v-col>
                  <v-checkbox
                    v-model="state.is_approved"
                    label="If you want approval for this Ticket"
                    color="success"
                    @change="state.status = state.is_approved ? 8 : 1"
                  />
                </v-col>
              </v-row> -->
              <!-- Cancel and Submit Button -->
              <v-row>
                <v-col>
                  <div class="btn-toolbar justify-center">
                    <v-btn variant="text" @click.prevent="clearForm">
                      {{ strings.createTicket.cancelButton }}
                    </v-btn>

                    <!-- <v-btn
                      :loading="loading"
                      variant="tonal"
                      :style="
                        loading
                          ? { backgroundColor: '#2dcc70', color: '#fff' }
                          : {}
                      "
                      @click="handleSubmit"
                    >
                      {{ strings.createTicket.submitButton }}
                      <template #loader>
                        <v-progress-circular
                          indeterminate
                          size="20"
                          class="spinner"
                        />
                      </template>
                    </v-btn> -->
                    <v-btn
                      :loading="loading"
                      variant="tonal"
                      @click="openDialog"
                    >
                      {{ strings.createTicket.submitButton }}
                      <template #loader>
                        <v-progress-circular
                          indeterminate
                          size="20"
                          class="spinner"
                        />
                      </template>
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
            </form>
          </div>
        </v-col>
        <v-col sm="12" md="3" lg="3">
          <v-card class="card-hightlight-default">
            <v-card-title>
              <h5>{{ strings.createTicket.tipsTitle }}</h5>
            </v-card-title>
            <v-card-text>
              <p>
                {{ strings.createTicket.tipsDescription }}
              </p>
              <v-expansion-panels v-model="panel" accordion>
                <v-expansion-panel>
                  <v-expansion-panel-title>
                    {{ strings.createTicket.trackProgressTitle }}
                  </v-expansion-panel-title>
                  <v-expansion-panel-text>
                    {{ strings.createTicket.trackProgressText }}
                  </v-expansion-panel-text>
                </v-expansion-panel>

                <v-expansion-panel>
                  <v-expansion-panel-title>
                    {{ strings.createTicket.addCommentsTitle }}
                  </v-expansion-panel-title>
                  <v-expansion-panel-text>
                    {{ strings.createTicket.addCommentsText }}
                  </v-expansion-panel-text>
                </v-expansion-panel>

                <v-expansion-panel>
                  <v-expansion-panel-title>
                    {{ strings.createTicket.resolveCloseTitle }}
                  </v-expansion-panel-title>
                  <v-expansion-panel-text>
                    {{ strings.createTicket.resolveCloseText }}
                  </v-expansion-panel-text>
                </v-expansion-panel>

                <v-expansion-panel>
                  <v-expansion-panel-title>{{
                    strings.createTicket.needHelpTitle
                  }}</v-expansion-panel-title>
                  <v-expansion-panel-text>
                    <ul class="fa-ul">
                      <li>
                        <span class="fa-li"
                          ><i class="fa-solid fa-phone"
                        /></span>
                        <span>{{ strings.createTicket.callHelpdesk }}</span>
                      </li>
                      <li>
                        <span class="fa-li"
                          ><i class="fa-solid fa-envelope"
                        /></span>
                        <span
                          ><a
                            href="mailto:<EMAIL>"
                            class="email-link"
                          >
                            {{ strings.createTicket.helpDeskEmail }}
                          </a></span
                        >
                      </li>
                    </ul>
                  </v-expansion-panel-text>
                </v-expansion-panel>
              </v-expansion-panels>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </section>
    <!-- Confirmation Dialog -->
    <v-dialog v-model="isDialogOpen" max-width="560">
      <v-card class="dialog-confirmation negative-dialog-confirmation">
        <!-- Content -->
        <v-card-text>
          <div class="dialog-confirmation-icon">
            <v-icon>mdi-alert-circle-outline</v-icon>
          </div>
          <h3 class="dialog-confirmation-title">
            {{ strings.createTicket.approvalDialogTitle }}
          </h3>
          <label class="dialog-confirmation-info"
            >This action requires confirmation. Do you want to request your
            manager's approval (<strong>{{ projectManager.full_name }}</strong
            >) for this ticket?</label
          >
        </v-card-text>

        <!-- Actions -->

        <v-card-actions>
          <v-btn
            variant="outlined"
            :loading="loadingNo"
            @click="handleSubmit(false, 'no')"
          >
            {{ strings.createTicket.proceedWithoutApproval }}
            <template #loader>
              <v-progress-circular indeterminate size="20" class="spinner" />
            </template>
          </v-btn>

          <v-btn
            variant="tonal"
            :loading="loading"
            @click="handleSubmit(true, 'yes')"
          >
            {{ strings.createTicket.needApproval }}
            <template #loader>
              <v-progress-circular indeterminate size="20" class="spinner" />
            </template>
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- <div v-if="loading" class="text-center mt-4">
      <v-progress-circular
        indeterminate
        color="blue-lighten-3"
        size="40"
      ></v-progress-circular>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, reactive, ref, watch } from "vue";
import { useVuelidate } from "@vuelidate/core";
import {
  email,
  required,
  helpers,
  minLength,
  maxLength,
} from "@vuelidate/validators";
import Button from "../../components/Button.vue";
import { apiClient, tickets } from "../../api/apiClient";
import { useRouter } from "vue-router";
import { useToast } from "vue-toastification";
import strings from "../../../src/assets/strings.json";
// State interface

type watchers = {
  id: any;
  email: string;
};

// Reactive state
const state = reactive<any>({
  title: "",
  description: "",
  project: "",
  priority: "",
  justification: "",
  watchers: [],
  attachement: [],
  created_by: "",
  location: "",
  status: 3, // Default status
  is_approved: false,
  assigned_to: "",
});

// Dropdown items
const projectItems = ref<any>([]); // Correctly structured
const priorityItems = ref([]);
const loading = ref<boolean>(false);
const loadingNo = ref<boolean>(false);
const toast = useToast();
const router = useRouter();
const allUsers = ref<watchers[]>([]);
// Validation rules
const maxFileSize = 2 * 1024 * 1024; // 2MB in bytes
const minDescriptionLength = 250;
const minJustificationLength = 250;

const customMinLengthDescription = helpers.withMessage(
  () => {
    const remaining = minDescriptionLength - state.description.length;
    return remaining > 0 ? `You need ${remaining} more characters` : "";
  },
  (value: string) => value?.length >= minDescriptionLength
);

const customMinLengthJustification = helpers.withMessage(
  () => {
    const remaining = minJustificationLength - state.justification.length;
    return remaining > 0 ? `You need ${remaining} more characters` : "";
  },
  (value: string) => value?.length >= minJustificationLength
);

const rules = {
  title: {
    required,
    maxLength: helpers.withMessage(
      "Title must be at most 50 characters",
      maxLength(50)
    ),
  },
  description: {
    required,
    customMinLengthDescription,
  },
  project: { required },
  priority: { required },
  justification: {
    required: helpers.withMessage(
      "Justification is required when priority is Very High or High",
      (value: string) => {
        // Only require justification when priority is 3 (High) or 4 (Very High)
        return !showJustification.value || !!value;
      }
    ),
    customMinLengthJustification: helpers.withMessage(
      (params) => {
        // Only apply length validation when justification is required
        if (!showJustification.value) return "";

        const remaining =
          minJustificationLength - (state.justification?.length || 0);
        return remaining > 0 ? `You need ${remaining} more characters` : "";
      },
      (value: string) => {
        // Skip validation if justification isn't required
        if (!showJustification.value) return true;
        return value?.length >= minJustificationLength;
      }
    ),
  },
  watchers: {
    required, // If mandatory
    emailList: helpers.withMessage(
      "Enter valid email addresses",
      (value: any) =>
        Array.isArray(value) &&
        value.every((user: any) => email.$validator(user.email, {}, {}))
    ),
  },
  attachement: {
    fileSize: helpers.withMessage(
      "Each file must be 2MB or less",
      (value: File[] | null) =>
        !value ||
        (Array.isArray(value) &&
          value.every((file) => file.size <= maxFileSize))
    ),
  },
  is_approved: {},
};

// Fetch user details
const user = JSON.parse(localStorage.getItem("user") || "{}");
const userDetails = ref<any>(null);
const getProjectMembers = ref<string[]>([]);
const emailDropdownOptions = ref<watchers[]>([]);
const approvalStatus = ref<string[]>([]);
const projectManager = ref<any>(null);

// Watch for priority changes and show justification if "Very High" is selected
const showJustification = ref(false);
const isDialogOpen = ref(false);
const openDialog = async () => {
  v$.value.$touch();
  await v$.value.$validate();

  if (v$.value.$error) {
    toast.error("Please correct the errors before submitting.");
    isDialogOpen.value = false; // Ensure it's false if errors
    return;
  }

  isDialogOpen.value = true; // Open dialog only if no errors
};

watch(
  () => state.priority,
  (newPriority) => {
    console.log("Selected Priority:", newPriority); // Debugging
    // Only show justification for High (3) and Very High (4)
    showJustification.value = newPriority === 4 || newPriority === 3;

    // If justification is no longer required, reset validation errors
    if (!showJustification.value) {
      v$.value.justification.$reset();
    }
  }
);

const fetchAdditionalUsers = async () => {
  try {
    const response = await getAllUsers(); // Fetch all users
    const loggedInUser = JSON.parse(localStorage.getItem("user") || "{}"); // Get logged-in user

    const roleBasedUsers = response.data.filter((user: any) =>
      ["Super Admin", "Admin", "Technician"].includes(user.role_name)
    );

    // Exclude the logged-in user if they are "Super Admin", "Admin", or "Technician"
    const filteredUsers = roleBasedUsers.filter(
      (user: any) => user.id !== loggedInUser.id
    );

    // Map the filtered users to match the dropdown format
    allUsers.value = filteredUsers.map((user: any) => ({
      id: user.id,
      email: user.email,
    }));
  } catch (error: any) {
    console.error("Error fetching additional users:", error);
  }
};

// Fetch all users and their projects
const fetchAllUsers = async () => {
  try {
    const response = await getUserById({ id: user.id });
    const getUsers = await getAllUsers();

    const userData: User = response.data; // ✅ Typed API response

    userDetails.value = userData; // ✅ Assigning user object correctly

    if (userData.projectDetails) {
      const projectDetailsList = userData.projectDetails.map(
        (proj: ProjectDetail) => ({
          value: proj.project_id,
          text: proj.project_name,
        })
      );

      projectItems.value = projectDetailsList;

      if (projectItems.value.length > 0) {
        state.project = projectItems.value[0].value;
        fetchProjectMembers(Number(state.project));
      }
    } else {
      console.warn("No projectDetails found for this user.");
    }
  } catch (error: any) {
    console.error("Error fetching users:", error);
  }
};

const fetchProjectMembers = async (projectId: number | null) => {
  if (!projectId) return;

  try {
    const response = await getProjectById({ id: projectId });
    projectManager.value = response.data.project_manager;

    const loggedInUserId = user.id;

    const projectMembers: any[] = response.data.members.filter(
      (member: any) => member.id !== loggedInUserId
    );

    // Add project manager if not already present
    if (
      projectManager.value &&
      projectManager.value.id !== loggedInUserId &&
      !projectMembers.some((m: any) => m.id === projectManager.value.id)
    ) {
      projectMembers.push(projectManager.value);
    }

    // Get all users and filter by role
    const allUsersRes = await getAllUsers();
    const filteredRoleUsers = allUsersRes.data.filter(
      (u: any) =>
        ["Super Admin", "Admin", "Technician"].includes(u.role_name) &&
        u.id !== loggedInUserId
    );

    // Combine and de-duplicate users
    const combinedUsers = [...projectMembers, ...filteredRoleUsers];

    const uniqueUsersMap = new Map<
      number,
      { id: number; email: string; role_name?: string }
    >();
    combinedUsers.forEach((u: any) => {
      if (u?.id && u?.email && !uniqueUsersMap.has(u.id)) {
        uniqueUsersMap.set(u.id, {
          id: u.id,
          email: u.email,
          role_name: u.role_name,
        });
      }
    });

    emailDropdownOptions.value = Array.from(uniqueUsersMap.values());

    // ✅ Set default watchers: Project Manager + one Super Admin
    const defaultWatchers: any[] = [];

    if (projectManager.value?.id) {
      const pm = emailDropdownOptions.value.find(
        (u) => u.id === projectManager.value.id
      );
      if (pm) defaultWatchers.push(pm);
    }

    const superAdmin = emailDropdownOptions.value.find((u) => u.id === 1);
    if (superAdmin && !defaultWatchers.find((w) => w.id === superAdmin.id)) {
      defaultWatchers.push(superAdmin);
    }

    setTimeout(() => {
      state.watchers = defaultWatchers;
    }, 0); // or try 100ms if needed
  } catch (error: any) {
    console.error("Error fetching project members:", error);
  }
};

const onWatcherUpdate = (newValues: any[]) => {
  // Avoid update if options not yet loaded
  if (!emailDropdownOptions.value.length) return;

  const validValues = newValues.filter((val) =>
    emailDropdownOptions.value.some((opt) => opt.id === val.id)
  );

  state.watchers = validValues;
};

watch(
  () => state.is_approved,
  (newValue: boolean) => {
    if (newValue) {
      state.assigned_to = projectManager.value?.id || "";
    } else {
      state.assigned_to = null; // Ensure assigned_to is null when is_approved is false
    }
  }
);

watch(
  () => state.project,
  async (newProjectId) => {
    if (!newProjectId) return;

    await fetchProjectMembers(newProjectId);
    state.watchers = []; // Reset selected watchers when changing projects
  }
);

const fetchApprovalOption = async () => {
  try {
    const response = await getApprovalStatus();
    approvalStatus.value = response.map((approval: any) => ({
      value: approval.id,
      text: approval.name,
    }));
  } catch (error: any) {
    console.error("Error fetching approvel status:", error);
  }
};

// Watch for project selection changes
const onProjectChange = (projectId: number | null) => {
  fetchProjectMembers(projectId);
  state.watchers = null;
};

// Initialize Vuelidate
const v$ = useVuelidate(rules, state);

// Utility to check valid file extensions
// const allowedExtensions = ["pdf", "csv", "xlsx", "xls", "jpg", "png"];

// Create FormData from state
function createFormData(): FormData {
  const formData = new FormData();

  const selectedProject: any = projectItems.value.find(
    (p: any) => p.value === state.project
  );

  formData.append("title", state.title || "");
  formData.append("description", state.description || "");
  formData.append("project", String(state.project)); // Store project name
  formData.append("priority", String(state.priority));
  formData.append("justification", state.justification || "");
  formData.append("created_by", user.id || "");
  formData.append("location", user.location || "");
  formData.append("status", state.status.toString());
  formData.append("is_approved", state.is_approved.toString()); // Append watchers as JSON array
  // formData.append("watchers", JSON.stringify(state.watchers));
  formData.append(
    "watchers",
    JSON.stringify(state.watchers.map((user: any) => user.id))
  );
  formData.append("assigned_to", state.assigned_to || "");

  // Append multiple files
  // Append multiple files (only if files exist)
  if (state.attachement && state.attachement.length > 0) {
    state.attachement.forEach((file: any) => {
      // if (!isValidFileExtension(file.name)) {
      //   throw new Error(`Invalid file type: ${file.name}`);
      // }
      formData.append("attachement", file);
    });
  }
  return formData;
}

// const allowedTypes = [
//   "application/pdf",
//   "image/png",
//   "image/jpeg",
//   "image/jpg",
//   "application/msword",
//   "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
//   "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
//   "text/plain",
// ];

const allowedTypes: string[] = []; // No restriction

const fileInput = ref<HTMLInputElement | null>(null);

const triggerFilePicker = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const files = input.files;
  if (!files) return;

  const selectedFiles = Array.from(files); // ✅ Real File objects

  const invalidFiles: File[] = [];
  const oversizedFiles: File[] = [];

  const newValidFiles = selectedFiles.filter((file) => {
    // Check file size separately
    if (file.size > maxFileSize) {
      oversizedFiles.push(file);
      return false;
    }

    // Check other validation criteria
    const isValid =
      allowedTypes.length === 0 || allowedTypes.includes(file.type);
    if (!isValid) {
      invalidFiles.push(file);
    }
    return isValid;
  });

  // Prevent duplicates (by name + size)
  const existingFileKeys = new Set(
    state.attachement.map((f: any) => `${f.name}-${f.size}`)
  );

  const uniqueFiles = newValidFiles.filter(
    (file) => !existingFileKeys.has(`${file.name}-${file.size}`)
  );

  // Append to attachment
  state.attachement = [...state.attachement, ...uniqueFiles];

  // Show specific error for oversized files
  if (oversizedFiles.length > 0) {
    const names = oversizedFiles.map((f) => f.name).join(", ");
    toast.error(`The following files exceed the 2MB size limit: ${names}`);
    v$.value.attachement.$touch();
  }

  // Show warning if any files were invalid for other reasons
  if (invalidFiles.length > 0) {
    const names = invalidFiles.map((f) => f.name).join(", ");
    toast.error(`Some files have invalid formats: ${names}`);
    v$.value.attachement.$touch();
  }

  // Reset input so same file can be selected again
  input.value = "";
};

const removeFile = (index: number) => {
  state.attachement.splice(index, 1);
};

const getImageUrl = (file: File) => {
  try {
    return URL.createObjectURL(file);
  } catch (error) {
    console.error("Invalid file for preview:", error);
    return "";
  }
};

// Clear form state
const clearForm = () => {
  v$.value.$reset(); // Reset validation errors
  state.title = "";
  state.description = "";
  state.project = null;
  state.priority = null;
  state.justification = "";
  state.watchers = [];
  state.attachement = [];
  state.is_approved = false;
  state.assigned_to = "";
  state.status = 3; // Reset to default status
};

// Submit form
async function handleSubmit(isApproved: boolean = false, type: "yes" | "no") {
  state.is_approved = isApproved;
  state.status = isApproved ? 8 : 3;

  // Check if justification is required but missing
  if (
    showJustification.value &&
    (!state.justification || state.justification.trim() === "")
  ) {
    toast.error("Justification is required when priority is Very High or High");
    v$.value.justification.$touch();
    return;
  }

  v$.value.$touch();
  await v$.value.$validate();

  if (v$.value.$error) {
    toast.error("Please correct the errors before submitting.");
    return;
  }

  // Set loader based on button type
  if (type === "yes") {
    loading.value = true;
  } else {
    loadingNo.value = true;
  }

  try {
    const formData = createFormData();
    const response = await tickets(formData);

    toast.success(response.message);
    router.push("/all-tickets");
    clearForm();
  } catch (error: any) {
    console.error("Error posting data:", error);
    toast.error(error.message);
  } finally {
    // Reset both loaders just in case
    loading.value = false;
    loadingNo.value = false;
  }
}

// Fetch priorities and users on component mount
onMounted(async () => {
  try {
    const response = await getPriorityLevel();
    priorityItems.value = response.map((priority: any) => ({
      value: priority.id,
      text: priority.priority_name,
    }));
  } catch (error: any) {
    console.error("Error fetching ticket data:", error);
  }
  fetchAllUsers();
  // fetchAdditionalUsers();
  fetchApprovalOption();
});

onUnmounted(() => {
  state.attachement.forEach((file: any) => {
    if (file instanceof File) {
      URL.revokeObjectURL(file.name);
    }
  });
});

// Computed properties for character counts
const descriptionCharsRemaining = computed(() => {
  return Math.max(0, minDescriptionLength - state.description.length);
});

const justificationCharsRemaining = computed(() => {
  return Math.max(0, minJustificationLength - state.justification.length);
});

const isDescriptionValid = computed(() => {
  return state.description.length >= minDescriptionLength;
});

const isJustificationValid = computed(() => {
  return state.justification.length >= minJustificationLength;
});

// Computed properties for title character count
const titleCharsOverLimit = computed(() => {
  return Math.max(0, state.title.length - 50);
});

const isTitleNearLimit = computed(() => {
  const remaining = 50 - state.title.length;
  return remaining >= 0 && remaining <= 10; // Warning when 10 or fewer characters remaining
});

const isTitleExceeded = computed(() => {
  return state.title.length > 50;
});
</script>

<script lang="ts">
import { defineComponent } from "vue";
// import type { User } from "@/stores/types";
import {
  getAllUsers,
  getApprovalStatus,
  getPriorityLevel,
  getProjectById,
  getUserById,
} from "../../api/apiClient";
import type { ProjectDetail, User } from "../../stores/data";

export default defineComponent({
  name: "CreateTicket",

  data() {
    return {
      panel: 0, // index of the panel to be open by default
      items: [
        { title: "Home", disabled: false, href: "/ticket-management" },
        {
          title: "Ticket Management",
          disabled: false,
          href: "/all-tickets",
        },
        {
          title: "Create Tickets",
          disabled: false,
          href: "",
        },
      ],
      strings,
    };
  },
});
</script>

<style>
.email-link {
  text-decoration: none;
  color: #5f7e9a;
}
.create-ticket-wrapp {
  background-color: white;
  padding: 12px;
}
.custom-select {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.custom-select-wrapp {
  display: flex;
  align-items: center;
  gap: 40px;
}
.select-field {
  cursor: pointer;
  width: 307px !important;
  margin-top: 4px;
}
.submit-btn {
  background-color: #2dcc70;
  color: #fff;
  text-transform: none;
}
.submit-btn:hover {
  background-color: #fff;
  color: #2dcc70;
}
.page-inner-content {
  min-height: calc(100vh - 180px);
}

.char-counter {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 12px;
  color: #666;
  margin-top: -18px;
}

.char-remaining {
  margin-left: 5px;
  color: #ff5252;
  font-weight: 500;
}

/* Add a transition for the character counter to make it smoother */
.char-counter,
.char-remaining {
  transition: all 0.2s ease;
}

/* Change color when minimum requirement is met */
.char-counter.requirement-met {
  color: #2dcc70;
}

/* Additional styles for title character counter */
.char-counter.limit-warning {
  color: #fb8c00; /* Orange warning color */
}

.char-counter.limit-exceeded {
  color: #ff5252; /* Red error color */
}

.char-remaining.warning {
  color: #fb8c00; /* Orange warning color */
}

/* Position the character counter for the title field */
.create-ticket-title-field .char-counter {
  margin-top: -16px; /* Adjust as needed */
  margin-bottom: 8px;
}

.file-size-limit {
  font-size: 0.8rem;
  color: #868686;
  margin-left: 8px;
  font-style: italic;
}

.file-upload-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
