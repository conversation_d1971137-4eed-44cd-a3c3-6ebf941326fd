<template>
  <div class="page-assign-ticket">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row align="center" justify="space-between">
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">Ticket Lists</h2>
            <v-chip size="x-small" class="data-count ml-2">
              Showing {{ tickets.length }} of {{ totalTickets }} Records
            </v-chip>
          </div>
          <v-breadcrumbs :items="items" class="breadcrumbs">
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
      </v-row>
    </div>

    <!-- Search and Filter User -->
    <!-- <section class="filter-wrapper">
      <Filter
        v-model:search-value="searchValue"
        :filters="filters"
        :search-placeholder="strings.assignTicket.searchPlaceholder"
        @update:filter-values="updateFilterValues"
      />
    </section> -->
    <section class="filter-wrapper">
      <v-row align="center" justify="space-between">
        <v-col cols="12" sm="12" md="9" lg="10">
          <v-row align="center">
            <!-- Search Input -->
            <v-col cols="12" lg="4" md="6" sm="12">
              <div class="form-group d-block">
                <input
                  id="searchValue"
                  v-model="searchValue"
                  type="text"
                  class="search-input"
                  :placeholder="
                    strings.manageUser.searchAndFilter.ticketListSearch
                  "
                />
              </div>
            </v-col>

            <!-- Status Filter -->
            <v-col cols="12" lg="3" md="6" sm="3">
              <div class="form-group d-block" />
              <div class="form-group d-block">
                <select
                  v-model="status"
                  :class="status === '' ? 'placeholder' : ''"
                  class="form-input"
                >
                  <option value="" class="dropdown-default-value">
                    {{ strings.manageUser.searchAndFilter.statusFilter }}
                  </option>
                  <option
                    v-for="option in statusOptions"
                    :key="option.id"
                    :value="option.value"
                  >
                    {{ option.text }}
                  </option>
                </select>
              </div>
            </v-col>

            <!-- Priority Filter -->
            <v-col cols="12" lg="3" md="6" sm="3">
              <div class="form-group d-block">
                <select
                  v-model="priority"
                  :class="priority === '' ? 'placeholder' : ''"
                  class="form-input"
                >
                  <option value="">
                    {{ strings.manageUser.searchAndFilter.priorityFilter }}
                  </option>
                  <option
                    v-for="option in priorityOptions"
                    :key="option.id"
                    :value="option.id"
                  >
                    {{ option.priority_name }}
                  </option>
                </select>
              </div>
            </v-col>

            <!-- Location Filter -->
            <v-col cols="12" lg="2" md="6" sm="3">
              <div class="form-group d-block">
                <select
                  id="location"
                  v-model="locations"
                  :class="locations === '' ? 'placeholder' : ''"
                  class="form-input"
                >
                  <option value="">
                    {{ strings.manageUser.searchAndFilter.locationFilter }}
                  </option>
                  <option
                    v-for="location in locationOptions"
                    :key="location.id"
                    :value="location.location_name"
                  >
                    {{ location.location_name }}
                  </option>
                </select>
              </div>
            </v-col>
          </v-row>
        </v-col>

        <!-- Search & Reset Buttons -->
        <v-col>
          <div class="btn-toolbar">
            <v-btn
              icon="mdi-magnify"
              variant="tonal"
              @click="fetchFilteredTickets(true)"
            />
            <v-btn icon="mdi-cached" variant="text" @click="handleResetClick" />
          </div>
        </v-col>
      </v-row>
    </section>

    <section class="page-content-wrapper">
      <!-- Loading Indicator -->
      <!-- <div v-if="loading" class="text-center mt-4">
        <v-progress-circular indeterminate color="blue-lighten-3" size="40" />
      </div> -->

      <!-- No Data Message -->
      <div v-if="noData && !loading && !errorMessage" class="text-center">
        <div class="no-data-available-wrapper">
          <figure>
            <img
              src="../../assets/images/no-data-available.svg"
              alt="No Data Available"
            />
          </figure>
          <label>{{ strings.common.noDataText }}</label>
          <small>{{ strings.common.noDataAvailableText }}</small>
        </div>
      </div>

      <!-- Ticket List -->

      <div v-if="!loading && !errorMessage && tickets.length > 0">
        <div
          v-for="(ticket, index) in tickets"
          :key="index"
          class="card-list-wrapper"
        >
          <v-card class="app-primary-card mb-3 cursor-pointer ticket-list-card">
            <v-row align="center" justify="space-between" no-gutters>
              <v-col cols="12">
                <v-row
                  align="center"
                  @click="
                    !isWatcher(ticket) ? editTicketPage(ticket.ticket_id) : null
                  "
                >
                  <v-col
                    cols="12"
                    sm="6"
                    md="4"
                    lg="3"
                    class="user-data-details"
                  >
                    <div class="name-section d-block">
                      <Batch class="batch-ticket-all">
                        {{ strings.allTicket.ticketId }} -
                        {{ ticket.ticket_id }}
                      </Batch>
                      <div class="user-data-value d-block mt-1">
                        {{ ticket.title }}
                      </div>
                    </div>
                  </v-col>
                  <v-col
                    cols="12"
                    sm="6"
                    md="4"
                    lg="2"
                    class="user-data-details"
                  >
                    <label class="user-data-header">
                      {{ strings.allTicket.projectTitle }}
                    </label>
                    <div class="user-data-value">
                      {{ ticket.project_name || "N/A" }}
                    </div>
                  </v-col>
                  <v-col
                    cols="12"
                    sm="6"
                    md="4"
                    lg="2"
                    class="user-data-details"
                  >
                    <label class="user-data-header">
                      {{ strings.allTicket.requesterName }}
                    </label>
                    <div class="user-data-value">
                      {{ ticket.created_by_fullname || "N/A" }}
                    </div>
                  </v-col>
                  <v-col
                    cols="12"
                    sm="6"
                    md="4"
                    lg="2"
                    class="user-data-details"
                  >
                    <label class="user-data-header">
                      {{ strings.allTicket.assignedName }}
                    </label>
                    <div class="user-data-value">
                      {{ ticket.assigned_to_fullname || "N/A" }}
                    </div>
                  </v-col>
                  <v-col
                    cols="12"
                    sm="6"
                    md="4"
                    lg="2"
                    class="user-data-details"
                  >
                    <label class="user-data-header">
                      {{ strings.allTicket.statusName }}
                    </label>
                    <div class="user-data-value">
                      {{ ticket.status_name || "N/A" }}
                    </div>
                  </v-col>

                  <!-- Buttons Section -->
                  <v-col
                    cols="12"
                    sm="6"
                    md="4"
                    lg="auto"
                    class="d-flex align-center justify-end text-right"
                  >
                    <div class="btn-toolbar d-flex flex-nowrap">
                      <v-tooltip text="Edit" location="top">
                        <template #activator="{ props }">
                          <v-btn
                            variant="outlined"
                            icon
                            v-bind="props"
                            class="mx-1"
                            @click="editTicketPage(ticket.ticket_id)"
                          >
                            <v-icon icon="mdi-pencil" />
                          </v-btn>
                        </template>
                      </v-tooltip>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <div class="ticket-calendar-stats">
              <ul>
                <li>
                  <label>
                    <span>Created:</span>
                    <strong>{{ formatDate(ticket.created_at) }}</strong>
                  </label>
                </li>
                <li>
                  <label>
                    <span>Last Updated:</span>
                    <strong>{{ formatDate(ticket.updated_at) }}</strong>
                  </label>
                </li>
              </ul>
            </div>
          </v-card>
        </div>
      </div>

      <!-- Loading More Indicator -->
      <div ref="loadMoreTrigger" class="loading-bottom">
        <v-progress-circular v-if="loadingMore" indeterminate color="primary" />
      </div>

      <!-- Warning Message -->
      <p v-if="noData && tickets.length > 0" class="warning">
        {{ strings.allTicket.noDatas }}
      </p>
    </section>
  </div>
</template>

<script lang="ts">
import {
  ref,
  onMounted,
  reactive,
  defineComponent,
  onUnmounted,
  watch,
  nextTick,
  defineProps,
  toRef,
} from "vue";
import Button from "../../components/Button.vue";
import {
  getLocations,
  getStatus,
  getTickets,
  getUserById,
  apiClient,
  getPriorityLevel,
} from "../../api/apiClient";
import Batch from "../../components/Batch.vue";
import router from "../../router";
import { useRoute } from "vue-router";
import Filter from "../../components/Filter.vue";
// import { useRouter } from "vue-router";
import { useToast } from "vue-toastification";
import strings from "../../../src/assets/strings.json";
import { debounce } from "lodash-es";
import { useTicketFilterStore } from "../../stores/ticketFilterStore";
// Define interfaces for your data structures
export interface Ticket {
  ticket_id: number;
  title: string;
  description: string;
  category: number;
  subcategory: number;
  documents: [];
  project: string;
  status: string;
  urgency: string;
  location: string;
  created_at: Date;
  updated_at: Date;
  closed_at: number;
  solved_at: number;
  assigned_to_fullname: string;
  created_by_fullname: string;
  assigned_to?: {
    first_name: string;
    email: string;
    [key: string]: any;
  };
  created_by: number; // Add this if not present
  watchers: string;
  createdByUser?: {
    first_name: string;
    email: string;
    [key: string]: any;
  };
  due_date: string;
}

export default defineComponent({
  name: "Ticketlist",
  components: {
    Button,
    Batch,
    Filter,
  },
  props: {
    isMyTickets: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const tickets = ref<any>([]);
    const totalTickets = ref(0);
    console.log("tickets:", tickets);
    console.log("totalTickets:", totalTickets);
    const originalTickets = ref<Ticket[]>([]); // To store the original data for resetting filters
    const locations = ref<any>("");
    const locationOptions = ref<{ id: string; location_name: string }[]>([]);
    console.log(locations);
    const dialogVisible = ref(false);

    const page = ref(1);
    const errorMessage = ref<any>("");
    const toast = useToast();
    // pagination
    const noData: any = ref(false);
    const loading = ref(false);
    const loadingMore = ref(false);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const hasMore = ref(true);
    const pageSize = 10;
    const searchValue = ref<any>("");
    const priority = ref<any>("");
    const priorityOptions = ref<any>([]);
    const searchQuery = ref<string>("");
    const status = ref<any>("");
    const statusOptions = ref<any>([]);
    const loadMoreTrigger = ref(null);
    const isFiltering = ref(false);
    const route = useRoute();
    const ticketFilterStore = useTicketFilterStore();

    // const props = defineProps<{
    //   isMyTickets: boolean;
    // }>();
    console.log(props.isMyTickets);

    // Initialize filters from store if available
    searchValue.value = ticketFilterStore.searchValue || "";
    status.value = ticketFilterStore.status || "";
    priority.value = ticketFilterStore.priority || "";
    locations.value = ticketFilterStore.locations || "";
    isFiltering.value = !!ticketFilterStore.isFiltering;

    // Update store when filters change
    watch(
      [searchValue, status, priority, locations],
      ([newSearch, newStatus, newPriority, newLocations]) => {
        ticketFilterStore.updateFilters({
          searchValue: newSearch,
          status: newStatus,
          priority: newPriority,
          locations: newLocations,
          isFiltering: isFiltering.value,
        });
      },
      { deep: true }
    );

    // Define the type for the ticketAssigneeOptions

    // Filter Functionality
    const user = JSON.parse(localStorage.getItem("user") || "{}");

    const fetchFilteredTickets = async (isInitialLoad = false) => {
      if (loading.value || loadingMore.value) return;
      if (!hasMore.value && !isInitialLoad) return;

      try {
        isFiltering.value = true;

        // Update store with current filter state
        ticketFilterStore.updateFilters({
          searchValue: searchValue.value,
          status: status.value,
          priority: priority.value,
          locations: locations.value,
          isFiltering: true,
        });

        if (isInitialLoad) {
          loading.value = true;
          tickets.value = [];
          noData.value = false;
          hasMore.value = true;
          currentPage.value = 1;
        } else {
          loadingMore.value = true;
        }

        const selectedLocation =
          locationOptions.value.find(
            (loc) => loc.location_name === locations.value
          )?.id || "";

        const params: any = {
          search: searchValue.value?.trim() || null,
          priority: priority.value || null,
          location: selectedLocation || null,
          page: currentPage.value,
          page_size: pageSize,
          role: user.role,
          user_id: user.id,
        };

        // ✅ Add ticket type based on prop or state
        if (props.isMyTickets) {
          params.my_ticket = 1;
        } else {
          params.team_ticket = 1;
        }

        // ✅ Add status or exclude flag
        if (status.value === "exclude_solved_closed") {
          params.exclude_solved_closed = 1;
        } else if (status.value) {
          params.status = status.value;
        }

        const response = await apiClient.get("tickets-all/", {
          params,
        });

        const ticketsData = response.data.results || [];

        if (isInitialLoad) {
          tickets.value = ticketsData;
        } else {
          tickets.value = [...tickets.value, ...ticketsData];
        }

        totalTickets.value = response.data.count || 0;
        totalPages.value = response.data.total_pages || 1;

        const current = response.data.current_page || 1;
        currentPage.value = current + 1;

        hasMore.value = current < totalPages.value;
        noData.value = tickets.value.length === 0;
      } catch (error: any) {
        console.error("Error fetching tickets:", error);
        errorMessage.value = "Error loading tickets.";
        hasMore.value = false;
      } finally {
        loading.value = false;
        loadingMore.value = false;
      }
    };

    const fetchData = async (isInitialLoad = false, force = false) => {
      if (
        !force &&
        (loading.value ||
          loadingMore.value ||
          !hasMore.value ||
          isFiltering.value)
      )
        return;

      loading.value = isInitialLoad;
      loadingMore.value = !isInitialLoad;
      errorMessage.value = "";

      if (isInitialLoad) {
        tickets.value = [];
        noData.value = false;
        currentPage.value = 1;
      }

      const params: any = {
        page: currentPage.value,
        page_size: pageSize,
        role: user.role,
        user_id: user.id,
      };

      // Check if we should filter by my_ticket based on route or props
      if (props.isMyTickets || route.query.filter === "my") {
        params.my_ticket = 1;
      } else {
        params.team_ticket = 1;
      }

      try {
        const response = await apiClient.get("tickets-all/", { params });
        const ticketsData = response.data.results || [];

        tickets.value = isInitialLoad
          ? ticketsData
          : [...tickets.value, ...ticketsData];

        totalTickets.value = response.data.count || 0;
        totalPages.value = response.data.total_pages || 1;
        currentPage.value = (response.data.current_page || 1) + 1;
        hasMore.value = currentPage.value <= totalPages.value;
        noData.value = tickets.value.length === 0;
      } catch (error: any) {
        console.error("Error fetching tickets:", error);
        errorMessage.value = "Error loading tickets.";
        toast.error("Error loading tickets.");
      } finally {
        loading.value = false;
        loadingMore.value = false;
      }
    };

    const isMyTicketsRef = toRef(props, "isMyTickets");
    watch(isMyTicketsRef, (newVal) => {
      console.log("isMyTickets changed to:", newVal);
      fetchFilteredTickets(true); // Refetch on toggle
    });

    const isWatcher = (ticket: any) => {
      return ticket.watchers?.some(
        (watcher: { id: number }) => watcher.id === user.id
      );
    };
    const formatDate = (dateString: any) => {
      return new Date(dateString).toLocaleString("en-US", {
        month: "short", // Apr
        day: "2-digit", // 29
        year: "numeric", // 2025
        hour: "2-digit", // 06
        minute: "2-digit", // 46
        hour12: true, // PM/AM
      });
    };
    const debouncedSearch = debounce((value: string) => {
      console.log("Debounced search triggered:", value);
      fetchFilteredTickets(true); // Force reset on search
    }, 300);

    // Watch searchValue changes
    watch(searchValue, (newValue) => {
      console.log("Search Value Changed:", newValue);
      hasMore.value = true; // Reset hasMore to allow new searches
      debouncedSearch(newValue);
    });

    const handleScroll = () => {
      if (noData.value || !hasMore.value) return;

      const scrollPosition = window.innerHeight + window.scrollY;
      const documentHeight = document.documentElement.scrollHeight;

      if (scrollPosition >= documentHeight - 100) {
        if (isFiltering.value) {
          fetchFilteredTickets(true); // ✅ Load more filtered data
        } else {
          fetchData(false); // ✅ Load more normal data
        }
      }
    };

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          if (!hasMore.value) return;
          if (isFiltering.value) {
            fetchFilteredTickets(false); // ✅ Load more filtered data
          } else {
            fetchData(false); // ✅ Load more normal data
          }
        }
      },
      { rootMargin: "100px" }
    );

    onMounted(() => {
      fetchData(true);
      if (loadMoreTrigger.value) observer.observe(loadMoreTrigger.value);
      window.addEventListener("scroll", handleScroll);
    });

    onUnmounted(() => {
      observer.disconnect();
      window.removeEventListener("scroll", handleScroll);
    });

    const isUpdating = ref(false); // State for loader

    const resetFilters = () => {
      // Reset all filter values
      searchValue.value = "";
      status.value = "";
      priority.value = "";
      locations.value = "";

      // Reset filtering state
      isFiltering.value = false;

      // Reset pagination parameters
      hasMore.value = true;
      currentPage.value = 1;
      // ✅ Call fetchData with isMyTickets to fetch correct data
      fetchData(true, true);
    };

    onMounted(async () => {
      try {
        // Initialize store from localStorage
        ticketFilterStore.initFromStorage();

        const locationData = await getLocations();
        const priorityData = await getPriorityLevel();
        const statusData = await getStatus();

        locationOptions.value = locationData.data.map((location: any) => ({
          id: location.location_id,
          location_name: location.location_name,
        }));

        priorityOptions.value = priorityData.map((priority: any) => ({
          id: priority.id,
          priority_name: priority.priority_name,
        }));

        statusOptions.value = [
          {
            value: "exclude_solved_closed",
            text: "All (Except Solved & Closed)",
          },
          ...statusData.map((status: any) => ({
            value: status.id,
            text: status.name,
          })),
        ];

        // Check if we have URL query params or stored filters
        if (route.query.status) {
          status.value = route.query.status; // Set the status from URL
          isFiltering.value = true;
          await fetchFilteredTickets(true); // Call Filtered API immediately
        } else if (isFiltering.value) {
          // If we have stored filters, use them
          await fetchFilteredTickets(true);
        } else {
          isFiltering.value = false;
          await fetchData(true);
        }
      } catch (error) {
        console.error("Error fetching data: ", error);
      }
      if (loadMoreTrigger.value) observer.observe(loadMoreTrigger.value);
      window.addEventListener("scroll", handleScroll);
    });

    const editTicketPage = (ticket_id: number) => {
      router
        .push({
          name: "/TicketManagement/editTicket",
          params: { ticket_id }, // Convert to string as route params are strings
        })
        .catch((err) => {
          if (err.name !== "NavigationDuplicated") {
            console.error("Navigation Error:", err);
          }
        });
    };

    watch(status, (newValue) => {
      if (newValue) {
        isFiltering.value = true;
        fetchFilteredTickets(true);
      }
    });

    const handleResetClick = async () => {
      console.log("Reset button clicked");

      // Reset all filter values
      searchValue.value = "";
      status.value = "";
      priority.value = "";
      locations.value = "";

      // Reset filtering state
      isFiltering.value = false;

      // Clear filter store
      ticketFilterStore.clearFilters();

      // Reset pagination parameters
      hasMore.value = true;
      currentPage.value = 1;

      // Clear URL query parameters if they exist
      if (Object.keys(route.query).length > 0) {
        await router.replace({ path: router.currentRoute.value.path });
      }

      // Force data refresh
      tickets.value = [];
      noData.value = false;

      try {
        loading.value = true;

        const params: any = {
          page: 1,
          page_size: pageSize,
          role: user.role,
          user_id: user.id,
        };

        // ✅ Include either my_ticket or team_ticket
        if (props.isMyTickets) {
          params.my_ticket = 1;
        } else {
          params.team_ticket = 1;
        }

        const response = await apiClient.get("tickets-all/", { params });

        const ticketsData = response.data.results || [];
        tickets.value = ticketsData;
        totalTickets.value = response.data.count || 0;
        totalPages.value = response.data.total_pages || 1;
        currentPage.value = 2; // Set to 2 for next page load
        hasMore.value = true;
        noData.value = tickets.value.length === 0;
      } catch (error) {
        console.error("Error fetching tickets:", error);
        errorMessage.value = "Error loading tickets.";
      } finally {
        loading.value = false;
      }
    };

    return {
      tickets,
      locations,
      priority,
      status,
      locationOptions,
      searchQuery,
      statusOptions,
      priorityOptions,
      loadMoreTrigger,
      editTicketPage,
      resetFilters,
      handleResetClick,
      searchValue,
      isWatcher,
      dialogVisible,
      isUpdating,
      loading,
      totalTickets,
      strings,
      noData,
      errorMessage,
      loadingMore,
      fetchFilteredTickets,
      formatDate,
    };
  },
  data: () => ({
    items: [
      { title: "Home", disabled: false, href: "/" },
      {
        title: "Ticket Management",
        disabled: false,
        href: "/all-tickets",
      },
      {
        title: "Ticket List",
        disabled: true,
        href: "",
      },
    ],
  }),
});
</script>

<style>
.custom-breadcrumbs {
  color: #8c8b90;
  font-size: 12px;
}
.v-breadcrumbs {
  padding: 0% !important;
}
.v-breadcrumbs--density-default {
  padding: 0% !important;
}
.breadcrums-heading {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between !important;
  /* width: 77%; */
}
</style>

<style scoped>
h1 {
  font-size: 27px;
  font-weight: 700;
  margin: 0;
}
.card {
  background: white;
  border: 2px solid #d8d8d8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  /* width: 77%; */
  padding: 12px 24px;
  cursor: pointer;
}

/* search & filter */
.search-filter-section {
  background-color: #f9f9f9;
  border-radius: 5px;
  margin: 5px;
  padding: 10px 5px;
}
.dropdown-default-value {
  color: #b5b5b5;
}
.form-section-container {
  padding: 12px;
}
.form-input,
.search-input {
  width: 100%;
  font-size: 14px;
  border: 1px solid #ccc;
  padding: 15px;
  border-radius: 4px;
  box-sizing: border-box;
  background-color: #fff;
}
.form-input {
  cursor: pointer;
}
.search-input {
  cursor: auto;
}
.form-input:focus {
  border-color: #000;
}
.placeholder {
  color: gray;
}

.card:hover {
  background-color: #f9f9f9;
  transition: background-color 0.3s, color 0.3s;
}
.hover-content {
  display: none;
}

.assign-ticket-card {
  border-radius: 8px;
}

.dialog-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  background-color: #1565c0; /* Matches the blue header color in the image */
  padding: 12px;
}

/* .cancel-btn {
  background-color: #e0e0e0;
  color: #757575;
}

.submit-btn {
  background-color: #4caf50;
  color: white;
} */

/* .card:hover,
.hover-content {
  display: block;
  position: absolute;
  z-index: 1000;
  width: 100%;
  height: 150px;
} */
.card-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.custom-card-item {
  padding: 10px;
  display: flex;
  /* justify-content: space-between !important; */
  align-items: center;
  width: 100%;
  gap: 40px;
}
.custom-card {
  /* padding: 6px; */
  display: flex;
  gap: 4px;
  flex-direction: column;
}

.custom-card span {
  font-size: 14px;
  font-weight: 500;
  color: #8c8c8c;
}
.custom-card p {
  font-size: 14px;
  font-weight: 500;
  color: #2e2e2e;
}

.filter-fields {
  display: flex;
  flex-wrap: wrap;
  /* align-items: center; */
  justify-content: space-evenly;
}

.action-btn {
  display: flex;
  flex-direction: row;
  /* justify-content: space-between; */
  gap: 12px;
}
.view-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #026bb1;
  border-radius: 50%;
  color: #026bb1;
}
.edit-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #2dcc70;
  border-radius: 50%;
  color: #2dcc70;
}
.custom-btn {
  background: none;
  border: 1px solid #2dcc70;
  border-radius: 5px;
  color: #2dcc70;
  box-shadow: none !important;
  margin: 0;
  text-transform: none;
}
.high-btn {
  background: none;
  border: 1px solid #ff7780;
  border-radius: 50px;
  color: #ff7780;
  box-shadow: none !important;
  margin: 0;
  padding: 0%;
  text-transform: none;
  max-width: 57px;
}

.filters-wrapp {
  margin-top: 20px;
  /* width: 77%; */
  display: flex;
  flex-wrap: wrap;
  background-color: #f9f9f9;
  padding: 12px;
  align-items: center;
  gap: 12px;
}

.search-icon {
  background: #2dcc70;
  color: #fff;
  width: 38px;
  height: 38px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.loading-bottom {
  display: flex;
  justify-content: center;
  padding: 20px;
}
/* .search-icon:hover {
  color: #2dcc70;
  background: #fff;
} */
.cached-icon {
  background: #e6e6e6;
  color: #b5b5b5;
  width: 38px;
  height: 38px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* Custom styles for inputs */

input,
.v-select__selections,
.v-text-field input {
  background: transparent;
  outline: none;
  box-shadow: none;
  font-size: 14px;
}

.v-select__control,
.v-text-field__control {
  border-radius: 12px; /* Same border radius for select */
  border: 1px solid #d8d8d8; /* Same border color */
}

.v-select__control:focus,
.v-text-field__control:focus {
  border-color: #2dcc70; /* Same focus color */
}
.v-icon {
  font-size: 14px !important;
}
.v-list-item__append > .v-icon {
  font-size: 14px !important;
}
</style>
