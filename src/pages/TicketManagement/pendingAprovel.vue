<template>
  <div class="page-wrapper page-pending-approval">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row
        align="center"
        justify="space-between"
      >
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h1>{{ strings.pendingApproval.title }}</h1>
            <v-chip
              size="x-small"
              class="data-count ml-2"
            >
              Showing {{ tickets.length }} of {{ totalTickets }} Records
            </v-chip>
          </div>
          <v-breadcrumbs
            :items="items"
            class="custom-breadcrumbs"
          >
            <template #title="{ item }">
              {{ item.title.toUpperCase() }}
            </template>
          </v-breadcrumbs>
        </v-col>
        <v-col cols="auto">
&nbsp;
        </v-col>
      </v-row>
    </div>

    <!-- Filter Section -->
    <!-- <section class="filter-wrapper">
      <Filter
        v-model:search-value="searchValue"
        :filters="filters"
        :search-placeholder="strings.assignTicket.searchPlaceholder"
        @update:filter-values="updateFilterValues"
      />
    </section> -->

    <section class="page-content-wrapper">
      <!-- No Data Available -->
      <div
        v-if="noData && !loading && !errorMessage"
        class="text-center"
        align="center"
      >
        <div class="no-data-available-wrapper">
          <figure>
            <img
              src="../../assets/images/no-data-available.svg"
              alt="No Data Available"
            >
          </figure>
          <label>{{ strings.ticketCategories.noDataAvailable }}</label>
          <small>{{ strings.common.noDataAvailableText }}</small>
        </div>
      </div>

      <div
        v-if="loading"
        class="text-center mt-4"
      >
        <v-progress-circular
          indeterminate
          color="blue-lighten-3"
          size="40"
        />
      </div>

      <!-- Ticket Cards Section -->

      <div v-if="!loading && !errorMessage && pendingTickets.length > 0">
        <div
          v-for="(ticket, index) in pendingTickets"
          :key="index"
          class="card-list-wrapper"
        >
          <v-card class="app-primary-card mt-2">
            <v-row
              align="center"
              justify="space-between"
            >
              <v-col
                xs="12"
                sm="12"
                md="3"
                lg="3"
                class="user-data-details"
              >
                <div class="name-section d-block">
                  <Batch class="batch-ticket-all">
                    {{ strings.pendingApproval.ticketId }} -
                    {{ ticket.ticket_id }}
                  </Batch>
                  <div class="user-data-value d-block">
                    {{ ticket.title }}
                  </div>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="12"
                md="3"
                lg="3"
                class="user-data-details"
              >
                <label class="user-data-header">
                  {{ strings.pendingApproval.projectTitle }}
                </label>
                <div class="user-data-value">
                  {{ ticket.project_name }}
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="12"
                md="2"
                lg="2"
                class="user-data-details"
              >
                <label class="user-data-header">
                  {{ strings.pendingApproval.requesterName }}
                </label>
                <div class="user-data-value">
                  {{ ticket.created_by_fullname || "N/A" }}
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="12"
                md="2"
                lg="2"
                class="user-data-details"
              >
                <label class="user-data-header">
                  {{ strings.pendingApproval.assignedName }}
                </label>
                <div class="user-data-value">
                  {{ ticket.assigned_to_fullname || "N/A" }}
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="12"
                md="2"
                lg="2"
              >
                <div class="btn-toolbar">
                  <v-tooltip
                    text="Approval"
                    location="top"
                  >
                    <template #activator="{ props: activatorProps }">
                      <v-btn
                        v-bind="activatorProps"
                        size="small"
                        variant="outlined"
                        icon
                        :disabled="!hasApprovalAccess(ticket.assigned_to)"
                        @click="openDialog(ticket.ticket_id)"
                      >
                        <v-icon icon="mdi-check" />
                      </v-btn>
                    </template>
                  </v-tooltip>
                  <!-- <v-btn
                  v-if="user.role === 'R001'"
                  variant="outlined"
                  class="btn-outlined-secondary"
                  icon
                  @click="editTicketPage(ticket.ticket_id)"
                >
                  <v-icon icon="mdi:mdi-pencil" />
                </v-btn> -->
                  <v-tooltip
                    text="View"
                    location="top"
                  >
                    <!--Approved-->
                    <template #activator="{ props }">
                      <v-btn
                        variant="outlined"
                        icon
                        v-bind="props"
                        @click="editTicketPage(ticket.ticket_id)"
                      >
                        <v-icon
                          icon="fa:fas fa-eye"
                          size="small"
                        />
                      </v-btn>
                    </template>
                  </v-tooltip>
                  <v-tooltip
                    text="Cancel"
                    location="top"
                  >
                    <!--Rejected-->
                    <template #activator="{ props }">
                      <v-btn
                        class="btn-outlined-danger"
                        variant="outlined"
                        icon
                        v-bind="props"
                        :disabled="!hasApprovalAccess(ticket.assigned_to)"
                        @click="openDeleteDialog(ticket.ticket_id)"
                      >
                        <v-icon icon="mdi-close" />
                      </v-btn>
                    </template>
                  </v-tooltip>
                  <!-- <v-dialog v-model="dialogVisible" max-width="500">
                      <v-card class="assign-ticket-card">
                        <v-card-title>
                          <h5>{{ strings.pendingApproval.approvalLabel }}</h5>
                          <v-btn
                            icon
                            @click="closeDialog"
                            class="dialog-close-btn"
                          >
                            <v-icon icon="mdi-close" />
                          </v-btn>
                        </v-card-title>

                    <v-card-text>
                      <v-row dense>
                        <v-col cols="12">
                          <v-select
                            v-model="state.status"
                            :v-error-messages="
                              v$.status.$errors.map((e) => e.$message)
                            "
                            :items="statusOptions"
                            :label="strings.pendingApproval.approvalLabel"
                            item-title="text"
                            item-value="value"
                            variant="outlined"
                            required
                            @blur="v$.status.$touch"
                            @change="v$.status.$touch"
                          />
                        </v-col>

                            <v-col cols="12">
                              <v-textarea
                                v-model="state.approvel_message"
                                :v-error-messages="
                                  v$.approvel_message.$errors.map((e) =>
                                    String(e.$message)
                                  )
                                "
                                :label="strings.pendingApproval.messageLabel"
                                variant="outlined"
                                required
                                @blur="v$.approvel_message.$touch"
                              />
                            </v-col>

                            <v-col cols="12">
                              <v-file-input
                                class="custom-upload-icon"
                                v-model="state.attachment"
                                label="attachments"
                                variant="outlined"
                                required
                                multiple
                                show-size
                                prepend-icon="mdi-upload"
                                accept=".pdf,.doc,.docx,.png,.jpg"
                              />
                            </v-col>
                          </v-row>
                        </v-card-text>

                        <v-card-actions>
                          <v-btn variant="text" @click="closeDialog">
                            {{ strings.assignTicket.cancelButton }}
                          </v-btn>
                          <v-btn variant="tonal" @click="submitForm">
                            Submit
                          </v-btn>
                        </v-card-actions>
                      </v-card>
                    </v-dialog> -->

                  <v-dialog
                    v-model="dialogVisible"
                    max-width="500"
                  >
                    <v-card class="assign-ticket-card">
                      <v-card-title>
                        <h5>{{ strings.pendingApproval.approvalLabel }}</h5>
                        <v-btn
                          icon
                          class="dialog-close-btn"
                          @click="closeDialog"
                        >
                          <v-icon icon="mdi-close" />
                        </v-btn>
                      </v-card-title>

                      <v-card-text>
                        <v-row dense>
                          <v-col
                            cols="12"
                            class="mb-2"
                          >
                            <v-select
                              v-model="state.status"
                              :v-error-messages="
                                v$.status.$errors.map((e) => e.$message)
                              "
                              :items="statusOptions"
                              :label="strings.pendingApproval.approvalLabel"
                              disabled
                              item-title="text"
                              item-value="value"
                              variant="outlined"
                              required
                              @blur="v$.status.$touch"
                              @change="v$.status.$touch"
                            />
                          </v-col>

                          <v-col cols="12">
                            <v-textarea
                              v-model="state.approvel_message"
                              :error-messages="
                                v$.approvel_message.$errors.map((e) =>
                                  String(e.$message)
                                )
                              "
                              :label="strings.pendingApproval.messageLabel"
                              variant="outlined"
                              required
                              @blur="v$.approvel_message.$touch"
                            />
                          </v-col>

                          <!-- <v-col cols="12">
                          <v-file-input
                            v-model="state.attachment"
                            class="custom-upload-icon"
                            label="attachments"
                            variant="outlined"
                            required
                            multiple
                            show-size
                            prepend-icon="mdi-upload"
                            accept=".pdf,.doc,.docx,.png,.jpg, .txt"
                          />
                        </v-col> -->
                        </v-row>
                      </v-card-text>

                      <v-card-actions>
                        <v-btn
                          variant="text"
                          @click="closeDialog"
                        >
                          {{ strings.assignTicket.cancelButton }}
                        </v-btn>
                        <v-btn
                          variant="tonal"
                          :disabled="isSubmitting"
                          class="btn-primary"
                          @click="submitForm"
                        >
                          <span v-if="isSubmitting">
                            <i class="fa fa-spinner fa-spin" />
                            {{ strings.common.submittingLoader }}
                          </span>
                          <span v-else> {{ strings.common.submitButton }}</span>
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-dialog>

                  <v-dialog
                    v-model="deleteDialogVisible"
                    max-width="420"
                  >
                    <v-card
                      class="dialog-confirmation negative-dialog-confirmation"
                    >
                      <!-- Content -->
                      <v-card-text>
                        <div class="dialog-confirmation-icon">
                          <v-icon> mdi-alert-circle-outline </v-icon>
                        </div>
                        <h3 class="dialog-confirmation-title">
                          {{ strings.pendingApproval.cancelApprovalTitle }}
                        </h3>
                        <label class="dialog-confirmation-info mb-3">
                          {{ strings.pendingApproval.cancelApprovalText }}
                          <!-- <span class="d-block">{{ selectedUser.fullName }}?</span> -->
                        </label>
                        <div>
                          <v-textarea
                            v-model="state.cancel_message"
                            :label="strings.pendingApproval.cancelmessageLabel"
                            variant="outlined"
                            required
                          />
                        </div>
                      </v-card-text>

                      <!-- Actions -->
                      <v-card-actions>
                        <v-btn
                          variant="text"
                          @click="handleReject"
                        >
                          {{ strings.manageUser.cancelText }}
                        </v-btn>
                        <v-btn
                          variant="tonal"
                          :disabled="!state.cancel_message"
                          @click="confirmReject"
                        >
                          {{ strings.pendingApproval.rejectApprovalButton }}
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-dialog>
                </div>
              </v-col>
            </v-row>
          </v-card>
        </div>
        <div
          v-if="loadingMore"
          class="text-center my-4"
        >
          <v-progress-circular
            indeterminate
            color="primary"
          />
        </div>
        <p
          v-if="noData && tickets.length > 0"
          class="warning"
        >
          {{ strings.pendingApproval.noDataText }}
        </p>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import {
  ref,
  onMounted,
  reactive,
  defineComponent,
  onUnmounted,
  computed,
  nextTick,
  watch,
} from "vue";
import Button from "../../components/Button.vue";
import {
  getApprovalStatus,
  getLocations,
  getStatus,
  getTicketById,
  getTickets,
  getUserById,
  apiClient,
} from "../../api/apiClient";
import Batch from "../../components/Batch.vue";
import router from "../../router";
import Filter from "../../components/Filter.vue";
import { useTicketStore } from "../../stores/piniaStore";
import useVuelidate from "@vuelidate/core";
import { required } from "@vuelidate/validators";
import { useRoute } from "vue-router";
import { useToast } from "vue-toastification";
import strings from "../../../src/assets/strings.json"; // Define interfaces for your data structures

interface State {
  status: null | string | undefined;
  approvel_message: string | null;
  attachment: File[] | null;
  cancel_message: string | null;
}

export default defineComponent({
  name: "AllTickets",
  components: {
    Button,
    Batch,
    Filter,
  },
  setup() {
    // Get users
    // const user = JSON.parse(localStorage.getItem("user") || "{}");

    const state = reactive<State>({
      status: null,
      approvel_message: null,
      cancel_message: "",
      attachment: null,
    });
    const locations = ref("");
    const locationOptions = ref<{ id: string; location_name: string }[]>([]);
    const dialogVisible = ref(false);
    const tickets = ref<any>([]);
    const page = ref(1);
    const totalTickets = ref(0);
    const noData = ref(false);
    const errorMessage = ref("");
    const ticketStore = useTicketStore();
    const route = useRoute();
    const toast = useToast();
    const ticketId = Number((route.params as { ticket_id: string }).ticket_id);
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    const loading = ref(false);
    const loadingMore = ref(false);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const hasMore = ref(true);
    const pageSize = 10;
    const deleteDialogVisible = ref(false);
    const selectedTicketId = ref<number | null>(null);
    const ticketDetails = ref<any>(null);
    const searchQuery = ref<string>("");
    const statusOptions = ref<string[]>([]);
    const requestorOptions = ref<string[]>([]);
    const selectedTicket = ref({
      title: "",
      created_by_fullname: "",
      assignee: {
        value: 0,
        text: "",
      },
      ticket_id: 0,
      assigned_to: 0,
      category: "",
      subcategory: "",
    });

    const assignedOptions = ref<any>([]);
    type AssigneeOption = {
      text: string;
      value: number;
    };

    // Define the type for the ticketAssigneeOptions
    const ticketAssigneeOptions = ref<AssigneeOption[]>([]);

    const pendingTickets = computed(() =>
      tickets.value.filter((ticket: any) => ticket.status !== 1)
    );

    const fetchData = async (isInitialLoad = false) => {
      if (loading.value || loadingMore.value) return;

      if (isInitialLoad) {
        tickets.value = []; // Reset only on first load or filter change
        currentPage.value = 1; // Reset pagination
        hasMore.value = true;
      }

      loading.value = true;

      try {
        const response = await apiClient.get("tickets-all/?is_approved=1", {
          params: {
            page: currentPage.value,
            page_size: pageSize,
            role: user.role,
            user_id: user.id,
            status: 8,
            timestamp: new Date().getTime(),
          },
        });

        const newTickets = response.data.results || [];

        if (isInitialLoad) {
          tickets.value = newTickets;
        } else {
          // Filter out duplicates before adding new data
          const existingTicketIds = new Set(
            tickets.value.map((t: any) => t.ticket_id)
          );
          const uniqueTickets = newTickets.filter(
            (t: any) => !existingTicketIds.has(t.ticket_id)
          );
          tickets.value = [...tickets.value, ...uniqueTickets];
        }

        totalPages.value = response.data.total_pages;
        totalTickets.value = response.data.count;

        // Update pagination logic
        if (currentPage.value < totalPages.value) {
          currentPage.value++;
          hasMore.value = true;
        } else {
          hasMore.value = false;
        }

        noData.value = tickets.value.length === 0;
      } catch (error: any) {
        console.error("Error fetching tickets:", error);
        noData.value = true;
        if (error.response?.status === 404) {
          // Redirect to custom 404 page
          router.push("/404");
        } else if (error.response?.status === 401) {
          // Optional: handle session expiration
          router.push("/session-expired");
        } else {
          toast.error("Error loading tickets.");
        }
      } finally {
        loading.value = false;
        loadingMore.value = false;
      }
    };

    const openDialog = async (id: number) => {
      dialogVisible.value = true;
      loading.value = true;
      try {
        const ticketData = await getTicketById({ id });
        ticketDetails.value = { ...ticketData };
        state.status = ticketData.status_name;
        state.approvel_message = ticketData.approvel_message || "";
        state.attachment = ticketData.attachment || [];
      } catch (error: any) {
        console.error("Error submitting form:", error);

        // Check if the error has a response from the server
        if (
          error.response &&
          error.response.data &&
          error.response.data.message
        ) {
          toast.error(`Error: ${error.response.data.message}`);
        } else {
          toast.error(`Error submitting form: ${error.message}`);
        }
      } finally {
        loading.value = false;
      }
    };

    const closeDialog = () => {
      dialogVisible.value = false;
      v$.value.$reset();
    };

    const openDeleteDialog = (ticketId: number) => {
      selectedTicketId.value = ticketId;
      deleteDialogVisible.value = true;
    };
   const confirmReject = async () => {
  if (!selectedTicketId.value) return;

  await rejectTicket(selectedTicketId.value); // use existing function
  selectedTicketId.value = null;
  state.cancel_message = "";
  deleteDialogVisible.value = false;
};

  const rejectTicket = async (ticket_id: number) => {
  loading.value = true;
  try {
    const { data: ticketData } = await apiClient.get(`tickets/${ticket_id}/`);
    if (!ticketData) throw new Error("Ticket data is null or undefined");

    const payload = {
      ...state,
      is_approved: 0,
      status: 10, // Rejected
      assigned_to: user.id,
      updated_by_id: user.id,
    };

    await apiClient.put(`ticket-approvalstatus/${ticket_id}/`, payload);

    toast.success("Ticket rejected successfully!");

    const { data: response } = await apiClient.get("tickets-all/?is_approved=1", {
      params: {
        page: 1,
        page_size: pageSize,
        role: user.role,
        user_id: user.id,
        status: 8,
        timestamp: new Date().getTime(), // prevent caching
      },
    });

    tickets.value = response.results || [];
    totalTickets.value = response.count;
    await nextTick();
  } catch (error: any) {
    console.error("Error rejecting ticket:", error);
    toast.error(error.response?.data?.message || "Failed to reject the ticket.");
  } finally {
    deleteDialogVisible.value = false;
    loading.value = false;
  }
};

  const handleReject = () => {
  deleteDialogVisible.value = false;
  selectedTicketId.value = null;
  state.cancel_message = "";
};

    const rules = {
      status: { required },
      approvel_message: { required },
      // cancel_message: { required },
    };
    const v$ = useVuelidate(rules, state);
    const isSubmitting = ref(false); // State for loader

    const submitForm = async () => {
      v$.value.$validate();
      if (!v$.value.$error) {
        try {
          isSubmitting.value = true; // Start loader

          const apiUrl = `ticket-approvalstatus/${ticketDetails.value.ticket_id}/`;
          const formData = new FormData();
          formData.append("status", String(state.status || 1));
          formData.append("approvel_message", state.approvel_message || "");
          formData.append("approved_by", state.cancel_message || "");

          if (state.attachment) {
            state.attachment.forEach((file) => {
              formData.append("attachment", file);
            });
          }

          // const payload = {
          //   ...state,
          //   title: ticketDetails.value.title,
          //   description: ticketDetails.value.description,
          //   project: ticketDetails.value.project,
          //   ticket_id: ticketDetails.value.ticket_id,
          //   approved_by_id: user.id,
          //   status: 1,
          //   assigned_to: null,
          //   updated_by_id: user.id,
          // };
          const payload = {
            ...state,
            is_approved: 0,
            status: 1,
            assigned_to: null,
            updated_by_id: user.id,
          };

          const response = await apiClient.put(apiUrl, payload);

          // ✅ Remove the updated ticket from the UI
          tickets.value = tickets.value.filter(
            (ticket: any) => ticket.ticket_id !== ticketDetails.value.ticket_id
          );
          closeDialog(); // Close the modal after success
          await fetchData(); // Refresh data

          toast.success(response.data.message);
        } catch (error: any) {
          console.error("Error submitting form:", error);

          if (error.response?.data?.message) {
            toast.error(`Error: ${error.response.data.message}`);
          } else {
            toast.error(`Error submitting form: ${error.message}`);
          }
        } finally {
          isSubmitting.value = false; // Stop loader after API response
        }
      } else {
        toast.error("Please correct the errors before submitting.");
      }
    };

    const handleScroll = () => {
      if (!hasMore.value || loadingMore.value) return; // Prevent multiple calls

      const scrollPosition = window.innerHeight + window.scrollY;
      const documentHeight = document.documentElement.scrollHeight;

      if (scrollPosition >= documentHeight - 100) {
        fetchData(); // Load next page
      }
    };

    onMounted(() => {
      fetchData(); // Load first page
      window.addEventListener("scroll", handleScroll);
    });

    onUnmounted(() => {
      window.removeEventListener("scroll", handleScroll);
    });

    onMounted(() => {
      fetchData(true);
    });

    const editTicketPage = (ticket_id: number) => {
      ticketStore.setStatusOptions(statusOptions.value); // Store data globally
      router
        .push({
          name: "/TicketManagement/pendingEditTicket",
          params: { ticket_id }, // Convert to string as route params are strings
        })
        .catch((err) => {
          if (err.name !== "NavigationDuplicated") {
            console.error("Navigation Error:", err);
          }
        });
    };
    const filters = ref([
      {
        id: "status",
        placeholder: "-- Select Status --",
        options: [],
      },
      {
        id: "location",
        placeholder: "-- Select Location --",
        options: [],
      },
      {
        id: "created_by",
        placeholder: "-- Requster --",
        options: [],
      },
      {
        id: "assigned_to",
        placeholder: "-- Assigned --",
        options: [],
      },
    ]);

    const searchValue = ref<string>("");

    const filterParams = ref<Record<string, string | number | null>>({});

    return {
      tickets,
      locations,
      locationOptions,
      // filters,
      searchQuery,
      statusOptions,
      requestorOptions,
      assignedOptions,
      ticketAssigneeOptions,
      editTicketPage,
      searchValue,
      filters,
      dialogVisible,
      selectedTicket,
      loading,
      totalTickets,
      state,
      v$,
      openDialog,
      closeDialog,
      submitForm,
      isSubmitting,
      ticketDetails,
      loadingMore,
      noData,
      errorMessage,
      user,
      // approveTicket,
      rejectTicket,
      strings,
      pendingTickets,
      deleteDialogVisible,
      openDeleteDialog,
      handleReject,
      confirmReject,
    };
  },

  data: () => ({
    items: [
      { title: "Home", disabled: false, href: "/ticket-management" },
      {
        title: "Ticket Management",
        disabled: false,
        href: "/all-tickets",
      },
      {
        title: "Pending Approval",
        disabled: false,
        href: "",
      },
    ],
    // dialog: false,
  }),
  methods: {
    hasApprovalAccess(ticketAssignedTo: unknown) {
      const isSameAssignee = this.user.id === ticketAssignedTo;

      return (
        this.user.role === "R003" || // Project Manager
        (this.user.role === "R001" && isSameAssignee) // SuperAdmin but only if they are the assignee
      );
    },
  },
});
</script>

<style>
.custom-breadcrumbs {
  color: #8c8b90;
  font-size: 12px;
}

.v-breadcrumbs {
  padding: 0% !important;
}

.v-breadcrumbs--density-default {
  padding: 0% !important;
}

.breadcrums-heading {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between !important;
  /* width: 77%; */
}
</style>

<style scoped>
h1 {
  font-size: 27px;
  font-weight: 700;
  margin: 0;
}

.card {
  background: white;
  border: 2px solid #d8d8d8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  /* width: 77%; */
  padding: 12px 24px;
  cursor: pointer;
}

.card:hover {
  background-color: #f9f9f9;
  transition: background-color 0.3s, color 0.3s;
}

.hover-content {
  display: none;
}

.assign-ticket-card {
  border-radius: 8px;
}

.dialog-title {
  font-size: 1.25rem;
  /* font-weight: bold; */
  color: white;
  background-color: #1565c0;
  /* Matches the blue header color in the image */
  padding: 12px;
}

/* .cancel-btn {
    background-color: #e0e0e0;
    color: #757575;
  }

  .submit-btn {
    background-color: #4caf50;
    color: white;
  } */

/* .card:hover,
  .hover-content {
    display: block;
    position: absolute;
    z-index: 1000;
    width: 100%;
    height: 150px;
  } */
.card-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.custom-card-item {
  padding: 10px;
  display: flex;
  /* justify-content: space-between !important; */
  align-items: center;
  width: 100%;
  gap: 40px;
}

.custom-card {
  /* padding: 6px; */
  display: flex;
  gap: 4px;
  flex-direction: column;
}

.custom-card span {
  font-size: 14px;
  font-weight: 500;
  color: #8c8c8c;
}

.custom-card p {
  font-size: 14px;
  font-weight: 500;
  color: #2e2e2e;
}

.filter-fields {
  display: flex;
  flex-wrap: wrap;
  /* align-items: center; */
  justify-content: space-evenly;
}

.action-btn {
  display: flex;
  flex-direction: row;
  /* justify-content: space-between; */
  gap: 12px;
}

.view-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #026bb1;
  border-radius: 50%;
  color: #026bb1;
}

.edit-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #2dcc70;
  border-radius: 50%;
  color: #2dcc70;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: 2px solid orangered;
  border-radius: 50%;
  color: orangered;
}

.custom-btn {
  background: none;
  border: 1px solid #2dcc70;
  border-radius: 5px;
  color: #2dcc70;
  box-shadow: none !important;
  margin: 0;
  text-transform: none;
}

.high-btn {
  background: none;
  border: 1px solid #ff7780;
  border-radius: 50px;
  color: #ff7780;
  box-shadow: none !important;
  margin: 0;
  padding: 0%;
  text-transform: none;
  max-width: 57px;
}

.filters-wrapp {
  margin-top: 20px;
  /* width: 77%; */
  display: flex;
  flex-wrap: wrap;
  background-color: #f9f9f9;
  padding: 12px;
  align-items: center;
  gap: 12px;
}

.search-icon {
  background: #2dcc70;
  color: #fff;
  width: 38px;
  height: 38px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* .search-icon:hover {
    color: #2dcc70;
    background: #fff;
  } */
.cached-icon {
  background: #e6e6e6;
  color: #b5b5b5;
  width: 38px;
  height: 38px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* Custom styles for inputs */

input,
.v-select__selections,
.v-text-field input {
  background: transparent;
  outline: none;
  box-shadow: none;
  font-size: 14px;
}

.v-select__control,
.v-text-field__control {
  border-radius: 12px;
  /* Same border radius for select */
  border: 1px solid #d8d8d8;
  /* Same border color */
}

.v-select__control:focus,
.v-text-field__control:focus {
  border-color: #2dcc70;
  /* Same focus color */
}

.v-icon {
  font-size: 14px;
}

.v-list-item__append > .v-icon {
  font-size: 14px !important;
}
</style>
