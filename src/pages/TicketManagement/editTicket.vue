<template>
  <div class="page-wrapper page-edit-ticket">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row align="center" justify="space-between">
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">
              {{ ticketDetails.title }}
            </h2>
            <v-chip size="x-small" class="data-count ml-2">
              {{ strings.editTicket.ticketId }} -
              {{ ticketDetails.ticket_id }}
            </v-chip>
          </div>
          <!-- <v-breadcrumbs
            :items="items"
            class="breadcrumbs"
          >
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs> -->
        </v-col>
        <v-col cols="auto" class="page-action">
          <div class="btn-toolbar">
            <v-btn variant="outlined" class="btn-outlined-dark" @click="goBack">
              <v-icon left> mdi-chevron-left </v-icon
              >{{ strings.viewUser.buttons.back }}
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Page Content Section -->
    <section
      class="page-inner-content page-content-wrapper page-two-col-layout fixed-right-sidebar"
    >
      <div class="page-inner-layout">
        <div class="page-left-column">
          <!-- Ticket Current Info -->
          <div class="ticket-current-info mb-4">
            <v-row align="center" justify="space-evenly">
              <v-col sm="12" lg="auto">
                <label class="ticket-data-label">{{
                  strings.editTicket.currentAssigneeName
                }}</label>
                <div class="ticket-data-value">
                  {{ ticketDetails?.assigned_to_fullname || "--N/A--" }}
                  <!-- Adding console log for debugging -->
                  <span>{{
                    console.log("Object", ticketDetails?.cancelled_by_name)
                  }}</span>
                </div>
              </v-col>
              <v-col sm="12" lg="auto">
                <label class="ticket-data-label">{{
                  strings.editTicket.currentStatusName
                }}</label>
                <div class="ticket-data-value">
                  {{ ticketDetails.status_name }}
                </div>
              </v-col>
            </v-row>
          </div>

          <!-- Tab Begin Here -->
          <v-card class="custom-tab-wrapper">
            <v-tabs v-model="tab">
              <v-tab value="tab-ticket-info">
                {{ strings.editTicket.editTicketTabTitles.ticketInfo }}
              </v-tab>
              <v-tab value="tab-stats">
                {{ strings.editTicket.editTicketTabTitles.ticketDescription }}
              </v-tab>
              <v-tab value="tab-tracking">
                {{ strings.editTicket.editTicketTabTitles.ticketTracking }}
              </v-tab>
            </v-tabs>

            <v-card-text>
              <v-tabs-window v-model="tab">
                <v-tabs-window-item value="tab-ticket-info">
                  <div class="tab-content-wrapper">
                    <div class="ticket-desc-primary">
                      <v-row justify="center">
                        <v-col md="12" lg="auto">
                          <!-- Approved Alert -->
                          <div
                            v-if="getApprovalDate(ticketDetails)"
                            class="ticket-approval-alert ticket-approval-success"
                          >
                            <span class="ticket-approval-alert-icon">
                              <v-icon icon="mdi-check-decagram" />
                            </span>
                            <div class="ticket-approval-alert-content">
                              {{ strings.editTicket.viewApprovalMessage }}
                            </div>
                          </div>
                          <!-- Cancel Alert -->
                          <div
                            v-else-if="getCancellationDate(ticketDetails)"
                            class="ticket-approval-alert ticket-approval-failure"
                          >
                            <span class="ticket-approval-alert-icon">
                              <v-icon icon="mdi-close-circle" />
                            </span>
                            <div class="ticket-approval-alert-content">
                              {{ strings.editTicket.viewCancelMessage }}
                            </div>
                          </div>
                        </v-col>
                      </v-row>
                      <!-- Approved Info -->
                      <ul
                        v-if="getApprovalDate(ticketDetails)"
                        class="custom-list-group ticket-desc-approve-info mb-5"
                      >
                        <li>
                          <v-row justify="space-between">
                            <v-col md="12" lg="4">
                              <label class="label-name">
                                {{
                                  strings.editTicket.ticketDescriptionLabel
                                    .approvedBy
                                }}
                              </label>
                              <div class="label-value">
                                {{ ticketDetails.approved_by_name || "N/A" }}
                              </div>
                            </v-col>
                            <v-col md="12" lg="4">
                              <label class="label-name">{{
                                strings.editTicket.approvedOn
                              }}</label>
                              <div class="label-value">
                                {{ formatDate(getApprovalDate(ticketDetails)) }}
                              </div>
                            </v-col>
                          </v-row>
                        </li>
                        <li v-if="ticketDetails.approvel_message">
                          <v-row>
                            <v-col md="12" lg="auto">
                              <label class="label-name">
                                {{
                                  strings.editTicket.ticketDescriptionLabel
                                    .approvalMessage
                                }}
                              </label>
                              <div class="label-value">
                                {{ ticketDetails.approvel_message }}
                              </div>
                            </v-col>
                          </v-row>
                        </li>
                      </ul>
                      <!-- Cancel Info -->
                      <ul
                        v-else-if="getCancellationDate(ticketDetails)"
                        class="custom-list-group ticket-desc-cancel-info mb-5"
                      >
                        <li>
                          <v-row justify="space-between">
                            <v-col md="12" lg="auto">
                              <label class="label-name">{{
                                strings.editTicket.cancelledBy
                              }}</label>
                              <div class="label-value">
                                {{ ticketDetails.cancelled_by_name || "N/A" }}
                              </div>
                            </v-col>
                            <!-- Cancel On -->
                            <v-col md="12" lg="auto">
                              <label class="label-name">{{
                                strings.editTicket.cancelledOn
                              }}</label>
                              <div class="label-value">
                                {{
                                  formatDate(getCancellationDate(ticketDetails))
                                }}
                              </div>
                            </v-col>
                            <!-- (Optional) Reason -->
                          </v-row>
                        </li>
                        <li>
                          <v-row>
                            <v-col v-if="ticketDetails.cancel_message" lg="12">
                              <label class="label-name">
                                {{
                                  strings.editTicket.ticketDescriptionLabel
                                    .cancelReason
                                }}
                              </label>
                              <div class="label-value">
                                {{ ticketDetails.cancel_message }}
                              </div>
                            </v-col>
                          </v-row>
                        </li>
                      </ul>
                      <ul class="custom-list-group">
                        <li>
                          <!-- Watcher List Begin Here -->
                          <div class="ticket-watchers-list">
                            <h6>{{ strings.editTicket.watchers }}:</h6>
                            <div class="ticket-watcher-content">
                              <template v-if="watchersSummary.count <= 1">
                                {{ watchersSummary.text }}
                              </template>
                              <template v-else>
                                <div class="custom-tooltip-wrapper">
                                  <span class="watchers-summary">
                                    {{ watchersSummary.firstWatcher }}
                                  </span>
                                  <span class="watchers-count">
                                    +{{ watchersSummary.count - 1 }}
                                  </span>

                                  <!-- Custom Tooltip with Separate Names -->
                                  <div class="custom-tooltip-content">
                                    <ul>
                                      <li
                                        v-for="(
                                          watcher, index
                                        ) in watchersSummary.list"
                                        :key="index"
                                        class="tooltip-name-label watcher-list-item"
                                      >
                                        <span>{{ watcher }}</span>
                                        <!-- <v-icon
                                          small
                                          color="red"
                                          class="watcher-remove-icon"
                                          @click="removeWatcherByIndex(index)"
                                          >mdi-close</v-icon
                                        > -->
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <v-btn
                                  icon
                                  size="small"
                                  class="btn-outlined-light watchers-edit-btn"
                                  variant="outlined"
                                  @click="openWatchersDialog"
                                >
                                  <v-icon>mdi-pencil</v-icon>
                                </v-btn>
                              </template>
                            </div>
                          </div>
                          <!-- Watcher List End Here -->
                          <v-row
                            align="center"
                            justify="space-between"
                            class="mt-1"
                          >
                            <v-col md="12" lg="4">
                              <label class="label-name">{{
                                strings.editTicket.projectName
                              }}</label>
                              <div class="label-value">
                                {{ ticketDetails.project_name }}
                              </div>
                            </v-col>
                            <v-col md="12" lg="4">
                              <label class="label-name">{{
                                strings.editTicket.location
                              }}</label>
                              <div class="label-value">
                                {{ ticketDetails.location_name }}
                              </div>
                            </v-col>
                            <v-col md="12" lg="4">
                              <label class="label-name">{{
                                strings.editTicket.priority
                              }}</label>
                              <div class="label-value">
                                {{ ticketDetails.priority_names }}
                              </div>
                            </v-col>
                          </v-row>
                        </li>
                        <li>
                          <v-row>
                            <v-col md="12" lg="4">
                              <div>
                                <label class="label-name">{{
                                  strings.editTicket.ticketDescriptionLabel
                                    .createdBy
                                }}</label>
                                <div class="label-value">
                                  {{ ticketDetails.created_by_fullname }}
                                </div>
                              </div>
                            </v-col>
                            <v-col md="12" lg="4">
                              <label class="label-name">{{
                                strings.editTicket.createDate
                              }}</label>
                              <div class="label-value">
                                {{ formatDate(ticketDetails.created_at) }}
                              </div>
                            </v-col>
                            <v-col md="12" lg="4">
                              <label class="label-name">{{
                                strings.editTicket.updateDate
                              }}</label>
                              <div class="label-value">
                                {{ formatDate(ticketDetails.updated_at) }}
                              </div>
                            </v-col>
                          </v-row>
                        </li>
                        <li>
                          <v-row align="center" justify="space-between">
                            <v-col lg="12">
                              <label class="label-name">{{
                                strings.editTicket.ticketDescriptionLabel
                                  .documents
                              }}</label>
                              <div class="mt-1 batch-document-wrapper">
                                <template
                                  v-if="
                                    ticketDetails.attachement &&
                                    ticketDetails.attachement.length
                                  "
                                >
                                  <Batch
                                    v-for="(
                                      doc, index
                                    ) in ticketDetails.attachement"
                                    :key="index"
                                    class="batch-documents"
                                  >
                                    <a :href="doc.attachement" target="_blank">
                                      {{ getFileName(doc.original_filename) }}
                                    </a>
                                  </Batch>
                                </template>
                                <template v-else>
                                  <div class="batch-documents mt-0 label-value">
                                    {{ strings.editTicket.nodata }}
                                  </div>
                                </template>
                              </div>
                            </v-col>
                          </v-row>
                        </li>
                      </ul>
                    </div>

                    <v-row class="mt-4">
                      <v-col>
                        <h6 class="tab-section-title">
                          {{
                            strings.editTicket.ticketDescriptionLabel
                              .ticketComments
                          }}
                        </h6>
                        <Chatbox :watchers="allUsersList" />
                      </v-col>
                    </v-row>
                  </div>
                </v-tabs-window-item>

                <v-tabs-window-item value="tab-stats">
                  <div class="tab-content-wrapper">
                    <div class="ticket-desc-primary">
                      <ul class="custom-list-group">
                        <li>
                          <v-row>
                            <v-col>
                              <label class="label-name">{{
                                strings.editTicket.ticketDescriptionLabel
                                  .description
                              }}</label>
                              <div class="label-value description-text">
                                {{ ticketDetails.description }}
                              </div>
                            </v-col>
                          </v-row>
                        </li>
                      </ul>
                      <ul class="custom-list-group mt-5">
                        <li>
                          <v-row>
                            <v-col
                              v-if="ticketDetails?.justification?.trim().length"
                            >
                              <label class="label-name">{{
                                strings.editTicket.ticketDescriptionLabel
                                  .justification
                              }}</label>
                              <div class="label-value">
                                {{ ticketDetails.justification || "N/A" }}
                              </div>
                            </v-col>
                          </v-row>
                        </li>
                      </ul>

                      <!-- Cancel block: only if there *is* a cancellation date -->
                      <!-- <ul
                        class="custom-list-group ticket-desc-cancel-info mt-4"
                        v-else-if="getCancellationDate(ticketDetails)"
                      >
                        <li>
                          <v-row>
                            <v-col md="12" lg="8">
                              <label class="label-name">
                                {{
                                  strings.editTicket.ticketDescriptionLabel
                                    .cancelReason
                                }}
                              </label>
                              <div class="label-value">
                                {{ ticketDetails.cancel_message || "N/A" }}
                              </div>
                            </v-col>
                            <v-col md="12" lg="4">
                              <label class="label-name">Cancelled On</label>
                              <div class="label-value">
                                {{
                                  formatDate(getCancellationDate(ticketDetails))
                                }}
                              </div>
                            </v-col>
                          </v-row>
                        </li>
                      </ul> -->
                    </div>
                  </div>
                </v-tabs-window-item>

                <v-tabs-window-item value="tab-tracking">
                  <v-timeline align="start" dense>
                    <v-timeline-item
                      v-for="(item, index) in statusOptions"
                      :key="index"
                      :dot-color="getStatusColor(item.current_status || item)"
                      size="small"
                    >
                      <v-card variant="plain">
                        <v-card-title>
                          {{ getStatusText(item.current_status) }}
                        </v-card-title>
                        <v-card-subtitle>
                          {{ strings.editTicket.ticketTrackingLabel.updatedBy }}
                          {{ item.updated_by || "Unknown" }}
                        </v-card-subtitle>
                        <v-card-text class="timeline-card-text mt-2">
                          <div class="d-block">
                            <label class="d-block">{{
                              strings.editTicket.ticketTrackingLabel.createdBy
                            }}</label>
                            <strong class="d-block">{{
                              item.created_by
                            }}</strong>
                          </div>
                          <div class="d-block mt-2">
                            <label class="d-block">{{
                              strings.editTicket.ticketTrackingLabel.description
                            }}</label>
                            <p class="d-block text-left">
                              {{ getStatusDescription(item.current_status) }}
                            </p>
                          </div>
                          <div class="d-block mt-2">
                            <label class="d-block">{{
                              strings.editTicket.ticketTrackingLabel.date
                            }}</label>
                            <strong>{{ formatDate(item.created_at) }}</strong>
                          </div>
                        </v-card-text>
                      </v-card>
                    </v-timeline-item>
                  </v-timeline>
                </v-tabs-window-item>
              </v-tabs-window>
            </v-card-text>
          </v-card>
          <!-- Tab End Here -->
        </div>
        <div class="page-right-column">
          <v-card class="edit-ticket-fixed-card">
            <v-card-text>
              <div class="row">
                <div class="col-12">
                  <div class="custom-select">
                    <v-select
                      v-model="state.category"
                      :disabled="isReadonly"
                      :error-messages="
                        v$.category.$errors.map((e) => String(e.$message))
                      "
                      :items="categories"
                      item-title="text"
                      item-value="value"
                      :label="strings.editTicket.category"
                      variant="outlined"
                      dense
                      required
                      class="select-field small-select"
                      @blur="v$.category.$touch"
                      @change="v$.category.$touch"
                      @input="v$.category.$touch"
                      @update:model-value="updateSubcategories"
                    />
                  </div>
                </div>
              </div>
              <div class="row mt-4">
                <div class="col-12">
                  <div class="custom-select">
                    <v-select
                      v-model="state.subcategory"
                      :disabled="isReadonly"
                      :error-messages="
                        v$.subcategory.$errors.map((e) => String(e.$message))
                      "
                      :items="subcategories"
                      item-title="text"
                      item-value="value"
                      :label="strings.editTicket.subcategory"
                      variant="outlined"
                      dense
                      required
                      class="select-field small-select"
                      @blur="v$.subcategory.$touch"
                      @change="v$.subcategory.$touch"
                      @input="v$.subcategory.$touch"
                    />
                  </div>
                </div>
              </div>
              <div class="row mt-4">
                <div class="col-12">
                  <div class="custom-select">
                    <v-select
                      v-model="state.status"
                      :disabled="isReadonly"
                      :v-error-messages="
                        v$.status.$errors.map((e) => String(e.$message))
                      "
                      :items="status"
                      item-title="text"
                      item-value="value"
                      :label="strings.editTicket.status"
                      variant="outlined"
                      dense
                      required
                      class="select-field small-select"
                      @blur="v$.status.$touch"
                      @change="v$.status.$touch"
                    >
                      <template #selection="{ item }">
                        <span v-if="item">
                          {{ getItemText(item) }}
                        </span>
                        <span v-else>
                          {{ getStatusName(Number(state.status) || 0) }}
                        </span>
                      </template>
                    </v-select>
                  </div>
                </div>
              </div>
              <div class="row mt-4">
                <div class="col-12">
                  <div class="custom-select">
                    <v-autocomplete
                      v-model="state.assigned_to"
                      :disabled="isReadonly"
                      :error-messages="
                        v$.assigned_to.$errors.map((e) => String(e.$message))
                      "
                      :items="assigned_to"
                      item-title="text"
                      item-value="value"
                      :label="strings.editTicket.assignee"
                      variant="outlined"
                      density="compact"
                      required
                      :searchable="true"
                      class="select-field small-select"
                      @blur="v$.assigned_to.$touch"
                      @change="v$.assigned_to.$touch"
                      @input="v$.assigned_to.$touch"
                    />
                  </div>
                </div>
              </div>
              <div v-if="isAdmin" class="row mt-4">
                <div class="col-12">
                  <v-text-field
                    id="due-date"
                    v-model="state.due_date"
                    :disabled="isReadonly"
                    type="datetime-local"
                    :min="minDateTime"
                    :v-error-messages="
                      v$.due_date.$errors.map((e) => String(e.$message))
                    "
                    :items="state.due_date"
                    variant="outlined"
                    dense
                    required
                    label="Due Date"
                    class="select-field small-select custom-due-date-field"
                    @blur="v$.due_date.$touch"
                    @change="handleDueDateChange"
                  />
                  <!-- <div class="custom-form-field-wrapper">
                    <div for="due-date">Due Date</div>

                    <v-text-field
  v-model="state.due_date"
  :disabled="isEmployee"
  type="datetime-local"
  label="Due Date"
  variant="outlined"
  dense
  class="select-field small-select"
/>
                  </div> -->
                </div>
              </div>
              <div class="row mt-5">
                <div class="col-12">
                  <div v-if="showExtensionField">
                    <v-textarea
                      id="due-date-extension"
                      v-model="state.due_expiry_reason"
                      :error-messages="
                        v$.due_expiry_reason.$errors.map((e) =>
                          String(e.$message)
                        )
                      "
                      :readonly="readonlyDueExtension"
                      :disabled="isReadonly"
                      :label="strings.editTicket.dueDateExtensionLabel"
                      variant="outlined"
                      dense
                      required
                      class="select-field small-select"
                    />
                  </div>
                </div>
              </div>
              <div class="btn-toolbar mt-4 justify-center">
                <!-- If the user is an Employee -->
                <template
                  v-if="ticketDetails.status === 6 && isCreatedByLoggedInUser"
                >
                  <!-- Show 'Closed' button if the ticket is 'solved' -->
                  <v-btn variant="tonal" @click="handleClosed">
                    {{ strings.common.closeButton }}
                  </v-btn>

                  <!-- Show 'Reopen' button if the ticket is 'closed' -->
                  <v-btn variant="tonal" @click="handleReopen">
                    {{ strings.common.reopenButton }}
                  </v-btn>
                </template>

                <!-- If the user is NOT an Employee -->
                <template v-else>
                  <v-btn
                    v-if="!isReadonly"
                    variant="text"
                    @click="handleCancel"
                  >
                    {{ strings.editTicket.cancelButton }}
                  </v-btn>
                  <v-btn
                    v-if="!loading && !isReadonly"
                    variant="tonal"
                    :disabled="!isFormModified"
                    :style="
                      loading
                        ? { backgroundColor: '#2dcc70', color: '#fff' }
                        : {}
                    "
                    @click="handleSubmit"
                  >
                    {{ strings.editTicket.submitButton }}
                    <template #loader>
                      <v-progress-circular
                        indeterminate
                        size="20"
                        class="spinner"
                      />
                    </template>
                  </v-btn>
                </template>
              </div>
            </v-card-text>
          </v-card>
          <template>
            <v-dialog v-model="showFeedbackDialog" max-width="500px">
              <v-card class="feedback-card">
                <!-- Header -->
                <v-card-title class="feedback-header">
                  <span>{{ strings.feedback.title }}</span>
                </v-card-title>

                <!-- Description -->
                <v-card-text class="feedback-text">
                  <p>
                    {{ strings.feedback.description }}
                  </p>
                </v-card-text>

                <!-- Emoji Rating System -->
                <v-container class="emoji-container">
                  <v-row class="justify-center">
                    <v-col
                      v-for="(feedback, index) in feedbackValues"
                      :key="index"
                      cols="auto"
                      class="emoji-col"
                    >
                      <v-btn
                        class="emoji-btn"
                        :class="{
                          selected: selectedFeedback === feedback.value_id,
                        }"
                        @click="selectFeedback(feedback)"
                      >
                        <span class="emoji">{{ feedback.emoji }}</span>
                      </v-btn>
                      <p class="emoji-label">
                        {{ feedback.label }}
                      </p>
                    </v-col>
                  </v-row>
                  <v-form ref="feedbackForm" v-model="feedbackFormIsValid">
                    <v-row>
                      <v-col>
                        <v-textarea
                          v-model="feedbackState.feedbackReason"
                          label="Reason"
                          variant="outlined"
                          :rules="feedbackRules"
                          dense
                          required
                          class="mb-4 my-textarea"
                        />
                      </v-col>
                    </v-row>
                  </v-form>
                </v-container>

                <!-- Submit Button -->
                <v-container class="submit-container">
                  <v-btn class="submit-button" @click="submitFeedback">
                    {{ strings.feedback.submitButton }}
                  </v-btn>
                </v-container>
              </v-card>
            </v-dialog>
          </template>
          <v-dialog
            :model-value="showWatchersDialog"
            @update:model-value="(val) => (showWatchersDialog = val)"
            max-width="500px"
          >
            <v-card class="watchers-dialog-card">
              <v-card-title class="watchers-dialog-title">
                Edit Watchers
              </v-card-title>
              <v-card-text class="watchers-dialog-content">
                <div class="current-watchers-list">
                  <h5>Current Watcher(s)</h5>
                  <ul v-if="currentWatchersList.length > 0">
                    <li
                      v-for="(watcher, index) in currentWatchersList"
                      :key="index"
                    >
                      <label>{{ getWatcherDisplayName(watcher) }}</label>
                      <button @click="removeWatcherByIndex(index)">
                        <span class="mdi mdi-delete-circle-outline"></span>
                      </button>
                    </li>
                  </ul>
                  <p v-else class="no-watchers">No watchers</p>
                </div>
                <h5>Add Watcher(s)</h5>
                <v-combobox
                  v-model="editableWatcherObjects"
                  :items="allUsersList"
                  item-title="text"
                  item-value="value"
                  chips
                  multiple
                  closable-chips
                  outlined
                  label="Add Watchers"
                  class="watchers-autocomplete"
                  variant="outlined"
                  @update:model-value="handleWatchersChange"
                />
              </v-card-text>
              <v-card-actions class="watchers-dialog-actions">
                <v-spacer />
                <v-btn variant="text" @click="showWatchersDialog = false"
                  >Cancel</v-btn
                >
                <v-btn color="primary" @click="saveWatchers">Save</v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  reactive,
  ref,
  computed,
  watch,
} from "vue";
import { useVuelidate } from "@vuelidate/core";
import { minLength, required, helpers } from "@vuelidate/validators";
import { useToast } from "vue-toastification";
import router from "../../router";
import Button from "../../components/Button.vue";
import Batch from "../../components/Batch.vue";
import {
  getTicketByChat,
  getTicketById,
  apiClient,
  ticketStatus,
  getStatus,
  users,
  getStatusTracking,
  getFeedbackValues,
  createFeedback,
  getAllCategories,
} from "../../api/apiClient";
import { useRoute } from "vue-router";
import strings from "../../../src/assets/strings.json";
import Chatbox from "../../components/Chatbox.vue";
import { now } from "lodash-es";

interface State {
  category: any;
  subcategory: any;
  assigned_to: string | null;
  priority: string | null;
  status: string | "";
  due_date: string | null;
  due_expiry_reason: string | null;
}

interface Feedback {
  value_id: number;
  emoji: string;
  label: string;
}

export default defineComponent({
  name: "EditTicket",
  components: {
    Button,
    Batch,
    Chatbox,
  },
  setup() {
    const toast = useToast();
    const categories = ref<any[]>([]);
    const subcategories = ref<any[]>([]);
    const statuses = ref<any>([]); // Holds statuses from API
    const status = ref<any>([]);
    const priority = ref<any>([]);
    const assigned_to = ref<any>([]);
    const loading = ref<boolean>(false);
    const showTable = ref(false);
    const route = useRoute();
    const isFromAssignTicket = computed(
      () => route.query.source === "assign-ticket"
    );
    const currentUser = ref({ id: 0 });
    const showFeedbackDialog = ref(false);
    const feedbackValues = ref<Feedback[]>([]);
    const selectedFeedback = ref<number | null>(null);
    const minDateTime = ref("");
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    const showExtensionField = ref(false);
    const due_date = ref<string | null>(null);
    const isEmployee = computed(() =>
      ["R003", "R005", "R004"].includes(user.role)
    );
    const isDueDateModified = ref(false); // Track if the user modified the due date
    const previousValidDueDate = ref<string | null>(null);
    const originalDueDate = ref<string | null>(null);
    const feedbackState = reactive({
      feedbackReason: "",
    });
    const isAdmin = computed(() =>
      ["R001", "R002", "R006"].includes(user.role)
    );
    const readonlyDueExtension = ref(true);
    const isReadonly = computed(() => {
      return (
        isEmployee.value ||
        (ticketDetails.value.status === 8 &&
          ticketDetails.value.assigned_to !== user.id) ||
        ticketDetails.value.status === 7
      );
    });

    const comments = ref<Comment[]>([]);

    const isCreatedByLoggedInUser = computed(() => {
      return ticketDetails.value.created_by.id === user.id;
    });
    const feedbackFormIsValid = ref(true);
    const feedbackForm = ref();
    const allUsersList = ref<any[]>([]); // full unfiltered user list

    const feedbackRules = computed(() => {
      const requiredFor = [1, 2];

      return [
        (value: string) => {
          if (requiredFor.includes(selectedFeedback.value || 0)) {
            return (
              (!!value && value.trim().length > 0) || "*Reason is required."
            );
          }
          return true;
        },
        (value: string) => {
          if (requiredFor.includes(selectedFeedback.value || 0)) {
            return (
              value?.length >= 100 || "*Reason must be at least 100 characters."
            );
          }
          return true;
        },
      ];
    });

    const handleCancel = () => {
      // Reset form state to initial state
      Object.assign(state, JSON.parse(JSON.stringify(initialState)));
      // Explicitly reset the due date to the initial due date
      state.due_date = initialState.due_date;
      // Hide the Due Date Extension field
      if (
        ticketDetails.value.due_expiry_reason &&
        ticketDetails.value.due_expiry_reason.trim() !== ""
      ) {
        showExtensionField.value = true;
        readonlyDueExtension.value = true;
      } else {
        showExtensionField.value = false;
        readonlyDueExtension.value = false;
        state.due_expiry_reason = null; // Also clear the field if not needed
      }

      // Ensure subcategory resets correctly (convert ID to full object)
      if (state.category) {
        const selectedCategory = categories.value.find(
          (cat) => cat.value === state.category
        );
        if (selectedCategory) {
          subcategories.value = selectedCategory.subcategories;
          const matchedSubcategory = subcategories.value.find(
            (sub) => sub.value === state.subcategory
          );
          state.subcategory = matchedSubcategory
            ? matchedSubcategory.value
            : null;
        }
      } else {
        state.subcategory = null;
      }
    };

    const canReopen = computed(() => {
      return (
        ticketDetails.value.status === 6 &&
        ticketDetails.value.created_by === user?.id
      );
    });
    const goBack = () => {
      router.go(-1); // Go to the previous page in the browser's history
    };

    const isFormModified = computed(() => {
      return (
        state.category !== initialState.category ||
        state.subcategory !== initialState.subcategory ||
        state.assigned_to !== initialState.assigned_to ||
        state.priority !== initialState.priority ||
        state.status !== initialState.status ||
        state.due_date !== initialState.due_date ||
        state.due_expiry_reason !== initialState.due_expiry_reason
      );
    });

    // Updated parseLocalDateTime and handleDueDateChange to preserve previous value if validation fails

    function parseLocalDateTime(dateTimeString: string): Date {
      const [datePart, timePart] = dateTimeString.split("T");
      const [year, month, day] = datePart.split("-").map(Number);
      const [hourStr, minuteStr] = timePart.split(":");

      const hour = parseInt(hourStr);
      const minute = parseInt(minuteStr);

      // Interpret as local datetime (no timezone shift)
      return new Date(year, month - 1, day, hour, minute);
    }

    const handleDueDateChange = () => {
      const currentInput = state.due_date;
      if (!currentInput) return;

      const selectedDateTime = parseLocalDateTime(currentInput);
      const now = new Date();
      const nowFormatted = `${now.getFullYear()}-${String(
        now.getMonth() + 1
      ).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")}T${String(
        now.getHours()
      ).padStart(2, "0")}:${String(now.getMinutes()).padStart(2, "0")}:00`;

      console.log("Now1:", nowFormatted);
      const localFormatted = `${selectedDateTime.getFullYear()}-${String(
        selectedDateTime.getMonth() + 1
      ).padStart(2, "0")}-${String(selectedDateTime.getDate()).padStart(
        2,
        "0"
      )}T${String(selectedDateTime.getHours()).padStart(2, "0")}:${String(
        selectedDateTime.getMinutes()
      ).padStart(2, "0")}:00`;
      const existingDueDate = ticketDetails.value.due_date;
      console.log("Selected Date and Time:", localFormatted);
      console.log("Now:", ticketDetails.value.due_date);
      console.log("Now1:", nowFormatted);

      previousValidDueDate.value = currentInput;

      if (
        localFormatted <= ticketDetails.value.due_date ||
        localFormatted <= nowFormatted
      ) {
        toast.error("Please select a future date and time.");
        state.due_date = previousValidDueDate.value;
        showExtensionField.value = false;
        state.due_expiry_reason = null;
        readonlyDueExtension.value = true;
        return;
      } else if (!originalDueDate.value) {
        showExtensionField.value = false;
        state.due_expiry_reason = null;
      } else {
        showExtensionField.value = true;
        readonlyDueExtension.value = false;
      }

      // if (existingDueDate && localFormatted > existingDueDate || localFormatted > nowFormatted) {
      //   showExtensionField.value = true;
      // } else {
      //   showExtensionField.value = false;
      //   state.due_expiry_reason = null;
      // }

      // showExtensionField.value = !!(
      //   existingDueDate &&
      //   selectedDateTime.getTime() !== existingDueDate.getTime()
      // );
    };

    const getMinDateTime = () => {
      const now = new Date();
      const localNow = new Date(
        now.getTime() - now.getTimezoneOffset() * 60000
      );
      return localNow.toISOString().slice(0, 16);
    };

    const fetchFeedbackValues = async () => {
      try {
        const response = await getFeedbackValues();

        // Ensure that response is an array of Feedback objects
        feedbackValues.value = response.map((feedback: any) => ({
          value_id: feedback.value_id,
          emoji: feedback.emoji,
          label: feedback.label,
        }));
      } catch (error: any) {
        console.error("Error fetching feedback values:", error);
      }
    };

    const selectFeedback = (feedback: { value_id: number }) => {
      selectedFeedback.value = feedback.value_id;
      if (feedbackForm.value) {
        feedbackForm.value.validate();
      }
    };

    // Compute selected label
    const selectedLabel = computed(() => {
      const selected = feedbackValues.value.find(
        (feedback) => feedback.value_id === selectedFeedback.value
      );
      return selected ? selected.label : "";
    });

    // Submit feedback
    const submitFeedback = async () => {
      if (feedbackForm.value) {
        const validationResult = await feedbackForm.value.validate();

        if (!validationResult.valid) {
          toast.error("Please provide Reason.");
          return;
        }
      }

      if (!selectedFeedback.value) {
        toast.error("Please select a feedback option before submitting.");
        return;
      }

      try {
        const payload = {
          ticket: ticketId, // Ticket ID from ticketDetails
          user: user.id, // Assuming 'assigned_to' is the user ID
          feedback_value: selectedFeedback.value, // Selected feedback value ID
          reason: feedbackState.feedbackReason,
        };

        const response = await createFeedback(payload); // Call API
        toast.success("Feedback submitted successfully!");
        handleSubmit();
        showFeedbackDialog.value = false; // Close dialog on success
      } catch (error: any) {
        toast.error("Failed to submit feedback. Please try again.");
        console.error("Error submitting feedback:", error);
      }
    };

    const handleClosed = async () => {
      state.status = "7";

      v$.value.$touch();
      await v$.value.$validate();

      if (v$.value.$error) {
        toast.warning("Please correct the errors before closing the ticket.");
        return;
      }

      showFeedbackDialog.value = true;
    };

    const handleReopen = async () => {
      try {
        // Ensure status is updated before calling handleSubmit
        state.status = "1"; // Set status to 'Open' (ID 1)

        // Call handleSubmit() only after ensuring the state update
        await handleSubmit();
      } catch (error: any) {
        console.error("Error reopening ticket:", error);
      }
    };

    // Computed property to map `assigned_to` to the corresponding status name
    const assignedToStatusName = computed(() => {
      const status = statuses.value.find(
        (s: any) => s.id === ticketDetails.value.status
      );

      return status ? status.name : "--N/A--";
    });

    // Fetch categories
    const fetchCategories = async () => {
      try {
        const response = await getAllCategories(); // Fetch the categories
        categories.value = response.map((category: any) => ({
          value: category.id,
          text: category.name,
          subcategories: category.subcategories.map((sub: any) => ({
            value: sub.id,
            text: sub.subcat_name,
          })),
        }));
      } catch (error: any) {
        console.error("Error fetching categories:", error);
      }
    };

    const updateSubcategories = (selectedCategoryId: number) => {
      const selectedCategory = categories.value.find(
        (cat) => cat.value === selectedCategoryId
      );
      subcategories.value = selectedCategory
        ? selectedCategory.subcategories
        : [];
      state.subcategory = null;
    };

    const fetchStatus = async () => {
      try {
        const response = await ticketStatus();
        console.log("Status response:", response.data);

        // Exclude Closed (id = 7) from dropdown options
        status.value = response.data
          .filter((status: any) => status.id !== 7)
          .map((status: any) => ({
            value: status.id,
            text: status.name,
          }));

        // But save all statuses separately (including Closed)
        statuses.value = response.data.map((status: any) => ({
          value: status.id,
          text: status.name,
        }));
      } catch (error: any) {
        console.error("Error fetching statuses:", error);
      }
    };

    const getStatusName = (id: number) => {
      const match = statuses.value.find((s: any) => s.value === id);
      return match ? match.text : "Unknown Status";
    };
    const fetchAssignee = async () => {
      try {
        const response = await users();
        console.log("Raw users response:", response.data.data);

        const usersList = response.data.data
          .filter((user: any) => {
            const isValid = user.id !== null && user.id !== undefined;
            if (!isValid) {
              console.log("Filtering out user with invalid ID:", user);
            }
            return isValid;
          })
          .map((user: any) => ({
            value: user.id,
            text: `${user.employee_id} - ${user.first_name} ${user.last_name}`,
            role: user.role_id,
          }));

        console.log("Processed usersList:", usersList);
        allUsersList.value = usersList;

        if (isFromAssignTicket.value) {
          assigned_to.value = usersList.filter((user: any) =>
            ["R001", "R002", "R006"].includes(user.role)
          );
        } else {
          assigned_to.value = usersList;
        }
      } catch (error: any) {
        console.error("Error fetching assignees:", error);
      }
    };

    const fetchPriority = async () => {
      try {
        const response = await apiClient.get("tickets-priority/");

        priority.value = response.data.map((priority: any) => ({
          value: priority.id,
          text: priority.priority_name,
        }));
      } catch (error: any) {
        console.error("Error fetching priority:", error);
      }
    };

    const ticketId = Number((route.params as { ticket_id: string }).ticket_id);

    const ticketDetails = ref<any>({
      ticket_id: 0,
      attachement: [],
      title: "",
      project: "",
      type: "",
      description: "",
      priority_names: "",
      watchers: "",
      created_at: "",
      updated_at: "",
      solved_at: "",
      closed_at: "",
      approvel_status: "",
      approvel_message: "",
      approved_by_name: "",
      approved_at: "",
      cancel_message: "",
      justification: "",
      category: "",
      subcategory: "",
      location_name: "",
      created_by: "",
      created_by_fullname: "",
      assigned_to_fullname: "",
      assigned_to: "",
      status: "",
      approved_by: "",
      due_date: "",
      due_expiry_reason: "",
    });

    const rules = {
      category: {
        required: helpers.withMessage("Category is required", () =>
          String(state.status) === "6" ? helpers.req(state.category) : true
        ),
      },
      subcategory: {
        required: helpers.withMessage("Subcategory is required", () =>
          String(state.status) === "6" ? helpers.req(state.subcategory) : true
        ),
      },
      assigned_to: {
        required: helpers.withMessage("Assignee is required", required),
      },
      priority: {},
      status: {},
      due_date: {},
      due_expiry_reason: {
        required: helpers.withMessage(
          "Due Date Extension reason is required",
          () =>
            showExtensionField.value
              ? helpers.req(state.due_expiry_reason)
              : true
        ),
      },
    };

    const state = reactive<State>({
      category: null,
      subcategory: null,
      assigned_to: null,
      priority: null,
      status: "",
      due_date: null,
      due_expiry_reason: null,
    });

    const initialState = reactive<State>({
      category: null,
      subcategory: null,
      assigned_to: null,
      priority: null,
      status: "",
      due_date: null,
      due_expiry_reason: null,
    });
    const v$ = useVuelidate(rules, state);

    function clear(): void {
      v$.value.$reset();
      Object.assign(state, initialState);
    }

    async function handleSubmit(): Promise<void> {
      if (state.due_date && !(state.status === "7" || state.status === "1")) {
        const selectedDateTime = new Date(state.due_date);
        const now = new Date();
        const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
        if (selectedDateTime.getTime() < oneHourFromNow.getTime()) {
          toast.error("Due date must be at least 1 hour from now.");
          return;
        }
      }

      v$.value.$touch();
      await v$.value.$validate();

      if (v$.value.$error) {
        // Instead of a generic message, show specific field errors
        const errorFields = [];

        // Check each field and collect error messages
        if (v$.value.category.$error) {
          errorFields.push("Category");
        }
        if (v$.value.subcategory.$error) {
          errorFields.push("Subcategory");
        }
        if (v$.value.assigned_to.$error) {
          errorFields.push("Assignee");
        }
        if (v$.value.due_expiry_reason.$error) {
          errorFields.push("Due Date Extension Reason");
        }

        // Display specific error message with missing fields
        if (errorFields.length > 0) {
          const fieldList = errorFields.join(", ");
          toast.error(`Please fill in the required fields: ${fieldList}`);
        } else {
          toast.error("Please correct the form errors before submitting.");
        }

        return;
      }

      try {
        loading.value = true;

        const localDueDate = state.due_date ? new Date(state.due_date) : null;

        const payload = {
          ...state,
          title: ticketDetails.value.title,
          description: ticketDetails.value.description,
          project: ticketDetails.value.project,
          ticket_id: ticketDetails.value.ticket_id,
          due_date: localDueDate
            ? `${localDueDate.getFullYear()}-${String(
                localDueDate.getMonth() + 1
              ).padStart(2, "0")}-${String(localDueDate.getDate()).padStart(
                2,
                "0"
              )}T${String(localDueDate.getHours()).padStart(2, "0")}:${String(
                localDueDate.getMinutes()
              ).padStart(2, "0")}`
            : null,
        };

        const apiUrl = `tickets/${ticketDetails.value.ticket_id}/`;

        await apiClient.put(apiUrl, payload);

        toast.success("Ticket updated successfully!");
        clear();
        await router.push("/all-tickets");
      } catch (error: any) {
        console.error("Error submitting form:", error);

        // Check if the error has a response from the server
        if (
          error.response &&
          error.response.data &&
          error.response.data.message
        ) {
          toast.error(`Error: ${error.response.data.message}`);
        } else {
          toast.error(`Error submitting form: ${error.message}`);
        }
      } finally {
        loading.value = false; // This runs **only after** the API call completes
      }
    }

    // const fetchStatusTracking = async () => {
    //   try {
    //     const statusTracking = await getStatusTracking({ id: ticketId });

    //     // Sort data in descending order by created_at
    //     statusOptions.value = statusTracking.data.sort(
    //       (a: any, b: any) =>
    //         new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    //     );
    //   }catch (error:any) {
    //     console.error("Error fetching ticket data:", error);
    //   }
    // };
    const statusMasterMap = ref<any>({});

    const fetchStatusTracking = async () => {
      try {
        const statusTracking = await getStatusTracking({ id: ticketId });

        // Sort data in descending order by created_at
        statusOptions.value = statusTracking.data.sort(
          (a: any, b: any) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      } catch (error: any) {
        console.error("Error fetching ticket data:", error);
      }
    };

    const fetchStatusMaster = async () => {
      try {
        const response = await getStatus(); // Fetch status master data
        const statusList = response;

        // Convert status master into a dictionary (id -> { statusName, description })
        statusMasterMap.value = statusList.reduce((acc: any, status: any) => {
          acc[status.id] = {
            name: status.name,
            description: status.description,
          };
          return acc;
        }, {});
      } catch (error: any) {
        console.error("Error fetching status master data:", error);
      }
    };

    // Get status text from status master table
    const getStatusText = (statusId: any) => {
      return statusMasterMap.value[statusId]?.name || "Unknown Status";
    };

    // Get status description from status master table
    const getStatusDescription = (statusId: any) => {
      return (
        statusMasterMap.value[statusId]?.description ||
        "No description available"
      );
    };

    // Map status IDs to colors
    const getStatusColor = (statusId: number): string => {
      const colors: Record<number, string> = {
        1: "#1976D2", // Open - Blue
        2: "#FF9800", // In Progress - Orange
        3: "#00BCD4", // New - Cyan
        4: "#9C27B0", // Under Observer - Purple
        5: "#FFC107", // Pending - Yellow
        6: "#4CAF50", // Solved - Green
        7: "#F44336", // Closed - Red
        8: "#E91E63", // Awaiting Approval - Pink
        9: "#009688", // Awaiting User Response - Teal
        10: "#795548", // Cancel - Brown
        11: "#3F51B5", // Awaiting Payment - Indigo
        12: "#FF5722", // Requests For Deployment - Deep Orange
      };

      return colors[statusId] || "#9E9E9E"; // Default Gray if not found
    };

    const formatDate = (dateString: any) => {
      return new Date(dateString).toLocaleString("en-US", {
        month: "short", // Apr
        day: "2-digit", // 29
        year: "numeric", // 2025
        hour: "2-digit", // 06
        minute: "2-digit", // 46
        hour12: true, // PM/AM
      });
    };

    const formatWatchers = (watchers: any) => {
      if (!watchers) {
        return "--N/A--";
      }

      try {
        // If watchers is a string, try to parse it as JSON
        if (typeof watchers === "string") {
          try {
            const parsedWatchers = JSON.parse(watchers);
            return formatWatchersArray(parsedWatchers);
          } catch {
            // If parsing fails, return the string as is
            return watchers || "--N/A--";
          }
        }

        // If watchers is already an array
        if (Array.isArray(watchers)) {
          return formatWatchersArray(watchers);
        }

        // If watchers is a single object
        if (typeof watchers === "object") {
          return getWatcherName(watchers);
        }

        return "--N/A--";
      } catch (error) {
        console.error("Error formatting watchers:", error);
        return "--N/A--";
      }
    };

    const formatWatchersArray = (watchersArray: any[]) => {
      if (!watchersArray || watchersArray.length === 0) {
        return "--N/A--";
      }

      const watcherNames = watchersArray
        .map((watcher) => getWatcherName(watcher))
        .filter((name) => name && name !== "--N/A--");

      return watcherNames.length > 0 ? watcherNames.join(", ") : "--N/A--";
    };
    const getWatcherName = (watcher: any): string => {
      if (!watcher) return "--N/A--";

      // Case 1: watcher is ID
      if (typeof watcher === "number") {
        const user =
          assigned_to.value.find((u: any) => u.value === watcher) ||
          allUsersList.value.find((u: any) => u.value === watcher);
        return user ? user.text : `User ID: ${watcher}`;
      }

      // Case 2: watcher is object
      if (typeof watcher === "object") {
        if (watcher.first_name || watcher.last_name) {
          return `${watcher.first_name || ""} ${
            watcher.last_name || ""
          }`.trim();
        }
        if (watcher.name) return watcher.name;
        if (watcher.email) return watcher.email;
        if (watcher.id && watcher.id !== null && watcher.id !== undefined) {
          const user =
            assigned_to.value.find((u: any) => u.value === watcher.id) ||
            allUsersList.value.find((u: any) => u.value === watcher.id);
          return user ? user.text : `User ID: ${watcher.id}`;
        }
      }

      return "--N/A--";
    };

    // Helper function to get display name for watchers in the dialog
    const getWatcherDisplayName = (watcher: any): string => {
      if (!watcher) return "--N/A--";

      // Case 1: watcher is ID
      if (typeof watcher === "number") {
        const user =
          assigned_to.value.find((u: any) => u.value === watcher) ||
          allUsersList.value.find((u: any) => u.value === watcher);
        return user ? user.text : `User ID: ${watcher}`;
      }

      // Case 2: watcher is object
      if (typeof watcher === "object") {
        if (watcher.first_name || watcher.last_name) {
          return `${watcher.first_name || ""} ${
            watcher.last_name || ""
          }`.trim();
        }
        if (watcher.name) return watcher.name;
        if (watcher.email) return watcher.email;
        if (watcher.id && watcher.id !== null && watcher.id !== undefined) {
          const user =
            assigned_to.value.find((u: any) => u.value === watcher.id) ||
            allUsersList.value.find((u: any) => u.value === watcher.id);
          return user ? user.text : `User ID: ${watcher.id}`;
        }
      }

      return "--N/A--";
    };
    const getApprovalDate = (ticket: any) => {
      // Most accurate field
      if (ticket.approve_at) {
        return ticket.approve_at;
      }
      // Legacy/fallback support
      if (ticket.approved_at) {
        return ticket.approved_at;
      }
      if (ticket.approval_date) {
        return ticket.approval_date;
      }
      if (ticket.approvel_date) {
        return ticket.approvel_date;
      }
      // Fallback
      if (ticket.approvel_message && ticket.updated_at) {
        return ticket.updated_at;
      }
      return null;
    };

    const getCancellationDate = (ticket: any) => {
      // Most accurate field
      if (ticket.cancel_at) {
        return ticket.cancel_at;
      }
      // Legacy/fallback support
      if (ticket.cancelled_at) {
        return ticket.cancelled_at;
      }
      if (ticket.cancellation_date) {
        return ticket.cancellation_date;
      }
      if (ticket.cancel_message && ticket.updated_at && ticket.status === 10) {
        return ticket.updated_at;
      }
      return null;
    };

    const statusOptions = ref<any>([]);

    const watchersSummary = computed(() => {
      const watchersText = formatWatchers(ticketDetails.value?.watchers);
      const watchersList =
        watchersText === "--N/A--" ? [] : watchersText.split(", ");

      return {
        text: watchersText,
        count: watchersList.length,
        firstWatcher: watchersList.length > 0 ? watchersList[0] : "--N/A--",
        list: watchersList, // Make sure this property is included
      };
    });

    // Computed property to get current watchers as a list for the dialog
    const currentWatchersList = computed(() => {
      const watchers = ticketDetails.value?.watchers;
      if (!watchers) return [];

      try {
        // If watchers is a string, try to parse it as JSON
        if (typeof watchers === "string") {
          try {
            const parsed = JSON.parse(watchers);
            // Filter out watchers with undefined or null IDs
            if (Array.isArray(parsed)) {
              return parsed.filter((w: any) => {
                if (typeof w === "object") {
                  return w.id !== null && w.id !== undefined;
                }
                return w !== null && w !== undefined;
              });
            }
            return parsed;
          } catch {
            return [];
          }
        }

        // If watchers is already an array
        if (Array.isArray(watchers)) {
          return watchers.filter((w: any) => {
            if (typeof w === "object") {
              return w.id !== null && w.id !== undefined;
            }
            return w !== null && w !== undefined;
          });
        }

        // If watchers is a single object
        if (typeof watchers === "object") {
          if (watchers.id !== null && watchers.id !== undefined) {
            return [watchers];
          }
          return [];
        }

        return [];
      } catch (error) {
        console.error("Error parsing watchers:", error);
        return [];
      }
    });

    watch(
      () => ticketDetails.value?.watchers,
      () => {
        // This will automatically update watchersSummary when watchers change
      },
      { immediate: true }
    );

    const showWatchersDialog = ref<boolean>(false);
    const editableWatchers = ref<number[]>([]); // user IDs for processing
    const editableWatcherObjects = ref<any[]>([]); // user objects for v-combobox display

    function openWatchersDialog() {
      // Check if allUsersList is loaded
      if (!allUsersList.value || allUsersList.value.length === 0) {
        toast.error("User list not loaded. Please try again.");
        return;
      }

      // Normalize watchers to array of IDs
      let current = ticketDetails.value.watchers;
      console.log("Original watchers data:", current);

      if (typeof current === "string") {
        try {
          current = JSON.parse(current);
          console.log("Parsed watchers data:", current);
        } catch {
          current = [];
          console.log("Failed to parse watchers, using empty array");
        }
      }
      if (Array.isArray(current)) {
        editableWatchers.value = current
          .map((w: any) => {
            if (typeof w === "object") {
              return w.id || null;
            }
            return w || null;
          })
          .filter((id: any) => id !== null && id !== undefined);
        console.log("Processed editableWatchers:", editableWatchers.value);

        // Convert IDs to user objects for v-combobox display
        editableWatcherObjects.value = editableWatchers.value
          .map((id: number) => {
            const user = allUsersList.value.find((u: any) => u.value === id);
            return user || { value: id, text: `User ID: ${id}` };
          })
          .filter((user: any) => user !== null);
        console.log(
          "editableWatcherObjects for display:",
          editableWatcherObjects.value
        );
      } else {
        editableWatchers.value = [];
        editableWatcherObjects.value = [];
        console.log("Watchers is not an array, using empty array");
      }
      showWatchersDialog.value = true;
    }

    async function saveWatchers() {
      try {
        loading.value = true;

        // Filter out any undefined or null values from watchers
        const validWatchers = editableWatchers.value.filter(
          (id: any) => id !== null && id !== undefined && !isNaN(id)
        );

        console.log("Original editableWatchers:", editableWatchers.value);
        console.log("Filtered validWatchers:", validWatchers);

        // Additional validation to ensure all watchers are valid numbers
        const finalWatchers = validWatchers
          .map((id: any) => {
            const numId = Number(id);
            return isNaN(numId) ? null : numId;
          })
          .filter((id: any) => id !== null);

        console.log("Final watchers to send:", finalWatchers);

        const payload = {
          ...state,
          watchers: finalWatchers,
          // ...other fields as in handleSubmit
          title: ticketDetails.value.title,
          description: ticketDetails.value.description,
          project: ticketDetails.value.project,
          ticket_id: ticketDetails.value.ticket_id,
          due_date: state.due_date,
        };
        const apiUrl = `tickets/${ticketDetails.value.ticket_id}/`;
        await apiClient.put(apiUrl, payload);
        ticketDetails.value.watchers = [...finalWatchers];
        toast.success("Watchers updated!");
        showWatchersDialog.value = false;
      } catch (error: any) {
        console.error("Error in saveWatchers:", error);
        toast.error("Failed to update watchers.");
      } finally {
        loading.value = false;
      }
    }

    function removeWatcherByIndex(index: number) {
      // Get current watchers as array of IDs
      let current = ticketDetails.value.watchers;
      if (typeof current === "string") {
        try {
          current = JSON.parse(current);
        } catch {
          current = [];
        }
      }
      if (!Array.isArray(current)) return;

      // Remove the watcher at the given index
      const updated = [...current];
      updated.splice(index, 1);

      // Update both arrays
      editableWatchers.value = updated
        .map((w: any) => {
          if (typeof w === "object") {
            return w.id || null;
          }
          return w || null;
        })
        .filter((id: any) => id !== null && id !== undefined);

      // Update the display objects
      editableWatcherObjects.value = editableWatchers.value
        .map((id: number) => {
          const user = allUsersList.value.find((u: any) => u.value === id);
          return user || { value: id, text: `User ID: ${id}` };
        })
        .filter((user: any) => user !== null);

      saveWatchers();
    }

    function handleWatchersChange(newValue: any[]) {
      console.log("handleWatchersChange called with:", newValue);

      // Update the editableWatcherObjects for display
      editableWatcherObjects.value = newValue || [];

      // Filter out any undefined, null, or invalid values and extract IDs
      const validWatchers = newValue.filter((item: any) => {
        if (item === null || item === undefined) {
          console.log("Filtering out null/undefined item:", item);
          return false;
        }
        if (
          typeof item === "object" &&
          (item.value === null || item.value === undefined)
        ) {
          console.log("Filtering out object with null/undefined value:", item);
          return false;
        }
        return true;
      });

      console.log("Valid watchers after filtering:", validWatchers);

      // Extract only the value IDs for processing
      editableWatchers.value = validWatchers
        .map((item: any) => {
          if (typeof item === "object" && item.value !== undefined) {
            console.log("Extracting value from object:", item.value);
            return item.value;
          }
          console.log("Using item as is:", item);
          return item;
        })
        .filter((id: any) => {
          const isValid = id !== null && id !== undefined;
          if (!isValid) {
            console.log("Filtering out invalid ID:", id);
          }
          return isValid;
        });

      console.log("Final editableWatchers:", editableWatchers.value);
    }

    onMounted(async () => {
      await fetchCategories();
      await fetchStatus();
      await fetchAssignee();
      await fetchPriority();
      await fetchStatusTracking();
      await fetchFeedbackValues();
      await fetchStatusMaster(); // Fetch status master first
      minDateTime.value = getMinDateTime();
      previousValidDueDate.value = state.due_date;
      currentUser.value = user.id;
      try {
        const getData = await getTicketById({ id: ticketId });
        ticketDetails.value = {
          ...getData,
          justification: getData.justification || "N/A",
          approved_by_name: getData.approved_by_name || "N/A",
          cancel_message: getData.cancel_message || "N/A",
        };
        state.category = getData.category;
        state.subcategory = getData.subcategory;
        state.assigned_to = getData.assigned_to;
        state.priority = getData.priority;
        state.status = getData.status;
        state.due_expiry_reason = getData.due_expiry_reason;
        originalDueDate.value = getData.due_date || null;
        if (
          getData.due_expiry_reason &&
          getData.due_expiry_reason.trim() !== ""
        ) {
          showExtensionField.value = true;
          readonlyDueExtension.value = true;
        } else {
          showExtensionField.value = false;
          readonlyDueExtension.value = false;
        }

        state.due_date = getData.due_date
          ? new Date(getData.due_date).toISOString().slice(0, 16)
          : "";

        Object.assign(initialState, JSON.parse(JSON.stringify(state)));

        const selectedCategory = categories.value.find(
          (cat) => cat.value === getData.category
        );

        if (selectedCategory) {
          subcategories.value = selectedCategory.subcategories;

          const matchedSubcategory = subcategories.value.find(
            (sub) => sub.value === getData.subcategory
          );

          state.subcategory = matchedSubcategory
            ? matchedSubcategory.value
            : null;
        } else {
          state.subcategory = null; // Reset if no matching category is found
        }

        // Convert due_date to datetime-local format
        if (getData.due_date) {
          const dueDate = new Date(getData.due_date);
          state.due_date = dueDate.toISOString().slice(0, 16);
        } else {
          state.due_date = "";
        }
      } catch (error: any) {
        console.error("Error fetching ticket data:", error);
      }

      // try {
      //   const getChat = await getTicketByChat({ ticket_id: ticketId });
      //   comments.value = getChat;
      // }catch (error:any) {
      //   console.error("Error fetching ticket data:", error);
      // }
      //   try {
      //     const statusTracking = await getStatusTracking({ id: ticketId });
      //     statusOptions.value = statusTracking.data;
      //     statusOptions.value = statusTracking.data.sort((a:any, b:any) =>
      //   new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      // );
      //   }catch (error:any) {
      //     console.error("Error fetching ticket data:", error);
      //   }
    });

    const tab = ref("tab-ticket-info"); // default to the first tab

    return {
      state,
      v$,
      handleSubmit,
      clear,
      ticketDetails,
      categories,
      subcategories,
      status,
      assigned_to,
      assignedToStatusName, // Expose the computed property
      priority,
      loading,
      strings,
      statusOptions,
      showTable,
      showExtensionField,
      currentUser,
      showFeedbackDialog,
      feedbackValues,
      selectedFeedback,
      selectFeedback,
      selectedLabel,
      submitFeedback,
      minDateTime,
      updateSubcategories,
      isEmployee,
      handleClosed,
      handleReopen,
      handleDueDateChange,
      goBack,
      formatDate,
      formatWatchers,
      getApprovalDate,
      getCancellationDate,
      getStatusText,
      getStatusColor,
      getStatusDescription,
      isFormModified,
      handleCancel,
      // vFeedback$,
      feedbackState,
      feedbackRules,
      feedbackForm,
      feedbackFormIsValid,
      isCreatedByLoggedInUser,
      isReadonly,
      getStatusName,
      isAdmin,
      readonlyDueExtension,
      watchersSummary,
      currentWatchersList,
      getWatcherDisplayName,
      showWatchersDialog,
      editableWatchers,
      editableWatcherObjects,
      openWatchersDialog,
      saveWatchers,
      allUsersList,
      removeWatcherByIndex,
      handleWatchersChange,
      tab,
    };
  },
  methods: {
    getItemText(item: any): string {
      return item.raw?.text || "";
    },
    // formatDate(dateString: string): string {
    //   return new Date(dateString).toLocaleDateString("en-US", {
    //     year: "numeric",
    //     month: "long",
    //     day: "numeric",
    //   });
    // },
    getFileName(url: string): string {
      return url.split("/").pop() || "";
    },
  },
});
</script>

<style scoped>
.right-col {
  background: #fff;
  border-left: 1px solid #d8d8d8;
  /* height: 100vh; */
}
.right-col span {
  color: #4a4a4a;
  font-weight: 500;
}
.left-col {
  background: #fff;
  border-right: 1px solid #d8d8d8;
  /* height: 100vh; */
}
.select-field {
  cursor: pointer;
}
.card-info {
  background: #026bb1;
  border-radius: 5px;
}
.card-info span {
  color: #7eb9e0;
  font-weight: 500;
  font-size: 14px;
}
.card-info p {
  color: #fff;
  font-weight: 500;
  font-size: 14px;
}
.card-2 {
  border: 1px solid #d8d8d8;
  border-radius: 5px;
}
.small-select {
  max-width: 100%; /* Adjust width as needed */
}
.card-2 span {
  color: #8c8c8c;
  font-size: 14px;
}
.card-2 p {
  color: #2e2e2e;
  font-size: 14px;
  font-weight: 500;
}
.titles {
  color: #2e2e2e;
  font-size: 23px;
  font-weight: 600;
  padding: 10px 0;
}
.submit-btn {
  background-color: #2dcc70;
  color: #fff;
  text-transform: none;
}
.submit-btn:hover {
  background-color: #fff;
  color: #2dcc70;
}
/* timeline-card */
.timeline-card-text {
  height: auto !important;
}

.feedback-card {
  /* padding: 20px; */
  border-radius: 12px;
  background-color: #fff;
}

/* Header */
.feedback-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: bold;
  font-size: 18px;
}

/* Description */
.feedback-text p {
  font-size: 14px;
  color: #666;
  margin-bottom: 0px;
}

/* Emoji Container */

.emoji-col {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.emoji-btn {
  background: transparent;
  border: 1px solid transparent;
  cursor: pointer;
  transition: 0.3s;
  font-size: 40px;
  border-radius: 10%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-btn:hover {
  background-color: #f0f0f0;
}

.emoji-btn.selected {
  border: 2px solid #007bff;
}

.emoji-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* Submit Button */
.submit-container {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.submit-button {
  background-color: #007bff;
  color: white;
  text-transform: none;
  font-size: 16px;
  padding: 10px 20px;
  width: 100%;
  border-radius: 8px;
}

.submit-button:hover {
  background-color: #0056b3;
}
.my-textarea .v-messages__message {
  text-align: left !important;
}
.v-messages__message {
  text-align: left !important;
}

.description-text {
  white-space: pre-line;
  word-wrap: break-word;
}

:root .custom-tab-wrapper .tab-content-wrapper .label-value {
  text-transform: none;
}

.required-marker {
  color: #ff5252;
  margin-left: 2px;
}

.required-field .v-field__outline {
  border-color: rgba(0, 0, 0, 0.38);
}

.required-field.v-input--error .v-field__outline {
  border-color: #ff5252 !important;
}

/* Highlight required fields that are empty when form is submitted */
.required-field.v-input--error .v-field__outline {
  border-width: 2px;
}

/* .approval-time {
  margin-top: 2px;
} */

.approval-time .text-muted {
  color: #6c757d;
  font-style: italic;
}
.watcher-names {
  margin: 0;
  padding: 0;
  list-style: none;
}

.watcher-count {
  cursor: pointer;
  color: #1976d2; /* Vuetify primary color */
  text-decoration: underline;
}

/* Watcher list item styles */
.watcher-list-item {
  display: flex;
  align-items: center;
}

.watcher-remove-icon {
  cursor: pointer;
  margin-left: 4px;
}

/* Watchers Dialog Styles */
.watchers-dialog-card {
  border-radius: 8px;
}

.watchers-dialog-title {
  font-weight: 600;
  color: #2e2e2e;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 16px;
}

.watchers-dialog-content {
  padding: 20px 0;
}

.watchers-autocomplete {
  margin-bottom: 16px;
}

.watchers-dialog-actions {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
}

.current-watchers-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.current-watchers-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.current-watchers-list li label {
  font-weight: 500;
  color: #333;
  margin: 0;
}

.current-watchers-list li button {
  background-color: #ff5252;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.current-watchers-list li button:hover {
  background-color: #d32f2f;
}

.no-watchers {
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 16px;
  margin: 0;
}
</style>
