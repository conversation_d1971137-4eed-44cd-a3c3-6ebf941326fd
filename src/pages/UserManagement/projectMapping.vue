<template>
  <div class="page-wrapper page-projectMapping">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row
        align="center"
        justify="space-between"
      >
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">
              {{ strings.projectMapping.title }}
            </h2>
            <v-chip
              size="x-small"
              class="data-count ml-2"
            >
              Showing {{ projectDetails.length }} of {{ projectCount }} Records
            </v-chip>
          </div>
          <v-breadcrumbs
            :items="items"
            class="breadcrumbs"
          >
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
        <v-col
          cols="auto"
          class="page-action action-button"
        >
          <v-btn
            variant="outlined"
            class="add-user-btn"
            aria-label="Add Project"
            @click="openAddProjectDialog"
          >
            <v-icon>mdi-folder-multiple</v-icon>{{ strings.projectMapping.buttons.addProject }}
          </v-btn>
        </v-col>
      </v-row>
    </div>

    <section class="page-content-wrapper">
      <!-- Loading Spinner -->
      <div
        v-if="loading"
        align="center"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>

      <!-- Error Message -->
      <div
        v-if="errorMessage"
        class="error-message"
      >
        <v-alert
          type="error"
          dismissible
        >
          {{ errorMessage }}
        </v-alert>
      </div>

      <!-- No Data Available -->
      <div
        v-if="noData && !loading && !errorMessage"
        class="no-data pt-5"
        align="center"
      >
        <div class="no-data-available-wrapper">
          <figure>
            <img
              src="../../assets/images/no-data-available.svg"
              alt="No Data Available"
            >
          </figure>
          <label>{{ strings.projectMapping.noDataText }}</label>
          <small>{{ strings.common.noDataAvailableText }}</small>
        </div>
      </div>

      <!-- Display Projects -->
      <section class="card-list-wrapper pt-3">
        <v-card
          v-for="(project, index) in projectDetails"
          :key="project.id"
          :ref="(el) => setProjectCardRef(el, index)"
          class="app-primary-card"
        >
          <v-row align="center">
            <v-col cols="3">
              <div class="user-data-header">
                {{ strings.projectMapping.projectName }}
              </div>
              <div class="user-data-value">
                {{ project.project_name }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="user-data-header">
                {{ strings.projectMapping.projectManager }}
              </div>
              <div class="user-data-value">
                {{ project.project_manager?.full_name }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="member-container">
                <ul class="member-list-count">
                  <li
                    v-for="member in getFilteredMembers(project).slice(0, 3)"
                    :key="member.id"
                  >
                    <v-tooltip location="bottom">
                      <template #activator="{ props }">
                        <v-avatar
                          size="32"
                          class="user-profile"
                          v-bind="props"
                          :style="{
                            backgroundColor: !member.profile_pic
                              ? generateRandomColor()
                              : 'transparent',
                          }"
                        >
                          <img
                            v-if="member.profile_pic"
                            :src="member.profile_pic"
                            class="profile-pic"
                            loading="lazy"
                          >
                          <span
                            v-else
                            class="initials"
                          >
                            {{ getInitials(member.full_name) }}
                          </span>
                        </v-avatar>
                      </template>
                      <span>{{ member.full_name }}</span>
                    </v-tooltip>
                  </li>
                  <li
                    v-if="getFilteredMembers(project).length > 3"
                    class="extra-members"
                  >
                    <v-avatar
                      size="32"
                      class="extra-members-avatar"
                    >
                      <span class="extra-members-text">
                        +{{ getFilteredMembers(project).length - 3 }}
                      </span>
                    </v-avatar>
                  </li>
                </ul>
              </div>
            </v-col>
            <v-col
              cols="3"
              class="actions"
            >
              <div class="btn-toolbar">
                <v-tooltip location="top">
                  <!-- The button is now inside the activator slot -->
                  <template #activator="{ props }">
                    <v-btn
                      v-bind="props"
                      class="btn-outlined-secondary"
                      variant="outlined"
                      size="x-small"
                      icon
                      @click="openEditProjectDialog(project.id)"
                    >
                      <v-icon>mdi-pencil</v-icon>
                    </v-btn>
                  </template>
                  <!-- This is the tooltip text -->
                  {{ strings.projectMapping.buttons.editProject }}
                </v-tooltip>
                <v-tooltip location="top">
                  <template #activator="{ props }">
                    <v-btn
                      v-bind="props"
                      class="view-btn"
                      variant="outlined"
                      size="x-small"
                      icon
                      @click="openViewProjectDialog(project.id)"
                    >
                      <v-icon>mdi-eye</v-icon>
                    </v-btn>
                  </template>
                  {{ strings.projectMapping.buttons.viewProject }}
                </v-tooltip>
                <v-tooltip location="top">
                  <template #activator="{ props }">
                    <v-btn
                      v-bind="props"
                      icon
                      size="small"
                      class="btn-outlined-danger"
                      variant="outlined"
                      @click="openDeleteProjectDialog(project)"
                    >
                      <v-icon>mdi-delete</v-icon>
                    </v-btn>
                  </template>
                  {{ strings.common.deleteButton }}
                </v-tooltip>
              </div>
            </v-col>
          </v-row>
        </v-card>
      </section>

      <!-- Observer Element -->
      <div
        ref="bottomObserver"
        class="observer-element"
        style="height: 10px; margin: 10px 0"
      />

      <!-- Loading Indicator -->
      <div
        v-if="loadingMore"
        class="text-center my-4"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>

      <!-- Add Project Dialog -->
      <v-dialog
        v-model="isAddProjectDialogOpen"
        max-width="600px"
      >
        <v-card>
          <v-card-title class="headline">
            <h5>{{ strings.projectMapping.addProjectDialogHeader }}</h5>
          </v-card-title>
          <v-card-text>
            <v-form
              ref="addProjectForm"
              v-model="isFormValid"
            >
              <v-row>
                <v-col>
                  <v-text-field
                    v-model="newProject.project_name"
                    label="Project Name"
                    :rules="[rules.required]"
                    variant="outlined"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <v-combobox
                    v-model="newProject.project_manager"
                    v-model:search-input="searchText"
                    label="Project Manager"
                    :items="projectManager ?? []"
                    item-title="text"
                    item-value="id"
                    variant="outlined"
                    chips
                    closable-chips
                    :hide-no-data="true"
                    :menu-props="{ closeOnContentClick: false }"
                    :allow-new-values="false"
                    return-object
                    @keydown.enter.prevent
                    @update:model-value="validateProjectManager"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <v-combobox
                    v-model="newProject.members"
                    v-model:search-input="searchText"
                    label="Members"
                    :items="projectMembers"
                    item-title="text"
                    item-value="id"
                    :rules="[rules.required]"
                    multiple
                    variant="outlined"
                    :placeholder="
                      strings.projectMapping.projectMemberPlaceholder
                    "
                    chips
                    closable-chips
                    :hide-no-data="true"
                    :menu-props="{ closeOnContentClick: false }"
                    @keydown.enter.prevent
                    @update:model-value="filterInvalidValues"
                  />
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-btn
              variant="text"
              @click="cancelProject"
            >
              {{ strings.projectMapping.buttons.cancelProject }}
            </v-btn>
            <v-btn
              variant="tonal"
              @click="addProject"
            >
              {{ strings.projectMapping.buttons.saveProject }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- View Project Dialog -->
      <v-dialog
        v-model="isViewProjectDialogOpen"
        max-width="600px"
      >
        <v-card>
          <v-card-title class="headline">
            <h5>{{ strings.projectMapping.viewProjectTitle }}</h5>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="selectedProject.project_name"
                  label="Project Name"
                  readonly
                  :rules="[rules.required]"
                  variant="outlined"
                  dense
                />
              </v-col>
              <v-col cols="12">
                <v-combobox
                  v-model="selectedProject.project_manager_full_name"
                  label="Project Manager"
                  :items="projectManager"
                  item-title="text"
                  item-value="id"
                  :rules="[rules.required]"
                  readonly
                  variant="outlined"
                  :placeholder="
                    strings.projectMapping.projectManagerPlaceholder
                  "
                  chips
                  closable-chips
                />
              </v-col>
              <v-col cols="12">
                <v-combobox
                  v-model="selectedProject.members"
                  label="Members"
                  :items="filteredProjectMembers"
                  item-title="text"
                  item-value="id"
                  :rules="[rules.required]"
                  multiple
                  readonly
                  variant="outlined"
                  :placeholder="strings.projectMapping.projectMemberPlaceholder"
                  chips
                  closable-chips
                />
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-btn @click="closeDialog">
              {{ strings.projectMapping.buttons.closeProject }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Edit Dialog -->
      <v-dialog
        v-model="isEditProjectDialogOpen"
        max-width="600px"
      >
        <v-card>
          <v-card-title class="headline">
            {{ strings.projectMapping.editProjectTitle }}
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="selectedProject.project_name"
                  label="Project Name"
                  :rules="[rules.required]"
                  variant="outlined"
                  dense
                />
              </v-col>
              <v-col cols="12">
                <v-combobox
                  v-model="selectedProject.project_manager_id as any"
                  v-model:search-input="searchText"
                  label="Project Manager"
                  :items="projectManager"
                  item-title="text"
                  item-value="id"
                  :rules="[rules.required]"
                  variant="outlined"
                  :placeholder="
                    strings.projectMapping.projectManagerPlaceholder
                  "
                  chips
                  closable-chips
                  :hide-no-data="true"
                  :menu-props="{ closeOnContentClick: false }"
                  :allow-new-values="false"
                  :no-filter="false"
                  @keydown.enter.prevent="handleProjectManagerKeydown"
                  @update:model-value="editProjectManager"
                />
              </v-col>
              <v-col cols="12">
                <v-combobox
                  v-model="selectedProject.members"
                  v-model:search-input="searchText"
                  label="Members"
                  :items="filteredProjectMembers"
                  item-title="text"
                  item-value="id"
                  :rules="[rules.required]"
                  multiple
                  variant="outlined"
                  :placeholder="strings.projectMapping.projectMemberPlaceholder"
                  chips
                  closable-chips
                  :hide-no-data="true"
                  :menu-props="{ closeOnContentClick: false }"
                  :allow-new-values="false"
                  :no-filter="false"
                  @keydown.enter.prevent="handleMemberKeydown"
                  @update:model-value="filterMemberValues"
                />
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-btn
              variant="text"
              @click="closeDialog"
            >
              {{ strings.projectMapping.buttons.closeProject }}
            </v-btn>
            <v-btn
              variant="tonal"
              :disabled="!isFormModified"
              @click="saveProject"
            >
              {{ strings.projectMapping.buttons.saveProject }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Delete Project Dialog -->
      <v-dialog
        v-model="isDeleteProjectDialogOpen"
        max-width="420"
      >
        <v-card class="dialog-confirmation negative-dialog-confirmation">
          <v-card-text>
            <div class="dialog-confirmation-icon">
              <v-icon>mdi-alert-circle-outline</v-icon>
            </div>
            <h3 class="dialog-confirmation-title">
              {{ strings.projectMapping.deleteProjectTitle }}
            </h3>
            <label class="dialog-confirmation-info">
              {{ strings.projectMapping.deleteDialogConfirmation }}
              <strong>{{ selectedProject?.project_name }}</strong>?
            </label>
          </v-card-text>
          <v-card-actions>
            <v-btn
              variant="text"
              @click="isDeleteProjectDialogOpen = false"
            >
              {{ strings.projectMapping.buttons.cancelProject }}
            </v-btn>
            <v-btn
              variant="tonal"
              :loading="isDeleting"
              @click="deleteProject"
            >
              {{ strings.projectMapping.buttons.deleteProject }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </section>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from "vue";
import type { ComponentPublicInstance } from "vue";
import { useToast } from "vue-toastification";
import {
  getAllUsers,
  getAllProject,
  updateProjectById,
  createProject,
  getProjectById,
  getProjectByUserId,
  deleteProjectById, // Add this import
} from "../../api/apiClient";
import strings from "../../assets/strings.json";

interface Project {
  id: number;
  project_name: string;
  project_manager: {
    id: number;
    full_name: string;
    profile_pic: string | null;
  } | null;
  project_status: boolean;
  members: {
    id: number;
    full_name: string;
    profile_pic: string | null;
  }[];
}

interface NewProject {
  project_name: string;
  project_manager: { full_name: string; id: number } | null;
  members: { full_name: string; id: number }[];
}

interface SelectedProject {
  id: number;
  project_name: string;
  project_manager_id: number | null;
  project_manager_full_name: string;
  project_start_date: string;
  project_end_date: string;
  project_status: boolean;
  members: any[];
}

export default {
  name: "ProjectMapping",
  setup() {
    // References
    const toast = useToast();
    const items = ref([
      { title: "Home", href: "/" },
      { title: "User Management", href: "/manage-users" },
      { title: "Project Mapping", href: "" },
    ]);
    const isDialogOpen = ref(false);
    const dialogMode = ref("view");
    const isAddProjectDialogOpen = ref(false);
    const isFormValid = ref(false);
    const isViewProjectDialogOpen = ref(false);
    const isEditProjectDialogOpen = ref(false);
    const isDeleteProjectDialogOpen = ref(false);
    const isDeleting = ref(false);
    const selectedProject = ref<SelectedProject>({
      id: 0,
      project_name: "",
      project_manager_full_name: "",
      project_manager_id: null,
      project_start_date: "",
      project_end_date: "",
      project_status: false,
      members: [],
    });

    // Other state variables
    const roles = ref("");
    const locations = ref("");
    const status = ref("");
    const statusOptions = ref(["Active", "Inactive"]);
    const searchValue = ref("");
    const projectManager = ref<{ id: number; full_name: string }[]>([]);
    const projectMembers = ref<{ id: number; full_name: string }[]>([]);
    const projectDetails = ref<Project[]>([]);
    const loading = ref(false);
    const errorMessage = ref("");
    const noData = ref(false);
    const newProject = ref<NewProject>({
      project_name: "",
      project_manager: null,
      members: [],
    });
    const observer = ref<IntersectionObserver | null>(null);
    const bottomObserver = ref<HTMLElement | null>(null);
    const loadingMore = ref(false);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const hasMore = ref(true);
    const pageSize = 10;
    const projectCount = ref(0);
    const project_manager_roles = ["Super Admin", "Manager"];
    const project_members_roles = [
      "Admin",
      "Manager",
      "Team Leader",
      "Employee",
      "Technician",
    ];
    const originalProjectName = ref("");
    const originalProjectManagerId = ref(null);
    const originalProjectMembers = ref([]);
    const projectCardRefs = ref<(HTMLElement | null)[]>([]);
    // Get user from local storage
    const user = JSON.parse(localStorage.getItem("user") || "{}");

    const searchText = ref("");

    const filterInvalidValues = (newVal: any) => {
      if (!Array.isArray(newVal)) {
        newProject.value.members = [];
        return;
      }

      const validIds = projectMembers.value.map((m) => m.id);

      // Filter out any invalid entries (strings or objects with invalid IDs)
      newProject.value.members = newVal.filter((item) => {
        // If it's an object with an id property
        if (item && typeof item === "object" && "id" in item) {
          return validIds.includes(item.id);
        }
        return false; // Reject strings and other invalid types
      });
    };

    const validateProjectManager = (newVal: any) => {
      if (newVal && typeof newVal === "string") {
        // If it's a string (manually typed), reject it
        newProject.value.project_manager = null;
        return;
      }

      if (newVal && typeof newVal === "object") {
        // Check if it's a valid project manager from the list
        const validManager = projectManager.value.find(
          (m) => m.id === newVal.id
        );
        if (!validManager) {
          newProject.value.project_manager = null;
        }
      }
    };

    const editProjectManager = (newVal: any) => {
      // If the value is a string (typed by user), check if it matches any project manager text
      if (typeof newVal === "string") {
        const matchingManager = projectManager.value.find(
          (pm) => pm?.full_name?.toLowerCase?.() === newVal.toLowerCase()
        );

        if (matchingManager) {
          // If we found a match, use its ID
          selectedProject.value.project_manager_id = matchingManager.id;
        } else {
          // If no match, reset to null or previous valid value
          selectedProject.value.project_manager_id = null;
        }
        return;
      }

      // If it's an object with an ID (selected from dropdown)
      if (newVal && typeof newVal === "object" && newVal.id) {
        // Make sure this ID exists in our list
        const validManager = projectManager.value.find(
          (pm) => pm.id === newVal.id
        );
        if (validManager) {
          selectedProject.value.project_manager_id = validManager.id;
        } else {
          selectedProject.value.project_manager_id = null;
        }
        return;
      }

      // If it's already an ID (number), verify it exists
      if (typeof newVal === "number") {
        const validManager = projectManager.value.find(
          (pm) => pm.id === newVal
        );
        if (!validManager) {
          selectedProject.value.project_manager_id = null;
        }
      }
    };

    const filterMemberValues = (newVal: any) => {
      if (!Array.isArray(newVal)) {
        selectedProject.value.members = [];
        return;
      }

      const validIds = filteredProjectMembers.value.map((m) => m.id);

      // Only keep valid objects with valid IDs
      selectedProject.value.members = newVal.filter((item) => {
        // Only accept objects with valid IDs
        if (item && typeof item === "object" && "id" in item) {
          return validIds.includes(item.id);
        }
        return false; // Reject strings and other invalid types
      });
    };

    const isFormModified = computed(() => {
      if (!selectedProject.value) return false;

      // Compare project name
      const isNameChanged =
        selectedProject.value.project_name.trim() !== originalProjectName.value;

      // Compare project manager ID
      const isManagerChanged =
        selectedProject.value.project_manager_id !==
        originalProjectManagerId.value;

      // Extract member IDs, sort them, and compare with original sorted member IDs
      const currentMemberIds = selectedProject.value.members
        .map((member: any) => member.id)
        .sort();
      const originalMemberIds = [...originalProjectMembers.value].sort();

      const isMembersChanged =
        JSON.stringify(currentMemberIds) !== JSON.stringify(originalMemberIds);

      return isNameChanged || isManagerChanged || isMembersChanged;
    });

    // Computed properties
    const filteredProjectMembers = computed(() => {
      const projectManagerId = selectedProject.value.project_manager_id;
      return projectMembers.value.filter(
        (member) => member.id !== projectManagerId
      );
    });

    // Fetch project data
    const fetchProjectData = async (isInitialLoad = false) => {
      if (loading.value || loadingMore.value) return;

      if (isInitialLoad) {
        loading.value = true;
        currentPage.value = 1;
        hasMore.value = true;
        projectDetails.value = [];
      } else {
        loadingMore.value = true;
      }

      try {
        let response;
        if (user.role === "R001") {
          response = await getAllProject(currentPage.value, pageSize);
        } else {
          response = await getProjectByUserId(
            user.id,
            currentPage.value,
            pageSize
          );
        }

        if (response) {
          const projects = response.results?.data || [];
          totalPages.value = response.total_pages || 1;
          projectCount.value = response.count || projects.length;

          if (isInitialLoad) {
            projectDetails.value = projects;
          } else {
            projectDetails.value.push(...projects);
          }

          currentPage.value++;
          hasMore.value = currentPage.value <= totalPages.value;

          // **Ensure noData is updated correctly**
          noData.value = projectDetails.value.length === 0;
        }
      } catch (error: any) {
        console.error("Failed to load projects.");
      } finally {
        loading.value = false;
        loadingMore.value = false;
      }
    };

    // Function to filter out the Project Manager from the members list
    const getFilteredMembers = (project: Project) => {
      if (!project.members || !Array.isArray(project.members)) return [];

      return project.members.filter(
        (member) => member.id !== project.project_manager?.id
      );
    };

    // Set project card reference for infinite scrolling
    const setProjectCardRef = (
      el: Element | ComponentPublicInstance | null,
      index: number
    ) => {
      projectCardRefs.value[index] = el as HTMLElement;
      if (index === projectDetails.value.length - 1) {
        if (bottomObserver.value) {
          observer.value?.observe(bottomObserver.value);
        }
      }
    };

    // Setup intersection observer for infinite scrolling
    const setupIntersectionObserver = () => {
      if (observer.value) {
        observer.value.disconnect();
      }

      observer.value = new IntersectionObserver(
        (entries) => {
          const firstEntry = entries[0];
          if (
            firstEntry.isIntersecting &&
            hasMore.value &&
            !loadingMore.value
          ) {
            fetchProjectData();
          }
        },
        {
          rootMargin: "100px",
          threshold: 0.1,
        }
      );

      if (bottomObserver.value) {
        observer.value.observe(bottomObserver.value);
      }
    };

    // Open view project dialog
    const openViewProjectDialog = async (projectId: number) => {
      isViewProjectDialogOpen.value = true;

      try {
        const response = await getProjectById({ id: projectId });
        const project = response.data;

        const projectManagerId = project.project_manager?.id || null;
        selectedProject.value = {
          id: project.id,
          project_name: project.project_name,
          project_manager_id: projectManagerId,
          project_manager_full_name: `${project.project_manager.employee_id} - ${project.project_manager.full_name}`,
          project_start_date: project.project_start_date,
          project_end_date: project.project_end_date || "",
          project_status: project.project_status,
          members: project.members
            .filter((member: any) => member.id !== projectManagerId)
            .map((member: any) => ({
              id: member.id,
              text: `${member.employee_id} - ${member.full_name}`,
            })),
        };
      } catch (error: any) {
        toast.error("Failed to fetch project details. Please try again.");
      } finally {
        loading.value = false;
      }
    };

    // Open edit project dialog
    const openEditProjectDialog = async (projectId: number) => {
      dialogMode.value = "edit";
      isEditProjectDialogOpen.value = true;

      try {
        const userData = await getAllUsers();
        projectManager.value = userData.data
          .filter((user: any) => project_manager_roles.includes(user.role_name))
          .map((user: any) => ({
            text: `${user.employee_id} - ${user.first_name} ${user.last_name}`,
            id: user.id,
          }));

        projectMembers.value = userData.data
          .filter((user: any) => project_members_roles.includes(user.role_name))
          .map((user: any) => ({
            text: `${user.employee_id} - ${user.first_name} ${user.last_name}`,
            id: user.id,
          }));

        const response = await getProjectById({ id: projectId });
        const project = response.data;

        const projectManagerId = project.project_manager?.id || null;

        // Store original values
        originalProjectName.value = project.project_name;
        originalProjectManagerId.value = project.project_manager?.id || null;
        originalProjectMembers.value = project.members
          .filter((member: any) => member.id !== projectManagerId) // Exclude project manager
          .map((member: any) => member.id);

        selectedProject.value = {
          id: project.id,
          project_name: project.project_name,
          project_manager_id: projectManagerId,
          project_manager_full_name: `${project.project_manager.employee_id} - ${project.project_manager.full_name}`,
          project_start_date: project.project_start_date,
          project_end_date: project.project_end_date || "",
          project_status: project.project_status,
          members: project.members
            .filter((member: any) => member.id !== projectManagerId)
            .map((member: any) => ({
              id: member.id,
              text: `${member.employee_id} - ${member.full_name}`,
            })),
        };
      } catch (error: any) {
        console.error("Failed to fetch project details:", error);
        toast.error("Failed to fetch project details. Please try again.");
      } finally {
        loading.value = false;
      }
    };

    const onProjectManagerChange = (
      newManager: any[] | { id: number; text: string } | null
    ) => {
      if (Array.isArray(newManager)) {
        // If multiple selection enabled, get first selected manager
        selectedProject.value.project_manager_id = newManager.length
          ? (newManager[0].id as { id: number }).id
          : null;
        selectedProject.value.project_manager_full_name = newManager.length
          ? newManager[0].text
          : "";
      } else if (newManager) {
        // If single selection, assign the values directly
        selectedProject.value.project_manager_id = (
          newManager as { id: number }
        ).id;
        selectedProject.value.project_manager_full_name = newManager.text;
      } else {
        // If nothing is selected
        selectedProject.value.project_manager_id = 0;
        selectedProject.value.project_manager_full_name = "";
      }
    };

    // Save project (for edit mode)
    const saveProject = async () => {
      try {
        const projectManager = selectedProject.value.project_manager_id ?? [];
        const projectManagerId =
          typeof projectManager === "object" && "id" in projectManager
            ? (projectManager as { id: number }).id
            : projectManager;

        if (!projectManagerId) {
          throw new Error(
            "Project manager should be provided as a valid user ID."
          );
        }

        const members = selectedProject.value.members
          .map((member: any) => member.id)
          .filter((id): id is number => id !== null && id !== undefined);

        if (members.length === 0) {
          throw new Error("At least one member must be selected.");
        }

        const payload = {
          project_name: selectedProject.value.project_name,
          project_manager: projectManagerId,
          members,
          updated_by: user.id,
        };

        const response = await updateProjectById(
          String(selectedProject.value.id),
          payload
        );

        if (response && response.data) {
          toast.success(response.message);
        }

        toast.success("Project updated successfully!");
        closeDialog();

        // Fetch updated project list
        let projectsResponse;
        if (user.role === "R001") {
          projectsResponse = await getAllProject(1, pageSize); // Reset to first page
        } else {
          projectsResponse = await getProjectByUserId(user.id, 1, pageSize);
        }

        if (!projectsResponse || !projectsResponse.results?.data) {
          throw new Error("Invalid response from project API");
        }

        // Reset pagination and update project list
        projectDetails.value = projectsResponse.results.data;
        currentPage.value = 2; // Reset page tracking for further pagination
        hasMore.value = currentPage.value <= projectsResponse.total_pages;
        projectCount.value = projectsResponse.count;
      } catch (error: any) {
        console.error("Error updating project:", error);
        toast.error(error.message);
      }
    };

    // Close dialog and reset selected project
    const closeDialog = () => {
      isEditProjectDialogOpen.value = false;
      isViewProjectDialogOpen.value = false;
      selectedProject.value = {
        id: 0,
        project_name: "",
        project_manager_full_name: "",
        project_manager_id: null,
        project_start_date: "",
        project_end_date: "",
        project_status: false,
        members: [],
      };
    };

    // Close view project dialog and reset selected project
    const closeViewProjectDialog = () => {
      isViewProjectDialogOpen.value = false;
      selectedProject.value = {
        id: 0,
        project_name: "",
        project_manager_full_name: "",
        project_manager_id: 0,
        project_start_date: "",
        project_end_date: "",
        project_status: false,
        members: [],
      };
    };

    // Map projects to the required structure
    const mapProjects = (projects: any[]): Project[] => {
      if (!projects || !Array.isArray(projects)) {
        console.error("mapProjects received invalid data:", projects);
        return [];
      }

      return projects.map((project: any) => ({
        id: project.id,
        project_name: project.project_name,
        project_manager: project.project_manager
          ? {
              id: project.project_manager.id,
              full_name: project.project_manager.full_name,
              profile_pic: project.project_manager.profile_pic || null,
            }
          : null,
        project_status: project.project_status,
        members:
          project.members?.map((member: any) => ({
            id: member.id,
            full_name: member.full_name,
            profile_pic: member.profile_pic || null,
          })) || [],
      }));
    };

    // Generate random color
    const generateRandomColor = (): string => {
      const letters = "0123456789ABCDEF";
      let color = "#";
      for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
      }
      return color;
    };

    // Validation rules
    const rules = {
      required: (value: any) => !!value || "This field is required.",
      minLength: (value: any) =>
        value.length >= 3 || "Minimum 3 characters required.",
    };

    // Cancel project creation
    const cancelProject = () => {
      isAddProjectDialogOpen.value = false;
      newProject.value = {
        project_name: "",
        project_manager: null,
        members: [],
      };
    };

    // Open add project dialog
    const openAddProjectDialog = async () => {
      try {
        const response = await getAllUsers();
        projectManager.value = response.data
          .filter((user: any) => project_manager_roles.includes(user.role_name))
          .map((user: any) => ({
            text: `${user.employee_id} - ${user.first_name} ${user.last_name}`,
            id: user.id,
          }));
        projectMembers.value = response.data
          .filter((user: any) => project_members_roles.includes(user.role_name))
          .map((user: any) => ({
            text: `${user.employee_id} - ${user.first_name} ${user.last_name}`,
            id: user.id,
          }));
        const loggedUser = projectManager.value.find(
          (manager) => manager.id === user.id
        );
        newProject.value.project_manager = loggedUser || null;
        isAddProjectDialogOpen.value = true;
      } catch (error: any) {
        console.error("Error fetching users:", error);
        toast.error("Failed to fetch project managers. Please try again.");
      }
    };

    // Add new project
    const addProject = async () => {
      if (isFormValid.value) {
        try {
          loading.value = true;

          const projectManagerId = newProject.value.project_manager?.id ?? null;
          if (!projectManagerId) {
            toast.warning("Please select a project manager.");
            loading.value = false;
            return;
          }

          const membersIds = newProject.value.members.map(
            (member) => member.id
          );

          const payload = {
            project_name: newProject.value.project_name,
            is_active: true,
            project_start_date: new Date().toISOString().split("T")[0],
            project_manager: projectManagerId,
            members: membersIds,
            created_by: user.id,
            updated_by: user.id,
          };

          const response = await createProject(payload);

          if (response && response.data) {
            toast.success(response.message);

            // Close dialog
            isAddProjectDialogOpen.value = false;

            // Reset form fields
            newProject.value = {
              project_name: "",
              project_manager: null,
              members: [],
            };

            // Delay fetching to ensure API updates the data
            setTimeout(async () => {
              await fetchProjectData(true);
            }, 500);
          } else if (response.error) {
            // Extract and display specific validation errors
            const errorMessages = Object.values(response.error)
              .flat()
              .join(", "); // Convert error object to a string message

            toast.error(
              errorMessages ||
                "Failed to create project. Please check your input."
            );
          }
        } catch (error: any) {
          console.error("Error adding project:", error);

          // If the API throws an error response, extract the message
          if (error.response && error.response.data) {
            const errorMessages = Object.values(error.response.data.error || {})
              .flat()
              .join(", "); // Convert API error object into readable string

            toast.error(
              errorMessages ||
                "Failed to create project. Please check your input."
            );
          } else {
            toast.error("Failed to create project. Please try again.");
          }
        } finally {
          loading.value = false;
        }
      } else {
        toast.warning("Please fill all required fields.");
      }
    };

    // Get initials from name
    const getInitials = (name: string): string => {
      if (!name || name.trim() === "") return "NA";
      const parts = name.trim().split(" ");
      return parts.length > 1
        ? `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase()
        : `${parts[0][0]}`.toUpperCase();
    };

    // Function to open delete project dialog
    const openDeleteProjectDialog = (project: Project) => {
      selectedProject.value = {
        id: project.id,
        project_name: project.project_name,
        project_manager_id: project.project_manager?.id || null,
        project_manager_full_name: project.project_manager?.full_name || "",
        project_start_date: "",
        project_end_date: "",
        project_status: project.project_status,
        members: project.members || [],
      };
      isDeleteProjectDialogOpen.value = true;
    };

    // Function to delete the project
    const deleteProject = async () => {
      if (!selectedProject.value.id) return;

      isDeleting.value = true;

      try {
        // Call your API to delete the project
        await deleteProjectById({
          project_id: selectedProject.value.id,
          project_deleted_by: user.id,
        });

        // Remove the project from the local state
        projectDetails.value = projectDetails.value.filter(
          (p) => p.id !== selectedProject.value.id
        );

        // Close the dialog
        isDeleteProjectDialogOpen.value = false;

        // Show success message
        toast.success("Project deleted successfully");
      } catch (error: any) {
        console.error("Error deleting project:", error);
        toast.error(error.message || "Failed to delete project");
      } finally {
        isDeleting.value = false;
      }
    };
    const handleMemberKeydown = (e: any) => {
      // Prevent adding invalid entries when pressing Enter
      if (e.key === "Enter" && searchText.value) {
        // Check if the entered text matches any existing member
        const matchingMember = filteredProjectMembers.value.find(
          (member) =>
            member.full_name.toLowerCase() === searchText.value.toLowerCase()
        );

        if (matchingMember) {
          // If a match is found, add it to the selection if not already selected
          const isAlreadySelected = selectedProject.value.members.some(
            (selected) => selected.id === matchingMember.id
          );

          if (!isAlreadySelected) {
            selectedProject.value.members.push(matchingMember);
          }
        }

        // Clear the search text
        searchText.value = "";
      }
    };

    const handleProjectManagerKeydown = (e: any) => {
      // Always prevent default Enter behavior to avoid adding invalid entries
      if (e.key === "Enter" && searchText.value) {
        // e.preventDefault();

        // Try to find a matching project manager by text
        const matchingManager = projectManager.value.find(
          (manager) =>
            manager.full_name.toLowerCase() === searchText.value.toLowerCase()
        );
        console.log("matchingManager:", matchingManager);

        if (matchingManager) {
          // If a match is found, set it as the project manager
          const isAlreadySelected =
            newProject.value.project_manager &&
            newProject.value.project_manager.id === matchingManager.id;
          if (!isAlreadySelected) {
            newProject.value.project_manager = matchingManager;
          }
        }

        // Always clear the search text
        searchText.value = "";
      }
    };

    // On Component Mount
    onMounted(async () => {
      nextTick(() => {
        fetchProjectData(true);
      });
      setupIntersectionObserver();
    });

    // On Component Unmount
    onUnmounted(() => {
      if (observer.value) {
        observer.value.disconnect();
      }
    });

    return {
      items,
      roles,
      locations,
      projectManager,
      projectMembers,
      status,
      statusOptions,
      searchValue,
      loading,
      errorMessage,
      projectDetails,
      noData,
      getInitials,
      generateRandomColor,
      isAddProjectDialogOpen,
      isFormValid,
      newProject,
      rules,
      openAddProjectDialog,
      addProject,
      cancelProject,
      isViewProjectDialogOpen,
      isEditProjectDialogOpen,
      selectedProject,
      openViewProjectDialog,
      closeViewProjectDialog,
      isDialogOpen,
      dialogMode,
      openEditProjectDialog,
      saveProject,
      closeDialog,
      strings,
      bottomObserver,
      loadingMore,
      hasMore,
      filteredProjectMembers,
      projectCount,
      setProjectCardRef,
      isFormModified,
      onProjectManagerChange,
      getFilteredMembers,
      searchText,
      filterInvalidValues,
      validateProjectManager,
      editProjectManager,
      filterMemberValues,
      isDeleteProjectDialogOpen,
      isDeleting,
      openDeleteProjectDialog,
      deleteProject,
      handleMemberKeydown,
      handleProjectManagerKeydown,
    };
  },
};
</script>

<style scoped>
.manage-user-container {
  margin: 5px;
  padding: 16px;
  height: 100vh;
  overflow-y: auto;
}
/* Page Header */
.page-header {
  display: flex;
  align-items: center;
  gap: 10px;
}
.page-header h2 {
  margin: 0;
  font-size: 24px;
  color: #333;
}
.data-count {
  background-color: #026bb1;
  border-radius: 16px;
  color: #fff;
  padding: 4px 12px;
  font-size: 12px;
}
.breadcrumbs {
  padding: 0;
  margin: 0;
  font-size: 14px;
  margin-bottom: 1rem;
  color: #757575;
}
.profile-pic {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* search & filter */
.search-filter-section {
  background-color: #f9f9f9;
  border-radius: 5px;
  margin: 5px;
  padding: 10px 5px;
}
.dropdown-default-value {
  color: #b5b5b5;
}
.form-section-container {
  padding: 12px;
}
.form-input {
  width: 100%;
  font-size: 14px;
  border: 1px solid #ccc;
  padding: 15px;
  border-radius: 4px;
  box-sizing: border-box;
  background-color: #fff;
}
.form-input:focus {
  border-color: #000;
}
.placeholder {
  color: gray;
}

/* User data */
.user-data {
  align-items: center;
  display: flex;
  justify-content: space-between;
}
.user-data-card {
  margin: 5px 15px;
  padding: 16px;
  width: 100%;
  border: 1px solid #d8d8d8;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}
.user-data-card:hover {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.4);
  transform: translateY(-3px);
}
.name-section {
  display: flex;
  gap: 5px;
}
.user-data-header {
  color: #8c8c8c;
  font-size: 12px;
}
.user-data-details {
  font-size: 14px;
  color: #2e2e2e;
  font-weight: 500;
}
.user-data-details p:first-child {
  color: #8c8c8c;
  margin-bottom: 4px;
}
.user-data-btn {
  display: flex;
  justify-content: center;
  gap: 10px;
}
.status-chip {
  height: 20px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
.member-container {
  display: flex;
  overflow-x: auto;
  cursor: pointer;
}
.member-list-count {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}
.member-list-count > li {
  margin-left: 0px;
}
.member-list-count > li + li {
  margin-left: -10px;
}
.member-list-count > li > div {
  border: 2px solid #fff;
}
.extra-members-avatar {
  background-color: #ccc; /* Neutral background for '+N' */
  display: flex;
  align-items: center;
  justify-content: center;
}

.extra-members-text {
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
}

/* Profile-Pic */
.initials {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: white;
  font-size: 0.75rem;
  border-radius: 50%;
  cursor: pointer;
}

/* Button */
.action-btn,
.action-button {
  align-items: center;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
.add-user-btn {
  text-transform: none;
  border: 1px solid #026bb1;
  color: #026bb1;
}
.add-user-btn:hover {
  background-color: #026bb1;
  color: #fff;
}
.view-btn {
  border: 1px soild #2dcc70;
  color: #2dcc70;
}
.view-btn:hover {
  background-color: #2dcc70;
  color: #fff;
}
.edit-btn {
  border: 1px solid #026bb1;
  color: #026bb1;
}
.edit-btn:hover {
  background-color: #026bb1;
  color: #fff;
}
.inactive-btn {
  border: 1px solid #ef4d56;
  color: #ef4d56;
}
.inactive-btn:hover {
  background-color: #ef4d56;
  color: #fff;
}

/* Dialog Model */
.block-dialog-card {
  text-align: center;
  padding: 20px;
}

.block-dialog-title {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.dialog-title-text {
  font-size: 30px;
  color: #333;
}

.dialog-content-text {
  font-size: 16px;
  color: #555;
}
.actions {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
.dialog-actions {
  display: flex;
  justify-content: space-evenly !important;
  gap: 10px;
}

.cancel-button {
  background-color: #f1f1f1;
  color: #7d7d7d;
  border: none;
}

.block-button {
  background-color: #ef4d56;
  color: #fff;
}

/* For Mobile Devices*/
@media (max-width: 600px) {
  .action-button {
    display: flex;
    justify-content: space-evenly;
  }
}

/* For Tablets */
@media (min-width: 601px) and (max-width: 960px) {
}

/* For Laptops and Desktops */
@media (min-width: 961px) {
}
</style>
