<template>
  <div class="page-wrapper page-addNewUser">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row
        align="center"
        justify="space-between"
      >
        <v-col cols="auto">
          <h2 class="page-title">
            {{ strings.addUser.title }}
          </h2>
          <v-breadcrumbs
            :items="items"
            class="breadcrumbs"
          >
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
        <!-- Right Column: Back Button -->
        <v-col
          cols="auto"
          class="page-action"
        >
          <div class="btn-toolbar">
            <v-btn
              variant="outlined"
              class="btn-outlined-dark"
              @click="goBack"
            >
              <v-icon>mdi-chevron-left</v-icon>
              {{ strings.addUser.backButton }}
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </div>

    <section class="page-content-wrapper">
      <!-- Form Section -->
      <v-row class="user-info-section">
        <v-col
          cols="12"
          lg="2"
          md="3"
          sm="3"
          xs="12"
          class="profile-section"
        >
          <div class="profile-pic-header">
            <div class="profile-label">
              {{ strings.addUser.profilePicture }}
            </div>
            <div class="optional-label">
              ({{ strings.addUser.optional }})
            </div>
          </div>
          <div class="profile-image-container">
            <div class="profile-image">
              <v-img :src="imageSrc || defaultImage" />
            </div>
            <!-- <div class="file-size-limit pt-1">(Maximum file size: 2MB)</div> -->
            <div class="upload-section py-3">
              <v-btn
                variant="outlined"
                @click="triggerFileInput"
              >
                <v-icon>mdi-upload</v-icon>{{ strings.addUser.uploadButton }}
              </v-btn>
              <input
                ref="fileInput"
                style="display: none"
                type="file"
                accept="image/*"
                @change="handleImageChange"
              >
              <span
                v-if="profilePicError"
                class="error-text"
                style="
                display: block;
                color: red;
                font-size: 12px;
                margin-top: 6px;
              "
              >
                {{ profilePicError }}
              </span>
            </div>
            <div class="helper-text">
              <label>{{ strings.common.filesizeText }}</label>
            </div>
          </div>
        </v-col>
        <v-col
          cols="10"
          lg="10"
          md="9"
          sm="9"
          xs="12"
          class="form-section"
        >
          <div class="form-input">
            <v-form ref="formRef">
              <v-row class="form-section-container">
                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="firstName"
                      class="form-label"
                    >{{
                      strings.addUser.firstName
                    }}</label>
                    <input
                      id="firstName"
                      v-model="firstName"
                      type="text"
                      class="form-input"
                      :placeholder="strings.addUser.firstName"
                      @blur="() => validateField('firstName', firstName)"
                      @input="clearError('firstName')"
                    >
                    <span
                      :class="['error-text', { visible: errors.firstName }]"
                    >{{ errors.firstName }}</span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="lastName"
                      class="form-label"
                    >{{
                      strings.addUser.lastName
                    }}</label>
                    <input
                      id="lastName"
                      v-model="lastName"
                      type="text"
                      class="form-input"
                      :placeholder="strings.addUser.lastName"
                      @blur="() => validateField('lastName', lastName)"
                      @input="clearError('lastName')"
                    >
                    <span :class="['error-text', { visible: errors.lastName }]">{{
                      errors.lastName
                    }}</span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="employeeId"
                      class="form-label"
                    >{{
                      strings.addUser.employeeId
                    }}</label>
                    <input
                      id="employeeId"
                      v-model="employeeId"
                      type="text"
                      class="form-input"
                      :placeholder="strings.addUser.employeeId"
                      @blur="() => validateField('employeeId', employeeId)"
                      @input="clearError('employeeId')"
                    >
                    <span
                      :class="['error-text', { visible: errors.employeeId }]"
                    >{{ errors.employeeId }}</span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="role"
                      class="form-label"
                    >{{
                      strings.addUser.role
                    }}</label>
                    <div
                      class="select-wrapper"
                      @click="toggleDropdown('role')"
                    >
                      <select
                        id="role"
                        v-model="roles"
                        class="form-input"
                        @blur="() => validateField('role', roles)"
                      >
                        <option
                          value=""
                          disabled
                        >
                          {{ strings.common.selectRolePlaceholder }}
                        </option>
                        <option
                          v-for="role in rolesOptions"
                          :key="role.id"
                          :value="role.role_name"
                        >
                          {{ role.role_name }}
                        </option>
                      </select>
                      <v-icon
                        class="dropdown-icon"
                        :class="{ rotate: isRoleDropdownOpen }"
                      >
                        mdi-chevron-down
                      </v-icon>
                    </div>
                    <span :class="['error-text', { visible: errors.role }]">{{
                      errors.role
                    }}</span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="location"
                      class="form-label"
                    >{{
                      strings.addUser.location
                    }}</label>
                    <div
                      class="select-wrapper"
                      @click="toggleDropdown('location')"
                    >
                      <select
                        id="location"
                        v-model="location"
                        class="form-input"
                        @blur="() => validateField('location', location)"
                      >
                        <option
                          value=""
                          disabled
                        >
                          {{ strings.common.selectLocationPlaceholder }}
                        </option>
                        <option
                          v-for="location in locationOptions"
                          :key="location.id"
                          :value="location.location_name"
                        >
                          {{ location.location_name }}
                        </option>
                      </select><v-icon
                        class="dropdown-icon"
                        :class="{ rotate: isLocationDropdownOpen }"
                      >
                        mdi-chevron-down
                      </v-icon>
                    </div>
                    <span :class="['error-text', { visible: errors.location }]">{{
                      errors.location
                    }}</span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="email"
                      class="form-label"
                    >{{
                      strings.addUser.email
                    }}</label>
                    <input
                      id="email"
                      v-model="email"
                      type="text"
                      class="form-input"
                      :placeholder="strings.addUser.email"
                      @blur="() => validateField('email', email)"
                      @input="clearError('email')"
                    >
                    <span :class="['error-text', { visible: errors.email }]">{{
                      errors.email
                    }}</span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="phoneNumber"
                      class="form-label"
                    >{{
                      strings.addUser.phoneNumber
                    }}</label>
                    <input
                      id="phoneNumber"
                      v-model="phoneNumber"
                      type="text"
                      class="form-input"
                      :placeholder="strings.addUser.phoneNumber"
                      @blur="() => validateField('phoneNumber', phoneNumber)"
                      @input="clearError('phoneNumber')"
                    >
                    <span
                      :class="['error-text', { visible: errors.phoneNumber }]"
                    >{{ errors.phoneNumber }}</span>
                  </div>
                </v-col>
              </v-row>
            </v-form>
          </div>
        </v-col>
      </v-row>

      <!-- Cancel and Submit Button -->
      <v-row>
        <v-col>
          <div class="btn-toolbar justify-center">
            <v-btn
              variant="text"
              @click="clearForm"
            >
              {{ strings.addUser.clearButton }}
            </v-btn>
            <v-btn
              :loading="loading"
              variant="tonal"
              :style="
                loading ? { backgroundColor: '#2dcc70', color: '#fff' } : {}
              "
              @click="submitForm"
            >
              <template #loader>
                <v-progress-circular
                  indeterminate
                  size="20"
                  class="spinner"
                />
              </template>
              {{ strings.addUser.submitButton }}
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </section>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, watch } from "vue";
import {
  getLocations,
  getUserRoles,
  addUser,
  getAllUsers,
} from "../../api/apiClient";
import { useToast } from "vue-toastification";
import router from "../../router";
import strings from "../../assets/strings.json";
import defaultImage from "../../assets/images/default-image.png";

export default {
  name: "AddNewUser",
  setup() {
    const dropdownOpen = ref(false);
    const toast = useToast();
    const loading = ref(false);
    const profilePicError = ref("");
    const items = ref([
      { title: "Home", href: "/" },
      { title: "User Management", href: "/manage-users" },
      { title: "Add User", href: "" },
    ]);

    const countryCodes: Record<string, string> = {
      "Nexware-India": "+91",
      "NexGen-Japan": "+81",
    };

    const fieldLabels: Record<string, string> = {
      firstName: "First Name",
      lastName: "Last Name",
      email: "Email Address",
      phoneNumber: "Phone Number",
      employeeId: "Employee ID",
      role: "Role",
      location: "Location",
    };

    const clearError = (fieldName: string) => {
      errors.value[fieldName] = "";
    };

    const validationRules: Record<
      string,
      (value: string, fieldName: string) => true | string
    > = {
      required(value: string, fieldName: string) {
        const label = fieldLabels[fieldName];
        return value.trim() !== "" || `*${label} is required.`;
      },
      email(value: string, fieldName: string) {
        const label = fieldLabels[fieldName];
        return (
          /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(value) ||
          `*Invalid ${label}.`
        );
      },
      phoneNumber(value: string, fieldName: string) {
        const label = fieldLabels[fieldName];
        return (
          /^\+\d{1,4}-\d{10}$/.test(value) || `*${label} should be 10 digits.`
        );
      },
      noDigits(value: string, fieldName: string) {
        const label = fieldLabels[fieldName];
        return (
          /^[A-Za-z\s]+$/.test(value) ||
          `*${label} should only contain alphabets.`
        );
      },
    };

    const firstName = ref("");
    const lastName = ref("");
    const employeeId = ref("");
    const email = ref("");
    const phoneNumber = ref("");
    const roles = ref("");
    const location = ref("");
    const profile_pic = ref<File | null>(null);
    const imageSrc = ref<string | null>(null);
    const rolesOptions = ref<{ id: string; role_name: string }[]>([]);
    const locationOptions = ref<{ id: string; location_name: string }[]>([]);
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    const status = ref("Active");
    const errors = ref<Record<string, string>>({});
    const filteredOptions = ref<
      { id: number; employee_id: string; full_name: string }[]
    >([]);
    const searchQuery = ref("");
    const isRoleDropdownOpen = ref(false);
    const isLocationDropdownOpen = ref(false);

    const toggleDropdown = (field: unknown) => {
      if (field === "role") {
        isRoleDropdownOpen.value = !isRoleDropdownOpen.value;
        isLocationDropdownOpen.value = false; // Close the other dropdown
      } else if (field === "location") {
        isLocationDropdownOpen.value = !isLocationDropdownOpen.value;
        isRoleDropdownOpen.value = false; // Close the other dropdown
      }
    };

    const validateField = (fieldName: string, value: string) => {
      const fieldRules: Record<string, string[]> = {
        firstName: ["required", "noDigits"],
        lastName: ["required", "noDigits"],
        email: ["required", "email"],
        phoneNumber: ["required", "phoneNumber"],
        employeeId: ["required"],
        role: ["required"],
        location: ["required"],
      };

      const rules = fieldRules[fieldName] || [];
      for (const rule of rules) {
        const validation = validationRules[rule](value, fieldName); // Pass fieldName for error message
        if (validation !== true) {
          errors.value[fieldName] = validation; // Store error message
          return false;
        }
      }

      errors.value[fieldName] = ""; // Clear error if valid
      return true;
    };

    const validateForm = () => {
      let isValid = true;
      [
        { field: "firstName", value: firstName.value },
        { field: "lastName", value: lastName.value },
        { field: "employeeId", value: employeeId.value },
        { field: "location", value: location.value },
        { field: "email", value: email.value },
        { field: "phoneNumber", value: phoneNumber.value },
        { field: "role", value: roles.value },
      ].forEach(({ field, value }) => {
        if (!validateField(field, value)) {
          isValid = false;
        }
      });
      return isValid;
    };

    const triggerFileInput = () => {
      const fileInput = document.createElement("input");
      fileInput.type = "file";
      fileInput.accept = "image/*";
      fileInput.onchange = handleImageChange;
      fileInput.click();
    };

    const handleImageChange = (event: Event) => {
      const fileInput = event.target as HTMLInputElement;
      const file = fileInput?.files?.[0];

      profilePicError.value = ""; // Reset error

      if (file) {
        const maxSize = 2 * 1024 * 1024; // 2MB
        if (file.size > maxSize) {
          profilePicError.value = "Profile picture must not exceed 2MB.";
          toast.error("Profile picture must not exceed 2MB.");
          profile_pic.value = null;
          imageSrc.value = null;
          return;
        }

        profile_pic.value = file;
        imageSrc.value = URL.createObjectURL(file);
      }
    };

    const clearForm = () => {
      firstName.value = "";
      lastName.value = "";
      employeeId.value = "";
      email.value = "";
      phoneNumber.value = "";
      roles.value = "";
      location.value = "";
      imageSrc.value = "";
      searchQuery.value = "";
      errors.value = {};
    };

    const submitForm = async () => {
      if (validateForm()) {
        loading.value = true; // Start spinner
        try {
          // Find the selected role using its value
          const selectedRole = rolesOptions.value.find(
            (role) => role.role_name === roles.value
          );

          if (!selectedRole) {
            toast.error(
              `Role ID not found for the selected role: ${roles.value}`
            );
            loading.value = false; // Stop spinner
            return;
          }

          // Find the selected location using its value
          const selectedLocation = locationOptions.value.find(
            (loc) => loc.location_name === location.value
          );

          if (!selectedLocation) {
            toast.error(
              `Location ID not found for the selected location: ${location.value}`
            );
            loading.value = false; // Stop spinner
            return;
          }

          const isActive = status.value === "Active";

          // Construct FormData
          const formData = new FormData();
          formData.append("first_name", firstName.value);
          formData.append("last_name", lastName.value);
          formData.append("employee_id", employeeId.value);
          formData.append("email", email.value);
          formData.append("phone_number", phoneNumber.value);
          formData.append("role", selectedRole.id);
          formData.append("location", selectedLocation.id);
          formData.append("is_active", String(isActive));
          formData.append("created_by", user.id);
          formData.append("updated_by", user.id);

          // Append profile picture file only if it exists
          if (profile_pic.value) {
            formData.append("profile_pic", profile_pic.value);
          }

          // API call
          const response = await addUser(formData);

          // Extract and display the success message
          if (response && response.data && response.data.message) {
            toast.success(response.message);
            router
              .push("/manage-users")
              .catch((err: any) => console.error("Navigation Error:", err));
          } else {
            toast.success("User added successfully!");
            router
              .push("/manage-users")
              .catch((err) => console.error("Navigation Error:", err));
          }

          clearForm();
        } catch (error: any) {
          console.error("Error submitting form: ", error);

          // Handle validation errors
          if (error.response?.data) {
            const errorData = error.response.data;

            // Check for specific fields with errors and show individual toasts
            for (const key in errorData) {
              if (Array.isArray(errorData[key])) {
                errorData[key].forEach((message: string) => {
                  toast.error(message); // Show each error message in a toast
                });
              }
            }
          } else {
            // Default error message if no specific validation errors
            toast.error("Failed to add user. Please try again.");
          }
        } finally {
          loading.value = false; // Stop spinner regardless of success or failure
        }
      } else {
        toast.warning("Please fill out all required fields.");
      }
    };

    const goBack = () => {
      router.go(-1); // Go to the previous page in the browser's history
    };

    const openDropdown = () => {
      dropdownOpen.value = true;
    };

    onMounted(async () => {
      try {
        const rolesData = await getUserRoles();
        const locationData = await getLocations();
        const allUsersData = await getAllUsers();
        rolesOptions.value = rolesData.data.map((role: any) => ({
          id: role.role_id,
          role_name: role.role_name,
        }));
        locationOptions.value = locationData.data.map((location: any) => ({
          id: location.location_id,
          location_name: location.location_name,
        }));
      } catch (error: any) {
        console.error("Error fetching roles: ", error);
      }
    });

    watch(location, (newLocation) => {
      if (countryCodes[newLocation]) {
        phoneNumber.value = countryCodes[newLocation] + "-";
      }
    });

    return {
      items,
      loading,
      clearError,
      firstName,
      lastName,
      employeeId,
      email,
      phoneNumber,
      roles,
      rolesOptions,
      location,
      profile_pic,
      locationOptions,
      errors,
      validateField,
      clearForm,
      imageSrc,
      submitForm,
      triggerFileInput,
      handleImageChange,
      filteredOptions,
      searchQuery,
      dropdownOpen,
      goBack,
      strings,
      toggleDropdown,
      isRoleDropdownOpen,
      isLocationDropdownOpen,
      defaultImage,
      profilePicError,
    };
  },
};
</script>

<style scoped>
.spinner {
  color: #fff;
}
.input-field {
  position: relative; /* Ensure the parent is positioned relative for absolute child positioning */
  margin-bottom: 5px; /* Add space between fields */
}
.error-text {
  color: red;
  font-size: 12px;
  margin-top: 6px;
  display: block;
}
.error-text.visible {
  visibility: visible; /* Show error when there's an issue */
}
.form-label {
  display: block;
  font-size: 14px;
  font-weight: bold;
  color: #2e2e2e;
}
.form-section-container {
  padding: 12px;
}
.form-input {
  width: 100%;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}
.form-input:focus {
  border-color: #000;
}
/* Breadcrumbs */
.breadcrumbs {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: grey;
}
/* Back Button */
.back-btn {
  color: #7d7d7d;
  border: 1px solid #7d7d7d;
  text-transform: none;
}
.back-btn:hover {
  background-color: #000;
  color: #fff;
}
/* Profile Section Row */
.user-info-section {
  margin-bottom: 20px;
}
/* Profile Picture Column */
.profile-section {
  text-align: center;
}

.upload-btn {
  border: 1px solid #2dcc70;
  color: #2dcc70;
  text-transform: none;
  width: 100%;
  font-size: 12px;
}
.upload-btn:hover {
  background-color: #2dcc70;
  color: #fff;
}
/* Form Section Column */
.form-input {
  border: 1px solid #d8d8d8;
  border-radius: 5px;
  padding: 12px;
}
/* Cancel & Submit */
.button-section {
  display: flex;
  justify-content: center;
  gap: 15px;
}
.clear-btn {
  color: #7d7d7d;
  text-transform: none;
}
.clear-btn:hover {
  color: #fff;
  background-color: #7d7d7d;
}
.submit-btn {
  color: #2dcc70;
  text-transform: none;
}
.submit-btn:hover {
  background-color: #2dcc70;
  color: #fff;
}
.dropdown-container {
  position: relative;
  width: 100%;
}
.dropdown-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}
.dropdown-list-container {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.dropdown-list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.dropdown-item {
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}
.dropdown-item:hover {
  background-color: #f0f0f0;
}
.dropdown-no-results {
  padding: 10px;
  color: #888;
  text-align: center;
}
.select-wrapper {
  width: 100%;
  cursor: pointer;
}
.custom-select {
  width: 100%;
  padding: 10px;
  padding-right: 30px; /* Space for the icon */
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  appearance: none; /* Hide default dropdown arrow */
  background-color: white;
}
/* Style for the dropdown icon */
.dropdown-icon {
  position: absolute;
  right: 20px;
  top: 65%;
  transform: translateY(-50%);
  color: #666;
  transition: transform 0.3s ease-in-out;
  pointer-events: none;
}
.dropdown-icon.rotate {
  transform: translateY(-50%) rotate(180deg);
}
/* Responsive Design */

/* For Mobile Devices*/
@media (max-width: 600px) {
  .action-section {
    display: none;
  }
  .breadcrumbs {
    margin: 0px;
    padding: 0px;
    font-size: 12px;
  }
  .profile-section {
    text-align: center;
  }
}

/* For Tablets */
@media (min-width: 601px) and (max-width: 960px) {
  .breadcrumbs {
    font-size: 12px;
  }
}

/* For Laptops and Desktops */
@media (min-width: 961px) {
  .breadcrumbs {
    font-size: 14px;
  }
}
</style>
