<template>
  <div class="page-wrapper page-edit-user">
    <!--Page Header Section -->
    <div class="page-header">
      <v-row
        align="center"
        justify="space-between"
      >
        <!-- Left Column: Header and Breadcrumbs -->
        <v-col cols="auto">
          <h2 class="page-title">
            {{ strings.editUser.title }}
          </h2>
          <v-breadcrumbs
            :items="isSelfView ? MyAccountItems : items"
            class="breadcrumbs"
          >
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
        <!-- Right Column: Back Button -->
        <v-col
          cols="auto"
          class="page-action"
        >
          <div class="btn-toolbar">
            <v-btn
              v-if="!isSelfView"
              variant="outlined"
              @click="viewUserPage(selectedUserId)"
            >
              <v-icon>mdi-eye</v-icon>
              {{ strings.editUser.buttons.view }}
            </v-btn>
            <v-btn
              variant="outlined"
              class="btn-outlined-dark"
              @click="goBack"
            >
              <v-icon>mdi-chevron-left</v-icon>
              {{ strings.editUser.buttons.back }}
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </div>

    <section class="page-content-wrapper">
      <!-- Form Section -->
      <v-row class="user-info-section">
        <v-col
          cols="12"
          lg="2"
          md="3"
          sm="3"
          xs="12"
          class="profile-section"
        >
          <div class="profile-pic-header">
            <div class="profile-label">
              {{ strings.editUser.profilePicture }}
            </div>
            <div class="optional-label">
              {{ strings.editUser.optional }}
            </div>
          </div>
          <div class="profile-image-container">
            <div class="profile-image">
              <v-img :src="imageSrc || defaultImage" />
            </div>
            <!-- <div class="file-size-limit pt-1">(Maximum file size: 2MB)</div> -->
            <div class="upload-section py-3">
              <v-btn
                variant="outlined"
                @click="triggerFileInput"
              >
                <v-icon>mdi-upload</v-icon>{{ strings.editUser.buttons.uploadProfile }}
              </v-btn>
              <input
                ref="fileInputRef"
                style="display: none"
                type="file"
                accept="image/*"
                @change="handleImageChange"
              >
              <v-btn
                variant="outlined"
                class="btn-outlined-danger"
                :disabled="!imageSrc"
                @click="removeProfile"
              >
                <v-icon>mdi-close</v-icon>{{ strings.editUser.buttons.removeProfile }}
              </v-btn>
              <span
                :class="['error-text', { visible: profilePicError }]"
                style="
                  display: block;
                  color: red;
                  font-size: 12px;
                  margin-top: 6px;
                "
              >
                {{ profilePicError }}
              </span>
            </div>
            <div class="helper-text">
              <label>{{ strings.common.filesizeText }}</label>
            </div>
          </div>
        </v-col>
        <v-col
          cols="12"
          lg="10"
          md="9"
          sm="9"
          xs="12"
          class="form-section"
        >
          <div class="form-input">
            <v-form ref="formRef">
              <v-row class="form-section-container">
                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="firstName"
                      class="form-label"
                    >{{
                      strings.editUser.firstName
                    }}</label>
                    <input
                      id="firstName"
                      v-model="firstName"
                      type="text"
                      :disabled="isSelfView"
                      class="form-input"
                      :placeholder="strings.editUser.firstName"
                      @blur="() => validateField('firstName', firstName)"
                    >
                    <span
                      :class="['error-text', { visible: errors.firstName }]"
                    >
                      {{ errors.firstName }}
                    </span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="lastName"
                      class="form-label"
                    >{{
                      strings.editUser.lastName
                    }}</label>
                    <input
                      id="lastName"
                      v-model="lastName"
                      :disabled="isSelfView"
                      type="text"
                      class="form-input"
                      :placeholder="strings.editUser.lastName"
                      @blur="() => validateField('lastName', lastName)"
                    >
                    <span
                      :class="['error-text', { visible: errors.lastName }]"
                    >{{ errors.lastName }}</span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="employeeId"
                      class="form-label"
                    >{{
                      strings.editUser.employeeId
                    }}</label>
                    <input
                      id="employeeId"
                      v-model="employeeId"
                      type="text"
                      class="form-input"
                      :disabled="isSelfView"
                      :placeholder="strings.editUser.employeeId"
                      @blur="() => validateField('employeeId', employeeId)"
                    >
                    <span
                      :class="['error-text', { visible: errors.employeeId }]"
                    >{{ errors.employeeId }}</span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="role"
                      class="form-label"
                    >{{
                      strings.editUser.role
                    }}</label>
                    <div
                      class="select-wrapper"
                      @click="toggleDropdown('role')"
                    >
                      <select
                        id="role"
                        v-model="roles"
                        :disabled="isSelfView"
                        class="form-input"
                        @blur="() => validateField('role', roles)"
                      >
                        <option
                          value=""
                          disabled
                        >
                          {{ strings.common.selectRolePlaceholder }}
                        </option>
                        <option
                          v-for="role in rolesOptions"
                          :key="role.id"
                          :value="role.role_name"
                        >
                          {{ role.role_name }}
                        </option>
                      </select>
                      <v-icon
                        class="dropdown-icon"
                        :class="{ rotate: isRoleDropdownOpen }"
                      >
                        mdi-chevron-down
                      </v-icon>
                    </div>
                    <span :class="['error-text', { visible: errors.role }]">
                      {{ errors.role }}
                    </span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="location"
                      class="form-label"
                    >{{
                      strings.editUser.location
                    }}</label>
                    <div
                      class="select-wrapper"
                      @click="toggleDropdown('location')"
                    >
                      <select
                        id="location"
                        v-model="location"
                        :disabled="isSelfView"
                        class="form-input"
                        @blur="() => validateField('location', location)"
                      >
                        <option
                          value=""
                          disabled
                        >
                          {{ strings.common.selectLocationPlaceholder }}
                        </option>
                        <option
                          v-for="location in locationOptions"
                          :key="location.id"
                          :value="location.location_name"
                        >
                          {{ location.location_name }}
                        </option>
                      </select><v-icon
                        class="dropdown-icon"
                        :class="{ rotate: isLocationDropdownOpen }"
                      >
                        mdi-chevron-down
                      </v-icon>
                    </div>
                    <span
                      :class="['error-text', { visible: errors.location }]"
                    >{{ errors.location }}</span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="email"
                      class="form-label"
                    >{{
                      strings.editUser.email
                    }}</label>
                    <input
                      id="email"
                      v-model="email"
                      type="text"
                      :disabled="isSelfView"
                      class="form-input"
                      :placeholder="strings.editUser.email"
                      @blur="() => validateField('email', email)"
                    >
                    <span :class="['error-text', { visible: errors.email }]">{{
                      errors.email
                    }}</span>
                  </div>
                </v-col>

                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  sm="6"
                  class="input-field"
                >
                  <div class="form-group d-block">
                    <label
                      for="phoneNumber"
                      class="form-label"
                    >{{
                      strings.editUser.phoneNumber
                    }}</label>
                    <input
                      id="phoneNumber"
                      v-model="phoneNumber"
                      type="text"
                      class="form-input"
                      :disabled="isSelfView"
                      :placeholder="strings.editUser.phoneNumber"
                      @blur="() => validateField('phoneNumber', phoneNumber)"
                    >
                    <span
                      :class="['error-text', { visible: errors.phoneNumber }]"
                    >{{ errors.phoneNumber }}</span>
                  </div>
                </v-col>
              </v-row>
            </v-form>
          </div>
        </v-col>
      </v-row>

      <!-- Cancel and Submit Button -->
      <v-row>
        <v-col class="btn-toolbar justify-center">
          <v-btn
            variant="text"
            @click="clearForm"
          >
            {{ strings.editUser.buttons.clear }}
          </v-btn>
          <v-btn
            :loading="loading"
            :disabled="!isFormModified"
            variant="tonal"
            :style="
              loading ? { backgroundColor: '#2dcc70', color: '#fff' } : {}
            "
            @click="submitForm"
          >
            <template #loader>
              <v-progress-circular
                indeterminate
                size="20"
                class="spinner"
              />
            </template>
            {{ strings.editUser.buttons.submit }}
          </v-btn>
        </v-col>
      </v-row>
    </section>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import {
  getLocations,
  getUserRoles,
  getUserById,
  updateUserById,
} from "../../api/apiClient";
import { useToast } from "vue-toastification";
import { useRoute, type RouteParams } from "vue-router";
import { useRouter } from "vue-router";
import router from "../../router";
import strings from "../../assets/strings.json";
import defaultImage from "../../assets/images/default-image.png";
import { useUserStore } from "../../stores/userStore";

interface User {
  id: number;
  profile_pic: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  role_name: string;
  location_name: string;
  email: string;
  phone_number: string;
}

export default {
  name: "EditUser",
  setup() {
    const route = useRoute();
    const router = useRouter();
    const selectedUserId = Number((route.params as { id: string }).id);
    const dropdownOpen = ref(false);
    const toast = useToast();
    const loading = ref(false);
    const profilePicError = ref("");
    const items = ref([
      { title: "Home", href: "/" },
      { title: "User Management", href: "/user-management" },
      { title: "Manage User", href: "/manage-users" },
      { title: "Edit User", href: "" },
    ]);
    const MyAccountItems = ref([
      { title: "Home", href: "/" },
      { title: "View User", href: `/view-user/${selectedUserId}?self=true` },
      { title: "Edit User", href: "" },
    ]);

    const originalData = ref({
      firstName: "",
      lastName: "",
      employeeId: "",
      email: "",
      phoneNumber: "",
      roles: "",
      location: "",
      profile_pic: null as File | null,
      imageSrc: null as string | null, // Store original profile image
    });

    const fieldLabels: Record<string, string> = {
      firstName: "First Name",
      lastName: "Last Name",
      email: "Email Address",
      phoneNumber: "Phone Number",
      employeeId: "Employee ID",
      role: "Role",
      location: "Location",
    };

    const countryCodes: Record<string, string> = {
      "Nexware-India": "+91",
      "NexGen-Japan": "+81",
    };

    const validationRules: Record<
      string,
      (value: string, fieldName: string) => true | string
    > = {
      required(value: string, fieldName: string) {
        const label = fieldLabels[fieldName];
        return value.trim() !== "" || `*${label} is required.`;
      },
      email(value: string, fieldName: string) {
        const label = fieldLabels[fieldName];
        return (
          /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(value) ||
          `*Invalid ${label}.`
        );
      },
      phoneNumber(value: string, fieldName: string) {
        const label = fieldLabels[fieldName];
        if (location.value === "NexGen-Japan") {
          // Must match +81-xx-xxxx-xxxx
          const japanRegex = /^\+81-\d{2}-\d{4}-\d{4}$/;
          return (
            japanRegex.test(value) ||
            `*${label} must be in +81-xx-xxxx-xxxx format.`
          );
        } else {
          // Default: Accept +countrycode-digits
          const defaultRegex = /^\+\d{1,4}-\d{7,15}$/;
          return defaultRegex.test(value) || `*Invalid ${label}.`;
        }
      },
    };
    const userData = ref<User[]>([]);
    const firstName = ref("");
    const lastName = ref("");
    const employeeId = ref("");
    const email = ref("");
    const phoneNumber = ref("");
    const roles = ref("");
    const location = ref("");
    const profile_pic = ref<File | null>(null);
    const imageSrc = ref<string | null>(null);
    const rolesOptions = ref<{ id: string; role_name: string }[]>([]);
    const locationOptions = ref<{ id: string; location_name: string }[]>([]);
    const errors = ref<Record<string, string>>({});
    const filteredOptions = ref<
      { id: number; employee_id: string; full_name: string }[]
    >([]); // Filtered options for dropdown
    const searchQuery = ref(""); // Search input value
    const isRoleDropdownOpen = ref(false);
    const isLocationDropdownOpen = ref(false);
    const isProfileRemoved = ref(false);
    const fileInputRef = ref<HTMLInputElement | null>(null);
    const isSelfView = computed(() => route.query.self === "true");
    const userStore = useUserStore();

    const triggerFileInput = () => {
      fileInputRef.value?.click();
    };

    const toggleDropdown = (field: unknown) => {
      if (field === "role") {
        isRoleDropdownOpen.value = !isRoleDropdownOpen.value;
        isLocationDropdownOpen.value = false; // Close the other dropdown
      } else if (field === "location") {
        isLocationDropdownOpen.value = !isLocationDropdownOpen.value;
        isRoleDropdownOpen.value = false; // Close the other dropdown
      }
    };

    const validateField = (fieldName: string, value: string) => {
      const fieldRules: Record<string, string[]> = {
        firstName: ["required"],
        lastName: ["required"],
        email: ["required", "email"],
        phoneNumber: ["required", "phoneNumber"],
        employeeId: ["required"],
        role: ["required"],
        location: ["required"],
      };

      const rules = fieldRules[fieldName] || [];
      for (const rule of rules) {
        const validation = validationRules[rule](value, fieldName);
        if (validation !== true) {
          errors.value[fieldName] = validation;
          return false;
        }
      }

      errors.value[fieldName] = "";
      return true;
    };

    const goBack = () => {
      router.go(-1); // Go to the previous page in the browser's history
    };

    const validateForm = () => {
      let isValid = true;
      [
        { field: "firstName", value: firstName.value },
        { field: "lastName", value: lastName.value },
        { field: "employeeId", value: employeeId.value },
        { field: "location", value: location.value },
        { field: "email", value: email.value },
        { field: "phoneNumber", value: phoneNumber.value },
        { field: "role", value: roles.value },
      ].forEach(({ field, value }) => {
        if (!validateField(field, value)) {
          isValid = false;
        }
      });
      return isValid;
    };

    const viewUserPage = (userId: number) => {
      router
        .push({ name: "/User Management/viewUser", params: { id: userId } })
        .catch((err) => console.error("Navigation Error:", err));
    };

    // const triggerFileInput = () => {
    //   const fileInput = document.createElement("input");
    //   fileInput.type = "file";
    //   fileInput.accept = "image/*";
    //   fileInput.onchange = handleImageChange;
    //   fileInput.click();
    // };

    const handleImageChange = (event: Event) => {
      const fileInput = event.target as HTMLInputElement;
      const file = fileInput?.files?.[0];

      profilePicError.value = ""; // Reset previous error

      if (file) {
        const maxSize = 2 * 1024 * 1024; // 2MB
        if (file.size > maxSize) {
          profilePicError.value = "Profile picture must not exceed 2MB.";
          toast.error("Profile picture must not exceed 2MB.");
          profile_pic.value = null;
          imageSrc.value = null;
          return;
        }

        profile_pic.value = file;
        imageSrc.value = URL.createObjectURL(file);
        isProfileRemoved.value = false;
      }
    };

    const user = JSON.parse(localStorage.getItem("user") || "{}");

    const clearForm = () => {
      fetchUsersById();
      errors.value = {};
    };

    const removeProfile = () => {
      imageSrc.value = null;
      profile_pic.value = null;
      isProfileRemoved.value = true;
    };
    const isSelfViewFromViewUser = computed(() => {
      return route.query.self === "true";
    });

    const submitForm = async () => {
      if (validateForm()) {
        loading.value = true; // Start loading spinner

        try {
          // Find the selected role using its value
          const selectedRole = rolesOptions.value.find(
            (role) => role.role_name === roles.value
          );

          if (!selectedRole) {
            toast.error(
              `Role ID not found for the selected role: ${roles.value}`
            );
            loading.value = false;
            return;
          }

          // Find the selected location using its value
          const selectedLocation = locationOptions.value.find(
            (loc) => loc.location_name === location.value
          );

          if (!selectedLocation) {
            toast.error(
              `Location ID not found for the selected location: ${location.value}`
            );
            loading.value = false;
            return;
          }

          // Construct FormData for API call
          const formData = new FormData();
          formData.append("first_name", firstName.value);
          formData.append("last_name", lastName.value);
          formData.append("employee_id", employeeId.value);
          formData.append("email", email.value);
          formData.append("phone_number", phoneNumber.value);
          formData.append("role", selectedRole.id); // Send role ID instead of name
          formData.append("location", selectedLocation.id); // Send location ID
          formData.append("updated_by", user.id.toString());

          if (profile_pic.value) {
            formData.append("profile_pic", profile_pic.value);
          } else if (isProfileRemoved.value) {
            formData.append("profile_pic", ""); //only clear if user clicked remove
          }

          // Payload for API call
          const payload = {
            id: selectedUserId,
            data: formData, // Ensure it's correctly formatted
          };

          console.log("🔍 FormData payload preview:");
          for (const pair of (formData as any).entries()) {
            console.log(`${pair[0]}:`, pair[1]);
          }

          // **API Call to update user**
          const response = await updateUserById(payload);
          // After successful updateUserById API call
          const currentUser = JSON.parse(localStorage.getItem("user") || "{}");

          const isOwnAccount = selectedUserId === currentUser.id;
          const roleChanged = selectedRole.id !== originalData.value.roles; // Use ID comparison for accuracy

          
          // if (isOwnAccount && roleChanged) {

          //   toast.success(
          //     "Your role has been updated. You will be logged out."
          //   );
          //   localStorage.removeItem("user");
          //   alert("User logged out due to role change.");
          //   await router.push("/login");
          //   return;
          // }

          if (response?.message) {
            toast.success(response.message, { timeout: 3000 });
          }

          if (isSelfViewFromViewUser.value) {
            // Update localStorage user if the current user matches the updated user
            const currentUser = JSON.parse(
              localStorage.getItem("user") || "{}"
            );
            if (currentUser.id === selectedUserId) {
              const updatedUser = {
                ...currentUser,
                first_name: firstName.value,
                last_name: lastName.value,
                email: email.value,
                phone_number: phoneNumber.value,
                location: selectedLocation.id,
                role: selectedRole.id,
                profile_pic: imageSrc.value || null,
                updated_at: new Date().toISOString(),
                updated_by: currentUser.id,
              };
              userStore.setUser(updatedUser);
            }

            await router.push(`/view-user/${selectedUserId}?self=true`); // Route to View User page
            // window.location.reload();
          } else {
            await router.push("/manage-users"); // Default route
          }
          //  const currentUser = JSON.parse(
          //     localStorage.getItem("user") || "{}"
          //   );
          //  const isOwnProfile = selectedUserId === currentUser.id;
          //   const isSuperadmin = currentUser.role === "R001";
          //   const roleChanged =
          //     selectedRole.role_name !== originalData.value.roles;
          //   console.log(roleChanged);

          //   if ( roleChanged) {
          //     toast.success(
          //       "Your role has been updated. You will be logged out."
          //     );
          //     localStorage.removeItem("user");
          //     alert("User logged out due to role change.");
          //     await router.push("/login");
          //     return; // Stop further execution
          //   }
        } catch (error: any) {
          // **Handle API Errors**
          if (error.response?.data) {
            Object.values(error.response.data).forEach((messageArray: any) => {
              if (Array.isArray(messageArray)) {
                messageArray.forEach((message) => toast.error(message));
              }
            });
          } else {
            toast.error("Failed to update user. Please try again.");
          }
        } finally {
          loading.value = false; // Stop spinner
        }
      } else {
        toast.warning("Please fill out all required fields.");
      }
    };

    const fetchUsersById = async () => {
      loading.value = true;
      try {
        // Ensure locationOptions is loaded before calling getUserById
        if (locationOptions.value.length === 0) {
          const locationData = await getLocations();
          locationOptions.value = locationData.data.map((location: any) => ({
            id: location.location_id,
            location_name: location.location_name,
          }));
        }

        const response = await getUserById({ id: selectedUserId });
        const user = response.data;

        // Find role name and location name using options
        const roleName =
          rolesOptions.value.find((role) => role.id === user.role)?.role_name ||
          "";
        const locationName =
          locationOptions.value.find(
            (location) => location.id === user.location
          )?.location_name || "";

        // Map the data to form fields
        firstName.value = user.first_name || "";
        lastName.value = user.last_name || "";
        employeeId.value = user.employee_id || "";
        email.value = user.email || "";
        phoneNumber.value = user.phone_number || "";
        roles.value = user.role_name || "";
        location.value = locationName;
        imageSrc.value = user.profile_pic;

        location.value = locationName;

        // Extract country code from the existing phone number
        for (const [locationKey, countryCode] of Object.entries(countryCodes)) {
          if (phoneNumber.value.startsWith(countryCode)) {
            location.value = locationKey; // Set the correct location
            break;
          }
        }
        originalData.value = {
          firstName: firstName.value,
          lastName: lastName.value,
          employeeId: employeeId.value,
          email: email.value,
          phoneNumber: phoneNumber.value,
          roles: roles.value,
          location: location.value,
          profile_pic: null,
          imageSrc: imageSrc.value, // Store original profile image
        };
      } catch (error: any) {
        toast.error(`Error fetching user: ${error.message}`);
        console.error("Error fetching user:", error);
      } finally {
        loading.value = false;
      }
    };

    const isFormModified = computed(() => {
      // Check if any text field has changed
      const textFieldsChanged =
        firstName.value !== originalData.value.firstName ||
        lastName.value !== originalData.value.lastName ||
        email.value !== originalData.value.email ||
        phoneNumber.value !== originalData.value.phoneNumber ||
        roles.value !== originalData.value.roles ||
        location.value !== originalData.value.location;

      // Check if profile picture has changed
      const profileChanged =
        profile_pic.value !== null ||
        imageSrc.value !== originalData.value.imageSrc;

      return textFieldsChanged || profileChanged;
    });

    onMounted(async () => {
      try {
        const rolesData = await getUserRoles();
        const locationData = await getLocations();
        rolesOptions.value = rolesData.data.map((role: any) => ({
          id: role.role_id,
          role_name: role.role_name,
        }));
        locationOptions.value = locationData.data.map((location: any) => ({
          id: location.location_id,
          location_name: location.location_name,
        }));
        await fetchUsersById();
      } catch (error: any) {
        console.error("Error fetching roles: ", error);
      }
    });

    watch(location, (newLocation) => {
      if (countryCodes[newLocation]) {
        const oldNumber = phoneNumber.value.replace(/^\+\d{1,4}-/, ""); // Remove old country code
        phoneNumber.value = countryCodes[newLocation] + "-" + oldNumber; // Keep existing digits
      }
    });

    return {
      items,
      loading,
      firstName,
      lastName,
      employeeId,
      email,
      phoneNumber,
      roles,
      rolesOptions,
      location,
      profile_pic,
      locationOptions,
      errors,
      validateField,
      clearForm,
      imageSrc,
      submitForm,
      triggerFileInput,
      handleImageChange,
      filteredOptions,
      searchQuery,
      dropdownOpen,
      removeProfile,
      userData,
      viewUserPage,
      selectedUserId,
      goBack,
      strings,
      toggleDropdown,
      isRoleDropdownOpen,
      isLocationDropdownOpen,
      defaultImage,
      isFormModified,
      fileInputRef,
      MyAccountItems,
      isSelfView,
      profilePicError,
    };
  },
};
</script>

<style scoped>
.spinner {
  color: #fff;
}
.input-field {
  position: relative; /* Ensure the parent is positioned relative for absolute child positioning */
  margin-bottom: 5px; /* Add space between fields */
}
.error-text {
  color: red;
  font-size: 12px;
  position: absolute; /* Absolute positioning to avoid layout shifts */
  bottom: -10px;
  left: 10px;
  visibility: hidden; /* Default hidden state */
}
.error-text.visible {
  visibility: visible; /* Show error when there's an issue */
  position: static;
  display: block;
  margin-top: 6px;
}
.form-label {
  display: block;
  font-size: 14px;
  font-weight: bold;
  color: #2e2e2e;
}
.form-section-container {
  padding: 12px;
}
.form-input {
  width: 100%;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}
.form-input:focus {
  border-color: #000;
}
/* Header Styles */
.add-user-header {
  margin-bottom: 20px;
}

/* Breadcrumbs */
.breadcrumbs {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: grey;
}

/* Back Button */
.action-section {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
.view-btn {
  border: 1px solid #2dcc70;
  color: #2dcc70;
  text-transform: none;
}
.view-btn:hover {
  background-color: #2dcc70;
  color: #fff;
}
.back-btn {
  color: #7d7d7d;
  border: 1px solid #7d7d7d;
  text-transform: none;
}
.back-btn:hover {
  background-color: #000;
  color: #fff;
}
/* Profile Section Row */
.user-info-section {
  margin-bottom: 20px;
}
/* Profile Picture Column */
.profile-section {
  text-align: center;
}
/* Profile Picture Header */
.profile-pic-header {
  background-color: #2e2e2e;
  color: #fff;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
/* Labels */
.profile-label {
  font-weight: bold;
  font-size: 16px;
}
.optional-label {
  font-size: 12px;
  color: #8e8e8e;
}
.profile-image-container {
  padding: 10px;
  border: 1px solid #2e2e2e;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.profile-image {
  padding: 5px;
  border: 1px solid #d9d9d9;
}
.upload-btn {
  border: 1px solid #2dcc70;
  color: #2dcc70;
  text-transform: none;
  width: 100%;
  font-size: 12px;
}
.upload-btn:hover {
  background-color: #2dcc70;
  color: #fff;
}
.remove-profile {
  border: 1px solid #ef4d56;
  color: #ef4d56;
  margin-top: 10px;
  text-transform: none;
  width: 100%;
  font-size: 12px;
}
.remove-profile:hover {
  background-color: #ef4d56;
  color: #fff;
}
/* Form Section Column */
.form-input {
  border: 1px solid #d8d8d8;
  border-radius: 5px;
  padding: 12px;
}
/* Cancel & Submit */
.button-section {
  display: flex;
  justify-content: center;
  gap: 15px;
}
.clear-btn {
  color: #7d7d7d;
  text-transform: none;
}
.clear-btn:hover {
  color: #fff;
  background-color: #7d7d7d;
}
.submit-btn {
  color: #2dcc70;
  text-transform: none;
}
.submit-btn:hover {
  background-color: #2dcc70;
  color: #fff;
}
.dropdown-container {
  position: relative;
  width: 100%;
}
.dropdown-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}
.dropdown-list-container {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.dropdown-list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.dropdown-item {
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}
.dropdown-item:hover {
  background-color: #f0f0f0;
}
.dropdown-no-results {
  padding: 10px;
  color: #888;
  text-align: center;
}
.select-wrapper {
  width: 100%;
  cursor: pointer;
}
.custom-select {
  width: 100%;
  padding: 10px;
  padding-right: 30px; /* Space for the icon */
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  appearance: none; /* Hide default dropdown arrow */
  background-color: white;
}
/* Style for the dropdown icon */
.dropdown-icon {
  position: absolute;
  right: 20px;
  top: 65%;
  transform: translateY(-50%);
  color: #666;
  transition: transform 0.3s ease-in-out;
  pointer-events: none;
}
.dropdown-icon.rotate {
  transform: translateY(-50%) rotate(180deg);
}
/* Responsive Design */

/* For Mobile Devices*/
@media (max-width: 600px) {
  .action-section {
    display: none;
  }
  .breadcrumbs {
    margin: 0px;
    padding: 0px;
    font-size: 12px;
  }
  .profile-section {
    text-align: center;
  }
}

/* For Tablets */
@media (min-width: 601px) and (max-width: 960px) {
  .breadcrumbs {
    font-size: 12px;
  }
}

/* For Laptops and Desktops */
@media (min-width: 961px) {
  .breadcrumbs {
    font-size: 14px;
  }
}
</style>
