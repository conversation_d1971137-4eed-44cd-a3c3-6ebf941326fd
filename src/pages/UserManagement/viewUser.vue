<template>
  <div class="page-wrapper page-view-user">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row
        align="center"
        justify="space-between"
      >
        <!-- Left Column: Header and Breadcrumbs -->
        <v-col cols="auto">
          <h2 class="page-title">
            {{ strings.viewUser.title }}
          </h2>
          <v-breadcrumbs
            :items="isSelfView ? MyAccountItems : items"
            class="breadcrumbs"
          >
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
        <!-- Right Column: Action Buttons -->
        <v-col
          cols="auto"
          class="page-action"
        >
          <div class="btn-toolbar">
            <v-btn
              variant="outlined"
              @click="editUserPage(user.id)"
            >
              <v-icon left>
                mdi-pencil
              </v-icon>{{ strings.viewUser.buttons.edit }}
            </v-btn>
            <v-btn
              variant="outlined"
              class="btn-outlined-dark"
              @click="goBack"
            >
              <v-icon left>
                mdi-chevron-left
              </v-icon>{{ strings.viewUser.buttons.back }}
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </div>

    <section class="page-content-wrapper">
      <!-- User Profile Section -->
      <section class="user-view-panel">
        <v-row
          align="center"
          justify="center"
        >
          <v-col
            cols="12"
            sm="12"
            md="6"
            lg="auto"
          >
            <ul class="custom-list-group">
              <li>
                <v-row>
                  <v-col
                    cols="12"
                    class="text-center profile_pic"
                  >
                    <div class="pt-3">
                      <v-avatar
                        size="100"
                        class="user-profile"
                      >
                        <template v-if="user.profile_pic">
                          <v-img
                            :src="user.profile_pic"
                            alt="User Profile"
                          />
                        </template>
                        <template v-else>
                          <span
                            class="initials"
                            :style="{ backgroundColor: getRandomColor(user) }"
                          >
                            {{ user.first_name.charAt(0).toUpperCase()
                            }}{{ user.last_name.charAt(0).toUpperCase() }}
                          </span>
                        </template>
                      </v-avatar>
                    </div>
                    <div class="pt-2">
                      <v-chip
                        :class="
                          user.status === 'Active'
                            ? 'user-active-badge'
                            : 'user-inactive-badge'
                        "
                      >
                        {{ user.status }}
                      </v-chip>
                    </div>
                  </v-col>
                </v-row>
              </li>
              <li>
                <v-row class="user-info">
                  <v-col cols="6">
                    <div>
                      <span class="title">{{ strings.viewUser.firstName }}</span>
                      <label class="user-value">{{ user.first_name }}</label>
                    </div>
                  </v-col>
                  <v-col cols="6">
                    <div>
                      <span class="title">{{ strings.viewUser.lastName }}</span>
                      <label class="user-value">{{ user.last_name }}</label>
                    </div>
                  </v-col>
                </v-row>
              </li>
              <li>
                <v-row class="user-info">
                  <v-col cols="6">
                    <div>
                      <span class="title">{{ strings.viewUser.employeeId }}</span>
                      <label class="user-value">{{ user.employee_id }}</label>
                    </div>
                  </v-col>
                  <v-col cols="6">
                    <div>
                      <span class="title">{{ strings.viewUser.role }}</span>
                      <label class="user-value">{{ user.role }}</label>
                    </div>
                  </v-col>
                </v-row>
              </li>
              <li>
                <v-row class="user-info">
                  <v-col cols="6">
                    <div>
                      <span class="title">{{
                        strings.viewUser.phoneNumber
                      }}</span>
                      <label class="user-value">{{ user.phone_number }}</label>
                    </div>
                  </v-col>
                  <v-col cols="6">
                    <div>
                      <span class="title">{{ strings.viewUser.location }}</span>
                      <label class="user-value">{{ user.location }}</label>
                    </div>
                  </v-col>
                </v-row>
              </li>
              <li>
                <v-row class="user-info">
                  <v-col>
                    <div>
                      <span class="title">{{ strings.viewUser.email }}</span>
                      <label class="user-value">{{ user.email }}</label>
                    </div>
                  </v-col>
                </v-row>
              </li>
            </ul>
          </v-col>
        </v-row>
      </section>
    </section>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref } from "vue";
import { getUserById } from "../../api/apiClient";
import { useRoute, type RouteParams } from "vue-router";
import { useRouter } from "vue-router";
import strings from "../../assets/strings.json";

export default defineComponent({
  name: "ViewUser",
  setup() {
    const route = useRoute();
    const router = useRouter();
    const userId = Number((route.params as { id: string }).id);
    const isSelfView = route.query.self;
    const items = ref([
      { title: "Home", href: "/" },
      { title: "User Management", href: "/manage-users" },
      { title: "Manage User", href: "/manage-users" },
      { title: "View User", href: "" },
    ]);
    const MyAccountItems = ref([
      { title: "Home", href: "/" },
      { title: "View User", href: "" },
    ]);

    const user = ref({
      id: 0,
      profile_pic: "",
      first_name: "",
      last_name: "",
      employee_id: "",
      role: "",
      location: "",
      email: "",
      phone_number: "",
      status: "",
    });
    const getRandomColor = (user: any) => {
      const colors = ["#FF5733", "#33FF57", "#3357FF", "#FF33A1"];
      const index =
        (user.first_name.charCodeAt(0) + user.last_name.charCodeAt(0)) %
        colors.length;
      return colors[index];
    };

    const goBack = () => {
      router.go(-1); // Go to the previous page in the browser's history
    };

    const editUserPage = (userId: number) => {
      router
        .push({
          name: "/UserManagement/editUser",
          params: { id: userId },
          query: { self: isSelfView },
        })
        .catch((err) => console.error("Navigation Error:", err));
      console.log("Edit Page", isSelfView);
    };

    onMounted(async () => {
      try {
        const getUser = await getUserById({ id: userId });
        const userData = getUser.data;
        const userStatus = computed(() =>
          userData.is_active ? "Active" : "Inactive"
        );
        user.value = {
          id: userData.id,
          profile_pic: userData.profile_pic,
          first_name: userData.first_name,
          last_name: userData.last_name,
          employee_id: userData.employee_id,
          role: userData.role_name,
          location: userData.location_name,
          email: userData.email,
          phone_number: userData.phone_number,
          status: userStatus.value,
        };
      } catch (error: any) {
        console.error("Error fetching user data:", error);
      }
    });

    return {
      items,
      user,
      userId,
      strings,
      MyAccountItems,
      isSelfView,
      getRandomColor,
      editUserPage,
      goBack,
    };
  },
});
</script>

<style scoped>
.view-user-container {
  /* margin: 16px; */
  padding: 16px;
  height: 100vh;
  overflow-y: auto;
}

.add-user-header {
  margin-bottom: 20px;
}

.breadcrumbs {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: grey;
}

.action-section {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.edit-btn {
  border: 1px solid #2dcc70;
  color: #2dcc70;
  text-transform: none;
}

.back-btn {
  border: 1px solid #7d7d7d;
  color: #7d7d7d;
  text-transform: none;
}

.view-container {
  display: block;
  width: 100%;
  max-width: 500px;
  border: 1px solid #d8d8d8;
  border-radius: 10px;
  margin: 0 auto;
}

.user-profile {
  margin: 0 auto;
}

.profile_pic {
  display: block;
}

.initials {
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 44px;
  height: 100%;
  width: 100%;
  border-radius: 50%;
}
.status-chip {
  padding: 0px 30px;
  text-align: center;
}
.user-info {
  display: flex;
}
.title {
  color: #8c8c8c;
}
.user-value {
  color: #2e2e2e;
  font-weight: 600;
}
</style>
