<template>
  <div class="page-wrapper page-manage-users">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row align="center" justify="space-between">
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">
              {{ strings.manageUser.title }}
            </h2>
            <v-chip size="x-small" class="data-count ml-2">
              Showing {{ userDetails.length }} of {{ totalUsers }} Records
            </v-chip>
          </div>
          <v-breadcrumbs :items="items" class="breadcrumbs">
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
        <v-col cols="auto" class="page-action">
          <div class="btn-toolbar">
            <v-btn
              variant="outlined"
              class="btn-outlined-secondary"
              @click="navigateToAddUser"
            >
              <v-icon>mdi-account-plus</v-icon
              >{{ strings.manageUser.addUserButton }}
            </v-btn>
            <v-btn variant="outlined" @click="openImportDialog">
              <v-icon>mdi-file-import</v-icon
              >{{ strings.manageUser.importButton }}
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Search and Filter User -->
    <section class="filter-wrapper">
      <v-row align="center" justify="space-between">
        <v-col cols="12" sm="12" md="9" lg="10">
          <v-row align="center">
            <v-col cols="12" lg="4" md="6" sm="12">
              <div class="form-group d-block">
                <input
                  id="searchValue"
                  v-model="searchValue"
                  type="text"
                  class="search-input"
                  :placeholder="strings.manageUser.searchAndFilter.search"
                />
              </div>
            </v-col>
            <v-col cols="12" lg="3" md="6" sm="3">
              <div class="form-group d-block">
                <select
                  id="status"
                  v-model="status"
                  :class="status === '' ? 'placeholder' : ''"
                  class="form-input"
                  :disabled="loading"
                >
                  <option class="dropdown-default-value" value="">
                    {{ strings.manageUser.searchAndFilter.statusFilter }}
                  </option>
                  <option
                    v-for="option in statusOptions"
                    :key="option"
                    :value="option"
                  >
                    {{ option }}
                  </option>
                </select>
              </div>
            </v-col>
            <v-col cols="12" lg="3" md="6" sm="3">
              <div class="form-group d-block">
                <select
                  id="role"
                  v-model="roles"
                  :class="roles === '' ? 'placeholder' : ''"
                  class="form-input"
                >
                  <option value="">
                    {{ strings.manageUser.searchAndFilter.roleFilter }}
                  </option>
                  <option
                    v-for="role in rolesOptions"
                    :key="role.id"
                    :value="role.role_name"
                  >
                    {{ role.role_name }}
                  </option>
                </select>
              </div>
            </v-col>
            <v-col cols="12" lg="2" md="6" sm="3">
              <div class="form-group d-block">
                <select
                  id="location"
                  v-model="locations"
                  :class="locations === '' ? 'placeholder' : ''"
                  class="form-input"
                >
                  <option value="">
                    {{ strings.manageUser.searchAndFilter.locationFilter }}
                  </option>
                  <option
                    v-for="location in locationOptions"
                    :key="location.id"
                    :value="location.location_name"
                  >
                    {{ location.location_name }}
                  </option>
                </select>
              </div>
            </v-col>
          </v-row>
        </v-col>
        <v-col>
          <div class="btn-toolbar">
            <v-btn
              icon="mdi-magnify"
              variant="tonal"
              @click="fetchFilteredUsers"
            />
            <v-btn icon="mdi-cached" variant="text" @click="resetFilter" />
          </div>
        </v-col>
      </v-row>
    </section>

    <!-- Page Content Begin Here -->
    <div class="page-content-wrapper">
      <!-- Loading Spinner -->
      <div v-if="loading" align="center" class="d-flex justify-content-center">
        <v-progress-circular indeterminate color="primary" />
      </div>

      <!-- Error Message -->
      <div v-if="errorMessage" class="error-message">
        <v-alert type="error" dismissible>
          {{ errorMessage }}
        </v-alert>
      </div>

      <!-- No Data Available -->
      <div
        v-if="noData && !loading && !errorMessage"
        class="no-data"
        align="center"
      >
        <div class="no-data-available-wrapper">
          <figure>
            <img
              src="../../assets/images/no-data-available.svg"
              alt="No Data Available"
            />
          </figure>
          <label>{{ strings.manageUser.noDataText }}</label>
          <small>{{ strings.common.noDataAvailableText }}</small>
        </div>
      </div>

      <!-- User Details Table -->
      <section class="card-list-wrapper">
        <v-card
          v-for="(user, index) in userDetails"
          :key="index"
          class="app-primary-card user-management-card"
        >
          <v-row align="center" justify="space-between">
            <!-- Avatar -->
            <v-col sm="12" md="3" lg="auto" class="user-avatar-sm">
              <div class="user-avatar-container">
                <v-avatar
                  size="32"
                  class="user-profile"
                  @mouseenter="showProfilePopup = user.id"
                  @mouseleave="showProfilePopup = null"
                >
                  <template v-if="user.profile_pic">
                    <v-img :src="user.profile_pic" alt="User Profile" />
                  </template>
                  <template v-else>
                    <span
                      class="initials"
                      :style="{ backgroundColor: getRandomColor(user) }"
                    >
                      {{ user.first_name.charAt(0).toUpperCase()
                      }}{{ user.last_name.charAt(0).toUpperCase() }}
                    </span>
                  </template>
                </v-avatar>

                <!-- Hover Popup -->
                <div
                  v-show="showProfilePopup === user.id"
                  class="profile-popup"
                  @mouseenter="showProfilePopup = user.id"
                  @mouseleave="showProfilePopup = null"
                >
                  <div class="popup-content">
                    <v-avatar size="80" class="popup-avatar">
                      <v-img
                        v-if="user.profile_pic"
                        :src="user.profile_pic"
                        alt="User Profile"
                      />
                      <span
                        v-else
                        class="popup-initials"
                        :style="{ backgroundColor: getRandomColor(user) }"
                        >{{
                          user.first_name.charAt(0).toUpperCase() +
                          user.last_name.charAt(0).toUpperCase()
                        }}</span
                      >
                    </v-avatar>
                  </div>
                </div>
              </div>
            </v-col>
            <!-- New Dev Code Begin Here -->

            <!-- <v-col sm="12" md="3" lg="3" class="user-data-details">
              <label class="user-data-header">Employee Info</label>
              <div class="user-data-value">N066 - Arunkumar Marisamy</div>
            </v-col>
            <v-col sm="12" md="3" lg="2" class="user-data-details">
              <label class="user-data-header">Role</label>
              <div class="user-data-value">Employee</div>
            </v-col>
            <v-col sm="12" md="3" lg="2" class="user-data-details">
              <label class="user-data-header">Location</label>
              <div class="user-data-value">Nexware-India</div>
            </v-col>
            <v-col sm="12" md="3" lg="3" class="user-data-details">
              <label class="user-data-header">Project</label>
              <div class="user-data-value">
                <ul>
                  <li>Project Name 1</li>
                  <li>
                    <v-btn>+6</v-btn>
                    <ol>
                      <li>Project Name 2</li>
                      <li>Project Name 3</li>
                      <li>Project Name 4</li>
                      <li>Project Name 5</li>
                      <li>Project Name 6</li>
                      <li>Project Name 7</li>
                    </ol>
                  </li>
                </ul>
              </div>
            </v-col> -->

            <!-- New Dev Code End Here -->
            <!-- Dynamic User Details -->
            <!-- <v-col cols="9">
              <v-row>
                <v-col
                  v-for="(field, fieldIndex) in [
                    { label: 'Employee ID', value: user.employee_id },
                    {
                      label: 'Full Name',
                      value: `${user.first_name} ${user.last_name}`,
                    },
                    { label: 'Role', value: user.role_name },
                    { label: 'Location', value: user.location_name },
                    {
                      label: 'Project',
                      value:
                        user.projects && user.projects.length
                          ? user.projects.map((p) => p.project_name).join(', ')
                          : 'No Project',
                    },
                  ]"
                  :key="fieldIndex"
                  cols="3"
                  class="user-data-details"
                >
                  <template v-if="field.label === 'Full Name'">
                    <div class="name-section">
                      <span class="user-data-header">{{ field.label }}</span>
                      <v-chip
                        :class="
                          user.is_active
                            ? 'user-active-badge'
                            : 'user-inactive-badge'
                        "
                        size="x-small"
                        variant="outlined"
                      >
                        <span
                          :class="{
                            color: user.is_active
                              ? 'user-active-badge'
                              : 'user-inactive-badge',
                          }"
                        >
                          {{ user.is_active ? "Active" : "Inactive" }}
                        </span>
                      </v-chip>
                    </div>
                    <div class="users-name">
                      {{ field.value }}
                    </div>
                  </template>
                  <template v-else>
                    <label class="user-data-header">{{ field.label }}</label>
                    <div class="user-data-value">
                      {{ field.value }}
                    </div>
                  </template>
                </v-col>
              </v-row>
            </v-col> -->

            <!-- <v-col cols="9">
              <v-row>
                <v-col
                  v-for="(field, fieldIndex) in [
                    { label: 'Employee ID', value: user.employee_id },
                    {
                      label: 'Full Name',
                      value: `${user.first_name} ${user.last_name}`,
                    },
                    { label: 'Role', value: user.role_name },
                    { label: 'Location', value: user.location_name },
                    {
                      label: 'Project',
                      value:
                        user.projects && user.projects.length
                          ? user.projects.map((p) => p.project_name).join(', ')
                          : 'N/A',
                    },
                  ]"
                  :key="fieldIndex"
                  cols="3"
                  class="user-data-details"
                >
                  <template v-if="field.label === 'Full Name'">
                    <div class="name-section">
                      <span class="user-data-header">{{ field.label }}</span>

                      <v-chip
                        :class="
                          user.is_active
                            ? 'user-active-badge'
                            : 'user-inactive-badge'
                        "
                        size="x-small"
                        variant="outlined"
                      >
                        <span
                          :class="{
                            color: user.is_active
                              ? 'user-active-badge'
                              : 'user-inactive-badge',
                          }"
                        >
                          {{ user.is_active ? "Active" : "Inactive" }}
                        </span>
                      </v-chip>
                    </div>
                    <div class="users-name">
                      {{ field.value }}
                    </div>
                  </template>
                  <template v-else>
                    <label class="user-data-header">{{ field.label }}</label>
                    <div class="user-data-value">
                      {{ field.value }}
                    </div>
                  </template>
                </v-col>
              </v-row>
            </v-col> -->
            <v-col sm="12" md="3" lg="3" class="user-data-details">
              <div class="name-section">
                <span class="user-data-header">Employee Info</span>
                <v-chip
                  :class="
                    user.is_active ? 'user-active-badge' : 'user-inactive-badge'
                  "
                  size="x-small"
                  variant="outlined"
                >
                  <span
                    :class="{
                      color: user.is_active
                        ? 'user-active-badge'
                        : 'user-inactive-badge',
                    }"
                  >
                    {{ user.is_active ? "Active" : "Inactive" }}
                  </span>
                </v-chip>
              </div>
              <div class="users-name">
                <span class="d-block font-weight-bold"
                  >{{ user.employee_id }} - {{ user.first_name }}
                  {{ user.last_name }}</span
                >
                <small class="d-block user-location-info">
                  <span>
                    <v-icon> mdi-map-marker </v-icon> {{ user.location_name }}
                  </span>
                </small>
              </div>
            </v-col>

            <v-col sm="12" md="3" lg="2" class="user-data-details">
              <label class="user-data-header">Role</label>
              <div class="user-data-value">{{ user.role_name }}</div>
            </v-col>

            <v-col sm="12" md="3" lg="3" class="user-data-details">
              <label class="user-data-header">Project</label>
              <div class="user-data-value">
                <ul
                  class="user-projects-wrapper"
                  v-if="(user.projects || []).length"
                >
                  <li>
                    {{
                      typeof user.projects?.[0] === "string"
                        ? user.projects[0]
                        : user.projects?.[0]?.project_name
                    }}
                  </li>
                  <li
                    v-if="(user.projects?.length ?? 0) > 1"
                    class="more-projects-root"
                  >
                    <v-menu open-on-hover offset-y location="bottom">
                      <template #activator="{ props }">
                        <v-btn
                          v-bind="props"
                          size="x-small"
                          variant="text"
                          :ripple="false"
                        >
                          +{{ (user.projects?.length ?? 0) - 1 }}
                        </v-btn>
                      </template>

                      <div class="more-project-v-list">
                        <ol>
                          <li
                            v-for="(project, index) in user.projects?.slice(1) ?? []"
                            :key="index"
                          >
                            {{
                              typeof project === "string"
                                ? project
                                : project.project_name
                            }}
                          </li>
                        </ol>
                      </div>
                    </v-menu>
                  </li>
                </ul>
                <span v-else>N/A</span>
              </div>
            </v-col>

            <!-- Actions -->
            <v-col sm="12" md="3" lg="3" class="user-actions">
              <div class="btn-toolbar">
                <v-btn
                  :class="
                    user.is_active === true ? 'btn-outlined-light' : 'btn-light'
                  "
                  :disabled="user.id === currentUser.id"
                  size="x-small"
                  variant="outlined"
                  icon
                  aria-label="Block User"
                  @click="openBlockDialog(user)"
                >
                  <v-icon :class="{ 'inactive-icon': !user.is_active }">
                    mdi-account-off
                  </v-icon>
                  <v-tooltip
                    v-if="user.is_active === true"
                    activator="parent"
                    location="top"
                  >
                    {{ strings.manageUser.tooltips.block }}
                  </v-tooltip>
                  <v-tooltip v-else activator="parent" location="top">
                    {{ strings.manageUser.tooltips.unBlock }}
                  </v-tooltip>
                </v-btn>
                <v-btn
                  class="btn-outlined-danger"
                  :disabled="user.id === currentUser.id"
                  size="x-small"
                  variant="outlined"
                  icon
                  aria-label="Delete User"
                  @click="openDeleteDialog(user)"
                >
                  <v-icon>mdi-account-minus</v-icon>
                  <v-tooltip activator="parent" location="top">
                    {{ strings.manageUser.tooltips.delete }}
                  </v-tooltip>
                </v-btn>
                <v-btn
                  class="btn-outlined-secondary"
                  :disabled="user.id === currentUser.id"
                  size="x-small"
                  variant="outlined"
                  icon
                  aria-label="Edit User"
                  @click="editUserPage(user.id)"
                >
                  <v-icon>mdi-pencil</v-icon>
                  <v-tooltip activator="parent" location="top">
                    {{ strings.manageUser.tooltips.edit }}
                  </v-tooltip>
                </v-btn>
                <v-btn
                  size="x-small"
                  variant="outlined"
                  icon
                  aria-label="View User"
                  @click="viewUserPage(user.id)"
                >
                  <v-icon>mdi-eye</v-icon>
                  <v-tooltip activator="parent" location="top">
                    {{ strings.manageUser.tooltips.view }}
                  </v-tooltip>
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </v-card>
        <div
          ref="bottomObserver"
          class="observer-element"
          style="height: 10px; margin: 10px 0"
        />

        <!-- Add loading indicator -->
        <div
          v-if="loadingMore"
          class="text-center my-4 d-flex justify-content-center"
        >
          <v-progress-circular indeterminate color="primary" />
        </div>
      </section>

      <v-dialog v-model="dialogVisible" max-width="420">
        <v-card class="dialog-confirmation negative-dialog-confirmation">
          <!-- Content -->
          <v-card-text>
            <div class="dialog-confirmation-icon">
              <v-icon> mdi-alert-circle-outline </v-icon>
            </div>
            <h3 class="dialog-confirmation-title">
              {{ dialogTitle }}
            </h3>
            <label class="dialog-confirmation-info">{{ dialogMessage }}</label>
          </v-card-text>

          <!-- Actions -->
          <v-card-actions>
            <v-btn variant="text" @click="cancelBlock">
              {{ strings.manageUser.cancelText }}
            </v-btn>
            <v-btn variant="tonal" @click="confirmBlock">
              {{ confirmButtonText }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <!-- Dialog Component -->
      <v-dialog v-model="deleteDialogVisible" max-width="420">
        <v-card class="dialog-confirmation negative-dialog-confirmation">
          <!-- Content -->
          <v-card-text>
            <div class="dialog-confirmation-icon">
              <v-icon> mdi-alert-circle-outline </v-icon>
            </div>
            <h3 class="dialog-confirmation-title">
              {{ strings.manageUser.deleteDialogTitle }}
            </h3>
            <label class="dialog-confirmation-info">
              {{ strings.manageUser.deleteDialogText }}
              <span class="d-block">{{ selectedUser.fullName }}?</span>
            </label>
          </v-card-text>

          <!-- Actions -->
          <v-card-actions>
            <v-btn variant="text" @click="cancelBlock">
              {{ strings.manageUser.cancelText }}
            </v-btn>
            <v-btn variant="tonal" @click="confirmDelete">
              {{ strings.manageUser.confirmDeleteText }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <!-- Import Dialog Component -->
      <v-dialog v-model="importDialogVisible" max-width="600">
        <v-card>
          <v-card-title>
            <h5>{{ strings.manageUser.importdialogHeader }}</h5>
            <v-btn
              icon="mdi-close"
              variant="text"
              class="dialog-close-btn"
              @click="importDialogVisible = false"
            />
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12">
                <!-- File Upload -->
                <v-file-upload
                  :key="fileInputKey"
                  clearable
                  density="compact"
                  title="Browse File"
                  variant="compact"
                  accept=".xls, .xlsx"
                  @change="handleFileUpload"
                />
                <div class="upload-help-block mt-2">
                  <label class="mb-1">{{
                    strings.manageUser.importSmapletext
                  }}</label>
                  <v-btn
                    variant="outlined"
                    class="btn-outlined-light"
                    @click="downloadSample"
                  >
                    {{ strings.manageUser.buttons.downloadSample }}
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-btn variant="text" @click="importDialogVisible = false">
              {{ strings.manageUser.buttons.cancel }}
            </v-btn>
            <v-btn
              variant="tonal"
              :loading="loading"
              :style="
                loading ? { backgroundColor: '#2dcc70', color: '#fff' } : {}
              "
              @click="uploadFile"
            >
              <template #loader>
                <v-progress-circular indeterminate size="20" class="spinner" />
              </template>
              {{ strings.common.uploadButton }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </div>
  </div>
</template>

<script lang="ts">
import {
  ref,
  onMounted,
  computed,
  watch,
  onUnmounted,
  nextTick,
  onUpdated,
} from "vue";
import { useToast } from "vue-toastification";
import { useRouter } from "vue-router";
import { debounce } from "lodash-es";
import {
  getAllUsers,
  getUserRoles,
  getLocations,
  blockUserById,
  removeUserById,
  searchAndFilter,
  importFile,
  getAllUsersPage,
} from "../../api/apiClient"; // Ensure this path is correct
import strings from "../../assets/strings.json";

// Define the interface for user details
interface User {
  id: number;
  profile_pic: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  role_name: string;
  location_name: string;
  reporting_to: number;
  email: string;
  projects?: (string | { project_name: string })[];
}

export default {
  name: "ManageAllUser",
  setup() {
    const toast = useToast();
    const router = useRouter();
    const items = ref([
      { title: "Home", href: "/" },
      { title: "User Management", href: "/manage-users" },
      { title: "Manage User", href: "" },
    ]);
    const dialogVisible = ref(false);
    const deleteDialogVisible = ref(false);
    const selectedUser = ref({
      fullName: "",
      id: 0,
      is_active: true,
    });
    const userColors = ref(new Map());
    const roles = ref("");
    const rolesOptions = ref<{ id: string; role_name: string }[]>([]);
    const locations = ref("");
    const locationOptions = ref<{ id: string; location_name: string }[]>([]);
    const status = ref("");
    const statusOptions = ref(["Active", "Inactive"]);
    const searchValue = ref("");

    const importDialogVisible = ref(false);
    const selectedFile = ref<File | null>(null);
    const fileInputKey = ref(0);

    // Specify the type of userDetails as an array of User
    const userDetails = ref<User[]>([]);
    const loading = ref(false);
    const errorMessage = ref("");
    const noData = ref(false);
    const observer = ref<IntersectionObserver | null>(null);
    const bottomObserver = ref<HTMLElement | null>(null);
    const loadingMore = ref(false);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const hasMore = ref(true);
    const pageSize = 10;
    const totalUsers = ref(0);
    const uploading = ref(false);
    const showMore = ref(false); //>>
    const showProfilePopup = ref<number | null>(null);

    const fetchUsers = async (isInitialLoad = false) => {
      if (loading.value || loadingMore.value || !hasMore.value) return;

      if (isInitialLoad) {
        loading.value = true;
      } else {
        loadingMore.value = true;
      }

      try {
        await new Promise((resolve) => setTimeout(resolve, 1000));

        const response = await getAllUsersPage(currentPage.value, pageSize);

        if (response && response.results && response.results.data) {
          if (isInitialLoad) {
            userDetails.value = response.results.data;
          } else {
            userDetails.value = [
              ...userDetails.value,
              ...response.results.data,
            ];
          }
          totalUsers.value = response.count;
          totalPages.value = response.total_pages;
          currentPage.value++;
          hasMore.value = currentPage.value <= totalPages.value;
        }
      } catch (error: any) {
        console.error("Error fetching users:", error);
        if (error.response?.status === 404) {
          // Redirect to custom 404 page
          router.push("/404");
        } else if (error.response?.status === 401) {
          // Optional: handle session expiration
          router.push("/session-expired");
        } else {
          toast.error("Failed to load users.");
        }
      } finally {
        loading.value = false;
        loadingMore.value = false;
      }
    };

    const loadMoreData = () => {
      // Check if any filter is set
      const isAnyFilterActive =
        searchValue.value.trim() !== "" ||
        status.value !== "" ||
        roles.value !== "" ||
        locations.value !== "";

      if (isAnyFilterActive) {
        // If a filter is set, load more with the filter API
        fetchFilteredUsers(false);
      } else {
        // Otherwise, call the normal fetch
        fetchUsers(false);
      }
    };

    const setupIntersectionObserver = () => {
      if (observer.value) observer.value.disconnect(); // ✅ Remove previous observer

      observer.value = new IntersectionObserver(
        (entries) => {
          if (
            entries[0].isIntersecting &&
            hasMore.value &&
            !loadingMore.value
          ) {
            loadMoreData();
          }
        },
        { rootMargin: "100px", threshold: 0.1 }
      );

      if (bottomObserver.value) observer.value.observe(bottomObserver.value);
    };

    const fetchFilteredUsers = async (isInitialLoad = true) => {
      if (loading.value || loadingMore.value) return;

      if (isInitialLoad) {
        loading.value = true;
        currentPage.value = 1;
        hasMore.value = true;
        userDetails.value = [];
      } else {
        loadingMore.value = true;
      }

      try {
        const selectedRole =
          rolesOptions.value.find((role) => role.role_name === roles.value)
            ?.id || "";
        const selectedLocation =
          locationOptions.value.find(
            (loc) => loc.location_name === locations.value
          )?.id || "";

        const payload = {
          search: searchValue.value || "",
          status: status.value || "",
          role: selectedRole || "",
          location: selectedLocation || "",
          page: currentPage.value,
          page_size: 10,
        };

        const response = await searchAndFilter(payload);

        if (response?.results?.data?.length) {
          const newUsers = response.results.data;

          if (isInitialLoad) {
            userDetails.value = newUsers;
          } else {
            userDetails.value = [...userDetails.value, ...newUsers];
          }

          totalUsers.value = response.count;
          totalPages.value = response.total_pages;
          currentPage.value += 1;
          hasMore.value = currentPage.value <= totalPages.value;
          noData.value = userDetails.value.length === 0;
        } else {
          noData.value = true;
          hasMore.value = false;
        }
      } catch (error: any) {
        console.error("Error fetching filtered users:", error);
        noData.value = true;
        hasMore.value = false;
      } finally {
        loading.value = false;
        loadingMore.value = false;
      }
    };

    const openImportDialog = () => {
      selectedFile.value = null;
      importDialogVisible.value = true;
    };

    const handleFileUpload = (event: Event) => {
      const target = event.target as HTMLInputElement;

      // Ensure target.files is not null
      if (target.files && target.files[0]) {
        selectedFile.value = target.files[0]; // Extract the first file
      } else {
        console.error("No file selected.");
        selectedFile.value = null;
      }
    };

    const user = JSON.parse(localStorage.getItem("user") || "{}");
    const currentUser = JSON.parse(localStorage.getItem("user") || "{}");

    const navigateToAddUser = () => {
      router.push({ name: "/UserManagement/addNewUser" });
    };

    const uploadFile = async () => {
      if (!selectedFile.value) {
        toast.error("Please select a file to upload.");
        loading.value = false;
        return;
      }

      try {
        loading.value = true;
        await nextTick();

        const userId = user.id.toString(); // Convert user ID to string
        const formData = new FormData();
        formData.append("file", selectedFile.value);
        formData.append("created_by", userId);
        formData.append("updated_by", userId);

        const response = await importFile(formData);

        if (response && response.message) {
          toast.success(response.message);
        } else {
          toast.error("Unexpected response from the server. Please try again.");
        }

        // Reset form state
        selectedFile.value = null;
        importDialogVisible.value = false;
        resetFilter();
      } catch (error: any) {
        console.error("Error uploading file:", error);

        if (error.response && error.response.data) {
          if (Array.isArray(error.response.data.error)) {
            // Display each error in a toast
            error.response.data.error.forEach((errMessage: string) => {
              toast.error(errMessage, { timeout: false });
            });
          } else {
            toast.error(
              error.response.data.error || "Failed to upload the file.",
              { timeout: false }
            );
          }
        } else {
          toast.error("An unexpected error occurred. Please try again.", {
            timeout: false,
          });
        }
      } finally {
        selectedFile.value = null;
        fileInputKey.value++;
        loading.value = false;
      }
    };

    const debouncedSearch = debounce((value: string) => {
      fetchFilteredUsers();
    }, 300);

    watch(searchValue, (newValue) => {
      debouncedSearch(newValue);
    });

    const dialogTitle = computed(() =>
      selectedUser.value.is_active ? "Block User" : "Unblock User"
    );

    const confirmButtonText = computed(() =>
      selectedUser.value.is_active ? "Yes, Block" : "Yes, Unblock"
    );

    const dialogMessage = computed(() =>
      selectedUser.value.is_active
        ? `Are you sure you want to block user ${selectedUser.value.fullName}?`
        : `Are you sure you want to unblock user ${selectedUser.value.fullName}?`
    );

    const openBlockDialog = (users: any) => {
      selectedUser.value.fullName = `${users.first_name} ${users.last_name}`;
      selectedUser.value.id = users.id;
      selectedUser.value.is_active = users.is_active;
      dialogVisible.value = true;
    };

    const openDeleteDialog = (user: any) => {
      selectedUser.value.fullName = `${user.first_name} ${user.last_name}`;
      selectedUser.value.id = user.id;
      deleteDialogVisible.value = true;
    };

    const cancelBlock = (user: any) => {
      dialogVisible.value = false;
      deleteDialogVisible.value = false;
    };

    const confirmBlock = async () => {
      try {
        // Determine the action based on the current user status
        const action = selectedUser.value.is_active ? "block" : "unblock";

        // Prepare the payload
        const payload = {
          id: selectedUser.value.id,
          action: action, // Send either "block" or "unblock"
          updated_by: user.id,
        };

        // Call the blockUserById function with the payload
        const response = await blockUserById(payload);

        if (response && response.message) {
          toast.success(response.message);
        } else {
          toast.success(
            `User ${selectedUser.value.fullName} has been ${action}ed successfully.`
          );
        }

        dialogVisible.value = false; // Close dialog after success
        resetFilter();
      } catch (error: any) {
        toast.error("Failed to update the user status. Please try again."); // Handle errors gracefully
        console.error("Error blocking/unblocking user:", error);
        dialogVisible.value = false; // Ensure dialog closes on error
      }
    };

    const confirmDelete = async () => {
      try {
        const payload = {
          id: selectedUser.value.id,
          updated_by: user.id,
        };
        const response = await removeUserById(payload);
        if (response && response.message) {
          toast.success(response.message);
        } else {
          toast.success(
            `User ${selectedUser.value.fullName} has been deleted successfully.`
          );
        }
        deleteDialogVisible.value = false;
        resetFilter();
      } catch (error: any) {
        toast.error("Failed to update the user status. Please try again."); // Handle errors gracefully
        console.error("Error blocking/unblocking user:", error);
        deleteDialogVisible.value = false; // Ensure dialog closes on error
      }
    };

    const editUserPage = (userId: number) => {
      router
        .push({ name: "/UserManagement/editUser", params: { id: userId } })
        .catch((err) => console.error("Navigation Error:", err));
    };

    const viewUserPage = (userId: number) => {
      router
        .push({ name: "/UserManagement/viewUser", params: { id: userId } })
        .catch((err) => console.error("Navigation Error:", err));
    };

    const generateRandomColor = () => {
      const letters = "0123456789ABCDEF";
      let color = "#";
      for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
      }
      return color;
    };

    const downloadSample = () => {
      const filePath = "/upload/User_Import_Template.xlsx";

      const anchor = document.createElement("a");
      anchor.href = filePath;
      anchor.download = "User_Import_Template.xlsx";

      document.body.appendChild(anchor);
      anchor.click();
      document.body.removeChild(anchor);
    };

    const getRandomColor = (user: any) => {
      if (!userColors.value.has(user.employee_id)) {
        userColors.value.set(user.employee_id, generateRandomColor());
      }
      return userColors.value.get(user.employee_id);
    };

    // Fetch all users data
    // const fetchAllUsers = async () => {
    //   loading.value = true;
    //   try {
    //     const response = await getAllUsers();
    //     const data = response.data.map((user: any) => ({
    //       id: user.id,
    //       profile_pic: user.profile_pic || null, // Default avatar if missing
    //       employee_id: user.employee_id,
    //       first_name: user.first_name,
    //       last_name: user.last_name,
    //       is_active: user.is_active,
    //       role_name: user.role_name,
    //       location_name: user.location_name,
    //     }));
    //     userDetails.value = data;
    //     if (data.length === 0) {
    //       noData.value = true;
    //     }
    //   }catch (error:any) {
    //     console.error("Error fetching users:", error);
    //     errorMessage.value = "Failed to fetch users. Please try again later.";
    //   } finally {
    //     loading.value = false;
    //   }
    // };

    const resetFilter = () => {
      roles.value = "";
      locations.value = "";
      status.value = "";
      searchValue.value = "";
      noData.value = false;
      currentPage.value = 1;
      hasMore.value = true;
      userDetails.value = [];
      fetchUsers(true);
    };

    onMounted(async () => {
      try {
        const rolesData = await getUserRoles();
        const locationData = await getLocations();

        rolesOptions.value = rolesData.data.map((role: any) => ({
          id: role.role_id,
          role_name: role.role_name,
        }));
        locationOptions.value = locationData.data.map((location: any) => ({
          id: location.location_id,
          location_name: location.location_name,
        }));

        await fetchUsers(true);
        setupIntersectionObserver();
      } catch (error: any) {
        console.error("Error in initialization:", error);
      }
    });

    onUnmounted(() => {
      if (observer.value) {
        observer.value.disconnect();
      }
    });

    // Watcher to clear file input when dialog is closed
    watch(importDialogVisible, (newValue) => {
      if (!newValue) {
        selectedFile.value = null;
        fileInputKey.value++;
      }
    });

    onUpdated(() => {
      nextTick(() => {
        if (bottomObserver.value && observer.value) {
          observer.value.disconnect();
          observer.value.observe(bottomObserver.value);
        }
      });
    });

    return {
      items,
      roles,
      rolesOptions,
      locations,
      locationOptions,
      status,
      statusOptions,
      searchValue,
      loading,
      errorMessage,
      userDetails,
      noData,
      dialogVisible,
      selectedUser,
      confirmButtonText,
      dialogTitle,
      dialogMessage,
      deleteDialogVisible,
      importDialogVisible,
      fileInputKey,
      strings,
      bottomObserver,
      loadingMore,
      hasMore,
      totalUsers,
      currentUser,
      showProfilePopup,
      downloadSample,
      uploadFile,
      openImportDialog,
      handleFileUpload,
      openDeleteDialog,
      confirmDelete,
      openBlockDialog,
      cancelBlock,
      confirmBlock,
      resetFilter,
      getRandomColor,
      fetchFilteredUsers,
      navigateToAddUser,
      viewUserPage,
      editUserPage,
      showMore,
    };
  },
};
</script>

<style scoped>
.spinner {
  color: #fff;
  width: 20px;
  height: 20px;
}

.manage-user-container {
  margin: 0px;
  padding: 12px;
  height: 100vh;
  font-family: "Roboto", sans-serif;
  overflow-y: auto;
}

.data-count {
  background-color: #026bb1;
  border-radius: 16px;
  color: #fff;
  padding: 4px 12px;
  font-size: 12px;
}
.breadcrumbs {
  padding: 0;
  margin: 0;
  font-size: 14px;
  margin-bottom: 1rem;
  color: #757575;
}
/* search & filter */
.search-filter-section {
  background-color: #f9f9f9;
  border-radius: 5px;
  margin: 5px;
  padding: 10px 5px;
}
.dropdown-default-value {
  color: #b5b5b5;
}
.form-section-container {
  padding: 12px;
}
.form-input,
.search-input {
  width: 100%;
  font-size: 14px;
  border: 1px solid #ccc;
  padding: 15px;
  border-radius: 4px;
  box-sizing: border-box;
  background-color: #fff;
}
.form-input {
  cursor: pointer;
}
.search-input {
  cursor: auto;
}
.form-input:focus {
  border-color: #000;
}
.placeholder {
  color: gray;
}

/* Dialog Model */
.block-dialog-card {
  text-align: center;
  padding: 20px;
}

.block-dialog-title {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.dialog-title-text {
  font-size: 30px;
  color: #333;
}

.dialog-content-text {
  font-size: 16px;
  color: #555;
}

.dialog-actions {
  display: flex;
  justify-content: space-evenly !important;
  gap: 6px;
}

.cancel-button {
  background-color: #f1f1f1;
  color: #7d7d7d;
  border: none;
}

.block-button {
  background-color: #ef4d56;
  color: #fff;
}

/* For Mobile Devices*/
@media (max-width: 600px) {
  .action-button {
    display: flex;
    justify-content: space-evenly;
  }
}

/* For Tablets */
@media (min-width: 601px) and (max-width: 960px) {
}

/* For Laptops and Desktops */
@media (min-width: 961px) {
}

/* User Avatar Hover Popup Styles */
.user-avatar-container {
  position: relative;
  display: inline-block;
}

.user-profile {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.user-profile:hover {
  transform: scale(1.05);
}

.profile-popup {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 10px;
  z-index: 1000;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e0e0e0;
  min-width: 100px;
  animation: fadeInUp 0.3s ease;
}

.profile-popup::before {
  content: "";
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
}

.popup-content {
  padding: 10px;
  text-align: center;
}

.popup-avatar {
  margin-bottom: 12px;
  border: 3px solid #f0f0f0;
}

.popup-initials {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #fff;
  font-weight: 700;
  font-size: 24px;
  border-radius: 50%;
}

.popup-user-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.popup-user-info p {
  margin: 0 0 2px 0;
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

.popup-user-info p.user-role {
  color: #026bb1;
  font-weight: 500;
  margin-top: 4px;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}
</style>
