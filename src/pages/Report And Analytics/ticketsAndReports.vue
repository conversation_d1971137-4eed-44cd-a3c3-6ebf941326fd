<template>
  <div class="page-wrapper">
    <v-row class="add-user-header align-center justify-space-between">
      <v-col
        cols="12"
        lg="10"
        md="10"
        sm="10"
      >
        <h3 class="page-title">
          {{ strings.ticketsReports.title }}
        </h3>
        <v-breadcrumbs
          :items="breadcrumbsItems"
          class="breadcrumbs"
        >
          <template #title="{ item }">
            {{ item.title }}
          </template>
        </v-breadcrumbs>
      </v-col>
    </v-row>

    <!-- Pie Chart Section -->
    <v-row>
      <v-col
        cols="12"
        lg="6"
      >
        <v-card>
          <v-card-text>
            <Datepicker
              v-model="yearlyDateRange"
              :enable-time-picker="false"
              range
              format="yyyy-MM-dd"
              placeholder="datepickerPlaceholder"
              class="daterange"
              :disabled-dates="disableFutureDates"
              :clearable="
                !yearlyDateRange || yearlyDateRange.some((date) => !date)
              "
            />
            <div class="chart-canvas-wrapper">
              <canvas ref="yearlyChart" />
            </div>
            <ChartComponent
            
              chart-type="apex"
              apex-type="bar"
              :title="strings.ticketsReports.yearlyReports"
              :labels="['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug']"
              :series="series"
              :height="350"
              :show-year-dropdown="true"
              :years="years"
              :default-year="selectedYearYearly"
              @year-changed="onYearlyChanged"
            />
          </v-card-text>
        </v-card>
      </v-col>
      <v-col
        cols="12"
        lg="6"
      >
        <v-card>
          <div class="chart-container pie-chart-container">
            <p class="chart-title">
              {{ strings.ticketsReports.categoryGraph }}
            </p>
            <canvas id="pie-chart" />
          </div>
        </v-card>
      </v-col>
      <v-col
        cols="12"
        lg="6"
      >
        <v-card>
          <!-- <div class="chart-container">
            <div class="chart-header d-flex justify-content-between">
              <p class="chart-title">
                {{ strings.ticketsReports.locationGraph }}
              </p>
              <select v-model="selectedYearCategory" class="native-dropdown">
                <option v-for="year in years" :key="year" :value="year">
                  {{ year }}
                </option>
              </select>
            </div>
            <div>
              <canvas ref="categoryChart" />
            </div>
          </div> -->
          <v-card-text>
            <ChartComponent
              chart-type="progress"
              title="Ticket Status Progress"
              :data="progressData"
              :height="250"
              :show-header="true"
            />
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, nextTick, watch, defineComponent } from "vue";
import Chart from "chart.js/auto"; // Import Chart.js
import {
  getCategoryReportData,
  getLocationReportData,
  getTickets,
  getTicketSeriesByHour,
  getYearlyReportData,
} from "../../api/apiClient"; // Ensure the API function is correctly imported
import strings from "../../assets/strings.json";
import ChartComponent from "../../components/ChartComponent.vue";
// import type { ApexAxisChartSeries } from 'apexcharts';
type ApexAxisChartSeries = Array<any>;
interface YearlyReportItem {
  month: string;
  open: number;
  solved: number;
  closed: number;
}

interface ChartDataItem {
  label: string;
  value: number;
}
type ChartJSDataset = {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
};

type ApexSeriesItem = {
  name: string;
  data: number[];
};

export default defineComponent({
  name: "Dashboard",
  // components: {
  //   ChartComponent,
  // },

  setup() {
    const breadcrumbsItems = ref([
      { title: "Home", href: "/" },
      { title: "Report & Analytics", href: "/" },
      { title: "Tickets & Reports", href: "/" },
    ]);
    const ticketCount = ref({
      total_tickets: 0,
      unassigned_tickets: 0,
      pending_tickets: 0,
      solved_tickets: 0,
      closed_tickets: 0,
    });

    const generateYears = () => {
      const currentYear = new Date().getFullYear();
      const startYear = 2016;
      years.value = Array.from(
        { length: currentYear - startYear + 1 },
        (_, i) => startYear + i
      );
    };
    const today = new Date();
    const disableFutureDates = (date: Date) => {
      return date > today;
    };
    const startDate = new Date();
    startDate.setDate(today.getDate() - 29); // 30 days including today
    const selectedYearYearly = ref<number>(new Date().getFullYear());
    const selectedYearCategory = ref<number>(new Date().getFullYear());
    const selectedYearEmployee = ref<number>(new Date().getFullYear());
    const yearlyDateRange = ref<[Date, Date]>([startDate, today]);

    const employeeReport = ref<any[]>([]);
    const years = ref<number[]>([]);
    const chartData = ref([]);
    const yearlyChart = ref<HTMLCanvasElement | null>(null);
    let chartInstance: Chart | null = null;

    const tableHeaders = ref([
      { title: "Employee ID", key: "employee_id" },
      { title: "Name", key: "name" },
      { title: "Total Tickets", key: "ticket_count" },
    ]);

    // Fetch user details
    const user = JSON.parse(localStorage.getItem("user") || "{}");

    const categoryChart = ref<HTMLCanvasElement | null>(null);
    const categoryChartInstance: Chart | null = null;

    const fetchReportData = async () => {
      try {
        const response = await getLocationReportData();
        chartData.value = response.data.map((item: any) => ({
          label: item.location_name,
          value: item.total_tickets,
        }));
        createPieChart(); // Call the chart creation method after fetching data
      } catch (error: any) {
        console.error("Failed to fetch report data", error);
      }
    };

    const createPieChart = () => {
      // Get the canvas element and cast it to HTMLCanvasElement
      const ctx = document.getElementById("pie-chart") as HTMLCanvasElement;
      if (!ctx) return; // Ensure the element exists

      const labels = chartData.value.map((item: any) => item.label);
      const data = chartData.value.map((item: any) => item.value);
      const colors = chartData.value.map(
        () =>
          `rgba(${Math.floor(Math.random() * 255)}, ${Math.floor(
            Math.random() * 255
          )}, ${Math.floor(Math.random() * 255)}, 0.6)`
      );

      // Create the pie chart
      new Chart(ctx, {
        type: "pie",
        data: {
          labels: labels,
          datasets: [
            {
              label: "Tickets by Location",
              data: data,
              backgroundColor: colors,
              borderColor: colors.map((color) => color.replace("0.6", "1")),
              borderWidth: 1,
            },
          ],
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: "bottom",
            },
          },
        },
      });
    };

    onMounted(async () => {
      try {
        generateYears();
        // await fetchCategoryReportData();
        // await fetchYearlyReportData();
        fetchReportData();
      } catch (error: any) {
        console.error(error);
      }
    });

    const series = ref<ApexAxisChartSeries>([]);
    const categories = ref<string[]>([]);
    const selectedCategory = ref<string>("");
    const selectedProject = ref<string>("");

    onMounted(async () => {
      await fetchYearlyReportData();
      const data = await getTickets(); // Should return Ticket[]
      const { series: s, categories: c } = getTicketSeriesByHour(
        data,
        selectedCategory.value,
        selectedProject.value
      );

      series.value = s;
      categories.value = c;
    });

    watch(selectedYearYearly, async () => {
      // await fetchYearlyReportData();
    });

    watch(selectedYearCategory, async () => {
      // await fetchCategoryReportData();
    });
    watch(
      yearlyDateRange,
      async (newRange) => {
        console.log("yearlyDateRange changed:", newRange);
        if (newRange && newRange.length === 2) {
          await fetchYearlyReportData();
        }
      },
      { deep: true, immediate: true }
    );

    const progressData = ref([
      { label: "Open Tickets", value: 45, percentage: 75, color: "#FF6384" },
      { label: "In Progress", value: 30, percentage: 50, color: "#36A2EB" },
      { label: "Resolved", value: 60, percentage: 100, color: "#4BC0C0" },
      { label: "Closed", value: 25, percentage: 42, color: "#FFCE56" },
    ]);

    const locationLabels = ref<string[]>([
      "Office A",
      "Office B",
      "Office C",
      "Remote",
    ]);
    const locationDatasets = ref<ChartJSDataset[]>([
      {
        label: "Tickets by Location",
        data: [25, 35, 20, 15],
        backgroundColor: ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"],
        borderColor: ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"],
        borderWidth: 1,
      },
    ]);

    const pieChartOptions = ref({
      legend: {
        position: "bottom",
      },
      responsive: true,
    });

    // Event handlers for year changes
    const onYearlyChanged = (year: number) => {
      selectedYearYearly.value = year;
      console.log("Yearly chart year changed to:", year);
      // Here you would fetch new data based on the selected year
    };

    const onCategoryYearChanged = (year: number) => {
      selectedYearCategory.value = year;
      console.log("Category chart year changed to:", year);
      // Here you would fetch new data based on the selected year
    };

    // Dummy data for the 'series' prop; replace with real data as needed
    const fetchYearlyReportData = async () => {
      try {
        if (!yearlyDateRange.value || yearlyDateRange.value.length !== 2) {
          console.error(
            "Please select a valid date range for the yearly report."
          );
          return;
        }

        const startDate = formatDate(yearlyDateRange.value[0]);
        const endDate = formatDate(yearlyDateRange.value[1]);

        const response = await getYearlyReportData({
          start_date: startDate,
          end_date: endDate,
          role: user.role,
          user_id: user.id,
        });

        // Make sure response?.data is in the format you expect
        if (response?.data?.data?.length) {
          const reportData = response.data.data;
          // Example: "period" is your x-axis label from the backend
          const labels = reportData.map((item: any) => item.period);
          const openTickets = reportData.map(
            (item: any) => item.tickets_count.open
          );
          const solvedTickets = reportData.map(
            (item: any) => item.tickets_count.solved
          );
          const closedTickets = reportData.map(
            (item: any) => item.tickets_count.closed
          );

          await nextTick();
          renderChart(labels, openTickets, solvedTickets, closedTickets);
        } else {
          console.warn("Empty or invalid response for yearly report.");
        }
      } catch (error: any) {
        console.error("Failed to fetch yearly report data:", error);
      }
    };
    const formatDate = (date: Date): string => {
      return `${date.getFullYear()}-${(date.getMonth() + 1)
        .toString()
        .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
    };

    const renderChart = (
      labels: string[],
      open: number[],
      solved: number[],
      closed: number[]
    ) => {
      if (!yearlyChart.value) {
        console.error("Chart canvas element is missing.");
        return;
      }

      if (chartInstance) {
        chartInstance.destroy();
      }

      chartInstance = new Chart(yearlyChart.value, {
        type: "bar",
        data: {
          labels, // These labels come directly from the backend "period" field.
          datasets: [
            {
              label: "Open Tickets",
              type: "bar",
              data: open,
              backgroundColor: "rgba(255, 99, 132, 0.6)",
              borderWidth: 1,
            },
            {
              label: "Solved Tickets",
              type: "line",
              data: solved,
              borderColor: "rgba(54, 162, 235, 1)",
              borderWidth: 2,
              fill: false,
            },
            {
              label: "Closed Tickets",
              type: "line",
              data: closed,
              backgroundColor: "rgba(255, 0, 0, 1)",
              borderWidth: 2,
              fill: false,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              title: {
                display: true,
                text: strings.dashboard.yearlyReportLabel.lableXAxis,
              },
            },
            y: {
              beginAtZero: true,
              suggestedMax: Math.max(...open, ...solved, ...closed) + 5,
              title: {
                display: true,
                text: strings.dashboard.yearlyReportLabel.lableYAxis,
              },
              ticks: {
                stepSize: 5,
                maxTicksLimit: 10,
              },
            },
          },
        },
      });
    };

    return {
      user,
      breadcrumbsItems,
      ticketCount,
      years,
      selectedYearEmployee,
      selectedYearYearly,
      selectedYearCategory,
      tableHeaders,
      categoryChart,
      yearlyChart,
      strings,
      progressData,
      locationLabels,
      locationDatasets,
      pieChartOptions,
      onYearlyChanged,
      onCategoryYearChanged,
      series,
      yearlyDateRange,
      disableFutureDates,
    };
  },
});
</script>

<style scoped>
.add-user-header {
  margin-bottom: 20px;
}

.breadcrumbs {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: grey;
}
.native-dropdown {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  display: flex;
  justify-content: space-between;
  padding: 5px 18px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  outline: none;
}
.chart-container {
  border-radius: 5px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
canvas {
  flex-grow: 1;
  width: 100%;
  height: auto;
}

.pie-chart-container {
  height: 300px; /* Adjust the height as needed */
  width: 300px; /* Adjust the width as needed */
  margin: 0 auto; /* Center align */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

#pie-chart {
  width: 100%;
  height: 100%;
}
</style>
