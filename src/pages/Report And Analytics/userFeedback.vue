<template>
  <div class="page-wrapper">
    <div class="technician-feedback-container">
      <v-row class="add-user-header align-center justify-space-between">
        <v-col
          cols="12"
          lg="10"
          md="10"
          sm="10"
        >
          <h3 class="page-title">
            {{ strings.userFeedback.title }}
          </h3>
          <v-breadcrumbs
            :items="breadcrumbsItems"
            class="breadcrumbs"
          >
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
        <v-col
          cols="12"
          lg="2"
        >
          <div class="dropdown-wrapper">
            <select
              v-model="selectedYear"
              class="native-dropdown"
            >
              <option
                v-for="year in years"
                :key="year"
                :value="year"
              >
                {{ year }}
              </option>
            </select>
          </div>
        </v-col>
      </v-row>

      <!-- Chart Section -->
      <v-row>
        <v-col cols="6">
          <v-card class="py-4">
            <div class="chart-container">
              <p class="chart-title">
                {{ strings.userFeedback.technicainFeedback }}
              </p>
              <canvas id="feedbackBarChart" />
            </div>
          </v-card>
        </v-col>
        <v-col cols="6">
          <v-card class="py-4">
            <div class="chart-container">
              <p class="chart-title">
                {{ strings.userFeedback.categoryFeedback }}
              </p>
              <canvas id="categoryBarChart" />
            </div>
          </v-card>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, onMounted } from "vue";
import Chart from "chart.js/auto";
import {
  getTechnicianFeedback,
  getCategoriesFeedback,
} from "../../api/apiClient";
import strings from "../../assets/strings.json";

export default {
  name: "UserFeedback",
  setup() {
    const breadcrumbsItems = ref([
      { title: "Home", href: "/" },
      { title: "Tickets & Reports", href: "/user-management" },
    ]);

    const generateYears = () => {
      const currentYear = new Date().getFullYear();
      const startYear = 2000;
      const yearsArray = [];
      for (let year = startYear; year <= currentYear; year++) {
        yearsArray.push(year.toString());
      }
      return yearsArray;
    };

    const years = ref(generateYears());
    const selectedYear = ref<number>(new Date().getFullYear());

    const feedbackData = ref([]);
    const categoryFeedbackData = ref([]);

    const fetchFeedbackData = async () => {
      try {
        const response = await getTechnicianFeedback();
        feedbackData.value = response.data;
        createHorizontalBarChart();
      } catch (error: any) {
        console.error("Failed to fetch feedback data:", error);
      }
    };

    const fetchCategoryFeedbackData = async () => {
      try {
        const response = await getCategoriesFeedback();
        categoryFeedbackData.value = response.data;
        createCategoryBarChart();
      } catch (error: any) {
        console.error("Failed to fetch category feedback data:", error);
      }
    };

    const createHorizontalBarChart = () => {
      const ctx = document.getElementById(
        "feedbackBarChart"
      ) as HTMLCanvasElement;
      if (!ctx) return;

      const labels = feedbackData.value.map(
        (item: any) => item.technician_name
      );
      const data = feedbackData.value.map((item: any) => item.feedback);

      const backgroundColors = feedbackData.value.map((item: any) => {
        switch (item.feedback) {
          case 1:
            return "red";
          case 2:
            return "lightcoral";
          case 3:
            return "yellow";
          case 4:
            return "lightgreen";
          case 5:
            return "darkgreen";
          default:
            return "gray";
        }
      });

      new Chart(ctx, {
        type: "bar",
        data: {
          labels: labels,
          datasets: [
            {
              label: "Feedback Score",
              data: data,
              backgroundColor: backgroundColors,
              borderColor: "black",
              borderWidth: 1,
            },
          ],
        },
        options: {
          responsive: true,
          indexAxis: "y",
          plugins: {
            legend: { display: false },
          },
          scales: {
            x: {
              title: {
                display: true,
                text: "Feedback Score",
              },
              beginAtZero: true,
              max: 5,
            },
            y: {
              title: {
                display: true,
                text: "Technicians",
              },
            },
          },
        },
      });
    };

    const createCategoryBarChart = () => {
      const ctx = document.getElementById(
        "categoryBarChart"
      ) as HTMLCanvasElement;

      if (!ctx) return;

      const labels = categoryFeedbackData.value.map(
        (item: any) => item.category_name
      );

      const ticketCounts = categoryFeedbackData.value.map(
        (item: any) => item.ticket_count
      );

      const feedbackCounts = categoryFeedbackData.value.map(
        (item: any) => item.feedback_count
      );

      new Chart(ctx, {
        type: "bar",
        data: {
          labels: labels,
          datasets: [
            {
              label: "Ticket Count",
              data: ticketCounts,
              backgroundColor: "#3b82f6",
              stack: "Stack 1",
            },
            {
              label: "Feedback Count",
              data: feedbackCounts,
              backgroundColor: "#10b981",
              stack: "Stack 1",
            },
          ],
        },
        options: {
          responsive: true,
          plugins: {
            legend: { display: true },
            title: {
              display: true,
              text: "Category-wise Tickets & Feedbacks (Stacked)",
            },
          },
          scales: {
            x: {
              stacked: true,
              title: {
                display: true,
                text: "Categories",
              },
            },
            y: {
              stacked: true,
              beginAtZero: true,
              title: {
                display: true,
                text: "Count",
              },
            },
          },
        },
      });
    };

    onMounted(() => {
      fetchFeedbackData();
      fetchCategoryFeedbackData();
    });

    return {
      breadcrumbsItems,
      years,
      selectedYear,
      strings,
    };
  },
};
</script>

<style scoped>
.native-dropdown {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  display: flex;
  justify-content: space-between;
  padding: 5px 18px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  outline: none;
}
.technician-feedback-container {
  height: 100vh;
}
.add-user-header {
  margin-bottom: 20px;
}
.chart-container {
  margin-top: 20px;
  width: 80%;
  margin: 0 auto;
}
</style>
