<template>
  <div class="page-wrapper">
    <div class="technician-report-container">
      <v-row class="add-user-header align-center justify-space-between">
        <v-col
          cols="12"
          lg="10"
          md="10"
          sm="10"
        >
          <h3 class="page-title">
            {{ strings.technicianPerformence.title }}
          </h3>
          <v-breadcrumbs
            :items="breadcrumbsItems"
            class="breadcrumbs"
          >
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
        <v-col
          cols="12"
          lg="2"
        >
          <div class="dropdown-wrapper">
            <select
              v-model="selectedYear"
              class="native-dropdown"
            >
              <option
                v-for="year in years"
                :key="year"
                :value="year"
              >
                {{ year }}
              </option>
            </select>
          </div>
        </v-col>
      </v-row>

      <!-- Chart.js Bar Chart -->
      <v-row>
        <v-col cols="6">
          <v-card>
            <div class="chart-container">
              <p class="chart-title">
                {{ strings.technicianPerformence.categoryGraph }}
              </p>
              <canvas id="groupedBarChart" />
            </div>
          </v-card>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, onMounted } from "vue";
import Chart from "chart.js/auto";
import { getTechnicianCategoryReport } from "../../api/apiClient";
import { getCategorySubcategoryHoursReport } from "../../api/apiClient";

import strings from "../../assets/strings.json";

export default {
  name: "GroupedBarChart",
  setup() {
    const breadcrumbsItems = ref([
      { title: "Home", href: "/" },
      { title: "Tickets & Reports", href: "/user-management" },
    ]);
    const generateYears = () => {
      const currentYear = new Date().getFullYear();
      const startYear = 2000;
      const yearsArray = [];
      for (let year = startYear; year <= currentYear; year++) {
        yearsArray.push(year.toString());
      }
      return yearsArray;
    };

    const years = ref(generateYears());
    const selectedYear = ref<number>(new Date().getFullYear());
    const yearlyChartData = ref([]);
    const generateRandomColor = () => {
      const letters = "0123456789ABCDEF";
      let color = "#";
      for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
      }
      return color;
    };

    const initializeChart = async () => {
      try {
        // Fetch API data
        const response = await getTechnicianCategoryReport();
        console.log("API Response:", response);

        const technicians = response.data || [];
        const labels: string[] = [];
        const datasets: any[] = [];

        // Generate labels (category names) and datasets dynamically
        technicians.forEach((technician: any) => {
          const technicianDataset = {
            label: technician.name, // Use technician name as label
            data: technician.data.map((category: any) => {
              if (!labels.includes(category.category_name)) {
                labels.push(category.category_name); // Add unique category name to labels
              }
              return category.total_tickets; // Map ticket counts
            }),
            backgroundColor: generateRandomColor(),
          };
          datasets.push(technicianDataset);
        });

        console.log("Labels:", labels);
        console.log("Datasets:", datasets);

        // Initialize Chart.js
        const ctx = document.getElementById(
          "groupedBarChart"
        ) as HTMLCanvasElement;
        new Chart(ctx, {
          type: "bar",
          data: {
            labels, // Dynamic labels (categories)
            datasets, // Dynamic datasets (technicians)
          },
          options: {
            responsive: true,
            plugins: {
              legend: { display: true, position: "top" },
              tooltip: { enabled: true },
            },
            scales: {
              x: { title: { display: true, text: "Categories" } },
              y: {
                title: { display: true, text: "Total Tickets" },
                beginAtZero: true,
              },
            },
          },
        });
      } catch (error: any) {
        console.error("Error initializing chart:", error);
      }
    };
    const initializeCharts = async () => {
      try {
        const response = await getCategorySubcategoryHoursReport();
        const categories = response.data || [];

        const labels: string[] = [];
        const datasets: any[] = [];

        categories.forEach((category: any) => {
          const categoryDataset = {
            label: category.category_name,
            data: [] as number[],
            backgroundColor: generateRandomColor(),
          };

          category.data.forEach((sub: any) => {
            if (!labels.includes(sub.subcategory_name)) {
              labels.push(sub.subcategory_name);
            }
          });

          // Fill the dataset data in the same order as labels
          labels.forEach((label) => {
            const subData = category.data.find(
              (sub: any) => sub.subcategory_name === label
            );
            categoryDataset.data.push(subData ? subData.working_hours : 0);
          });

          datasets.push(categoryDataset);
        });

        const ctx = document.getElementById(
          "categorySubcategoryChart"
        ) as HTMLCanvasElement;
        new Chart(ctx, {
          type: "bar",
          data: {
            labels,
            datasets,
          },
          options: {
            responsive: true,
            plugins: {
              legend: { display: true, position: "top" },
              tooltip: { enabled: true },
            },
            scales: {
              x: { title: { display: true, text: "SubCategories" } },
              y: {
                title: { display: true, text: "Working Hours" },
                beginAtZero: true,
              },
            },
          },
        });
      } catch (error) {
        console.error("Chart loading error:", error);
      }
    };

    onMounted(() => {
      initializeChart();
      initializeCharts();
    });

    return {
      breadcrumbsItems,
      years,
      selectedYear,
      strings,
    };
  },
};
</script>

<style scoped>
.native-dropdown {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  display: flex;
  justify-content: space-between;
  padding: 5px 18px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  outline: none;
}
.technician-report-container {
  height: 100vh;
}
.add-user-header {
  margin-bottom: 20px;
}

.chart-container {
  /* border: 1px solid black; */
  border-radius: 5px;
  margin: 20px auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
