<template>
  <div class="page-wrapper page-ticket-category">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row
        align="center"
        justify="space-between"
      >
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">
              {{ strings.ticketCategories.title }}
            </h2>
            <v-chip
              size="x-small"
              class="data-count ml-2"
            >
              Showing {{ categories.length }} of {{ totalCategory }} Records
            </v-chip>
          </div>
          <v-breadcrumbs
            :items="items"
            class="breadcrumbs"
          >
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
        <v-col
          cols="auto"
          class="page-action action-button"
        >
          <v-btn
            variant="outlined"
            class="add-user-btn"
            aria-label="Add Category"
            @click.stop="handleAddCategory"
          >
            <v-icon>mdi-folder-multiple</v-icon> {{ strings.common.addCategoryButton }}
          </v-btn>
        </v-col>
      </v-row>
    </div>

    <section class="page-content-wrapper">
      <!-- Loading Spinner -->
      <div
        v-if="loading"
        align="center"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>

      <!-- No Data Available -->
      <div
        v-if="!loading && categories.length === 0"
        class="no-data"
        align="center"
      >
        <div class="no-data-available-wrapper">
          <figure>
            <img
              src="../../assets/images/no-data-available.svg"
              alt="No Data Available"
            >
          </figure>
          <label>{{ strings.ticketCategories.noDataAvailable }}</label>
          <small>{{ strings.common.noDataAvailableText }}</small>
        </div>
      </div>

      <!-- Categories List -->
      <section class="card-list-wrapper pt-3">
        <v-card
          v-for="category in categories"
          :key="category.id"
          class="category-card app-primary-card"
        >
          <v-row
            align="center"
            justify="space-between"
          >
            <v-col cols="2">
              <div class="title">
                {{ strings.ticketCategories.cardHeaders.category }}
              </div>
              <div class="value">
                {{ category.name }}
              </div>
            </v-col>
            <v-col cols="2">
              <div class="title">
                {{ strings.ticketCategories.cardHeaders.subcategory }}
              </div>
              <div class="value d-flex align-center">
                <span>{{ getFirstVisibleSubcategory(category) }}</span>

                <v-tooltip
                  v-if="
                    category.subcategories.filter((sub) => sub.is_active).length -
                      1 >
                      0
                  "
                  location="top"
                >
                  <template #activator="{ props }">
                    <v-chip
                      class="sub-chip"
                      size="x-small"
                      v-bind="props"
                    >
                      +{{
                        category.subcategories.filter((sub) => sub.is_active)
                          .length - 1
                      }}
                    </v-chip>
                  </template>
                  <template #default>
                    <div class="tooltip-list">
                      <ul>
                        <li
                          v-for="(sub, index) in category.subcategories.filter(
                            (sub) => sub.is_active
                          )"
                          :key="index"
                        >
                          {{ sub.subcat_name }}
                        </li>
                      </ul>
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </v-col>
            <v-col cols="2">
              <div class="title">
                {{ strings.ticketCategories.cardHeaders.is_active }}
              </div>
              <div class="value">
                <v-chip
                  :class="
                    category.is_active
                      ? 'user-active-badge'
                      : 'user-inactive-badge'
                  "
                  size="x-small"
                  variant="outlined"
                >
                  <span
                    :class="{
                      color: category.is_active
                        ? 'user-active-badge'
                        : 'user-inactive-badge',
                    }"
                  >{{ category.is_active ? "Active" : "Inactive" }}</span>
                </v-chip>
              </div>
            </v-col>
            <!-- Created By -->
            <v-col cols="2">
              <div class="title">
                {{ strings.ticketCategories.cardHeaders.createdBy }}
              </div>
              <div class="value">
                {{ category.created_by_fullname }}
              </div>
            </v-col>

            <!-- Updated By -->
            <v-col cols="2">
              <div class="title">
                {{ strings.ticketCategories.cardHeaders.updatedBy }}
              </div>
              <div class="value">
                {{ category.updated_by_fullname }}
              </div>
            </v-col>

            <v-col
              cols="2"
              class="text-right"
            >
              <div class="actions">
                <div class="btn-toolbar">
                  <v-tooltip location="top">
                    <template #activator="{ props }">
                      <v-btn
                        v-bind="props"
                        icon
                        size="small"
                        class="btn-outlined-secondary"
                        :disabled="!category.is_active"
                        variant="outlined"
                        @click="handleEditCategory(category)"
                      >
                        <v-icon>mdi-pencil</v-icon>
                      </v-btn>
                    </template>
                    {{ strings.common.editButton }}
                  </v-tooltip>

                  <v-tooltip location="top">
                    <template #activator="{ props }">
                      <v-btn
                        v-bind="props"
                        icon
                        size="small"
                        class="view-btn"
                        :disabled="!category.is_active"
                        variant="outlined"
                        @click="handleViewCategory(category)"
                      >
                        <v-icon>mdi-eye</v-icon>
                      </v-btn>
                    </template>
                    {{ strings.common.viewButton }}
                  </v-tooltip>
                  <!-- Action Button + Tooltip -->
                  <v-tooltip location="top">
                    <template #activator="{ props }">
                      <v-btn
                        v-bind="props"
                        icon
                        size="small"
                        class="btn-outlined-danger"
                        variant="outlined"
                        @click="handleToggleCategoryStatus(category)"
                      >
                        <v-icon>
                          {{
                            category.is_active
                              ? "mdi-block-helper"
                              : "mdi-check-circle-outline"
                          }}
                        </v-icon>
                      </v-btn>
                    </template>
                    <span>{{
                      category.is_active
                        ? "Deactivate Category"
                        : "Activate Category"
                    }}</span>
                  </v-tooltip>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-card>
      </section>

      <!-- Observer Element -->
      <div
        ref="bottomObserver"
        class="observer-element"
      />

      <!-- Loading Indicator -->
      <div
        v-if="loadingMore"
        class="text-center my-4"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>

      <!-- Delete Dialog -->
      <v-dialog
        v-model="showDeleteDialog"
        max-width="420"
      >
        <v-card
          :class="[
            'dialog-confirmation',
            selectedCategory?.is_active
              ? 'negative-dialog-confirmation'
              : 'positive-dialog-confirmation',
          ]"
        >
          <v-card-text>
            <div class="dialog-confirmation-icon">
              <v-icon>
                {{
                  selectedCategory?.is_active
                    ? "mdi-alert-circle-outline"
                    : "mdi-check-circle-outline"
                }}
              </v-icon>
            </div>
            <h3 class="dialog-confirmation-title">
              {{
                selectedCategory?.is_active
                  ? "Deactivate Category"
                  : "Activate Category"
              }}
            </h3>
            <label class="dialog-confirmation-info">
              {{ strings.ticketCategories.activateDeactivateDialog.description }}
              <strong>{{
                selectedCategory?.is_active ? "deactivate" : "activate"
              }}</strong>
              category <strong>{{ selectedCategory?.name }}</strong> {{ "?" }}
            </label>
          </v-card-text>
          <v-card-actions>
            <v-btn
              variant="text"
              @click="showDeleteDialog = false"
            >
              {{ strings.ticketCategories.deleteDialog.buttons.cancel }}
            </v-btn>
            <v-btn
              variant="tonal"
              :loading="isDeleting"
              @click="confirmDeleteCategory"
            >
              {{ strings.ticketCategories.deleteDialog.buttons.confirm }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Add/Edit Dialog -->
      <v-dialog
        v-if="showDialog"
        v-model="showDialog"
        persistent
        max-width="500px"
      >
        <v-card>
          <v-card-title class="dialog-title">
            <h5>
              {{
                isEditing
                  ? strings.ticketCategories.editDialogTitle
                  : strings.ticketCategories.addDialogTitle
              }}
            </h5>
          </v-card-title>
          <v-card-text>
            <v-form
              ref="formRef"
              v-model="isFormValid"
            >
              <v-text-field
                v-model="newCategory.categoryName"
                label="Category Name"
                required
                variant="outlined"
                class="mt-2"
                :readonly="isEditing"
              />
              <v-select
                v-if="isEditing"
                v-model="selectedSubCategories"
                :items="existingSubCategories"
                item-title="title"
                item-value="value"
                label="Existing Subcategories"
                variant="outlined"
                class="mt-2"
                multiple
                chips
                closable-chips
              />
              <v-text-field
                v-model="newSubCategory"
                :label="isEditing ? 'New Sub Category Name' : 'Sub Category Name'"
                required
                variant="outlined"
                class="mt-2"
              />
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-btn
              variant="text"
              @click="handleCloseDialog"
            >
              {{ strings.common.cancelButton }}
            </v-btn>
            <v-btn
              variant="tonal"
              :disabled="isEditing && !isFormModified"
              @click="isEditing ? handleUpdateCategory() : handleCreateCategory()"
            >
              {{ isEditing ? "Update" : "Save" }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- View Dialog -->
      <v-dialog
        v-model="showViewDialog"
        persistent
        max-width="500px"
      >
        <v-card>
          <v-card-title class="dialog-title">
            <h5>{{ strings.ticketCategories.viewDialogTitle }}</h5>
          </v-card-title>
          <v-card-text>
            <v-form>
              <v-row>
                <v-col>
                  <v-text-field
                    v-model="selectedViewCategory.categoryName"
                    label="Category Name"
                    variant="outlined"
                    readonly
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <v-select
                    v-model="selectedViewCategory.subcategories"
                    :items="existingSubCategories"
                    label="Existing Subcategories"
                    variant="outlined"
                    class="mt-1"
                    multiple
                    chips
                    readonly
                  />
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-btn
              variant="tonal"
              @click="showViewDialog = false"
            >
              {{ strings.common.closeButton }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </section>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  onMounted,
  onUnmounted,
  nextTick,
  computed,
} from "vue";
import { useToast } from "vue-toastification";
import {
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  activeCategory,
} from "../../api/apiClient";
import strings from "../../assets/strings.json";

interface Subcategory {
  id: number;
  subcat_name: string;
  category: number;
  category_name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by_fullname: string;
  updated_by_fullname: string;
}

interface Category {
  id: number;
  name: string;
  is_active: boolean;
  subcategoriesCount: number;
  subcategories: Subcategory[];
  created_by_fullname: string;
  updated_by_fullname: string;
}

export default defineComponent({
  name: "CategorySubCategory",
  setup() {
    const toast = useToast();
    const items = ref([
      { title: "Home", href: "/" },
      { title: "IT Support Config", href: "/ticketcategories" },
      { title: "Ticket Category", href: "" },
    ]);

    // Form & Dialog Refs
    const isFormValid = ref(false);
    const showDialog = ref(false);
    const isEditing = ref(false);
    const categories = ref<Category[]>([]);
    const selectedCategoryId = ref<any>(null);
    const formRef = ref(null);
    const newCategory = ref({ categoryName: "" });
    const existingSubCategories = ref([]);
    const selectedSubCategories = ref<any>([]);
    const newSubCategory = ref<any>("");
    const showViewDialog = ref(false);
    const showDeleteDialog = ref(false);
    const selectedCategory = ref<any>(null);
    const isDeleting = ref(false);
    const selectedViewCategory = ref({ categoryName: "", subcategories: [] });
    const user = JSON.parse(localStorage.getItem("user") || "{}");

    // Pagination & Loading
    const currentPage = ref(1);
    const totalPages = ref(1);
    const pageSize = 10;
    const totalCategory = ref(0);
    const loading = ref(false);
    const loadingMore = ref(false);
    const errorMessage = ref("");
    const hasMore = ref(true);
    let scrollTimeout: number | null = null;

    // Infinite Scroll Observer
    const observer = ref<IntersectionObserver | null>(null);
    const bottomObserver = ref<HTMLElement | null>(null);

    // Store original values when editing
    const originalCategoryName = ref("");
    const originalSubCategories = ref<number[]>([]);

    const isFormModified = computed(() => {
      const isNameChanged =
        newCategory.value.categoryName.trim() !== originalCategoryName.value;
      const isSubcategoriesChanged =
        JSON.stringify(selectedSubCategories.value) !==
        JSON.stringify(originalSubCategories.value);
      const isNewSubcategoryAdded = newSubCategory.value.trim() !== "";
      return isNameChanged || isSubcategoriesChanged || isNewSubcategoryAdded;
    });

    // Fetch Categories function with logging
    const fetchCategories = async (isInitialLoad = false) => {
      if (
        !isInitialLoad &&
        (loading.value || loadingMore.value || !hasMore.value)
      )
        return;

      if (isInitialLoad) {
        loading.value = true;
        categories.value = [];
        currentPage.value = 1;
        hasMore.value = true;
      } else {
        loadingMore.value = true;
      }

      setTimeout(async () => {
        try {
          const response = await getCategories(currentPage.value, pageSize);

          if (response && response.results && response.results.length > 0) {
            if (isInitialLoad) {
              categories.value = response.results;
            } else {
              categories.value = [...categories.value, ...response.results];
            }
            totalCategory.value = response.count;
            totalPages.value = response.total_pages;
            hasMore.value = currentPage.value < totalPages.value;
            if (hasMore.value) {
              currentPage.value += 1;
            }
          } else {
            hasMore.value = false;
          }
        } catch (error: any) {
          console.error("Error fetching categories:", error);
          toast.error("Failed to load categories. Please try again.");
        } finally {
          loading.value = false;
          loadingMore.value = false;
        }
      }, 1000);
    };

    const getFirstVisibleSubcategory = (category: Category) => {
      const visibleSubs = category.subcategories.filter((sub) => sub.is_active);
      return visibleSubs.length > 0 ? visibleSubs[0].subcat_name : "N/A";
    };

    // Add Category Dialog
    const handleAddCategory = () => {
      isEditing.value = false;
      newCategory.value = { categoryName: "" };
      existingSubCategories.value = [];
      selectedSubCategories.value = [];
      newSubCategory.value = "";
      showDialog.value = true;
    };

    // Edit Category Dialog
    const handleEditCategory = (category: any) => {
      isEditing.value = true;
      selectedCategoryId.value = category.id;
      newCategory.value.categoryName = category.name;
      originalCategoryName.value = category.name;

      // Create dropdown options from all subcategories, marking inactive ones.
      existingSubCategories.value = category.subcategories.map((sub: any) => ({
        value: sub.id,
        title: sub.is_active ? sub.subcat_name : `${sub.subcat_name}`,
      }));

      // Preselect only active subcategories.
      selectedSubCategories.value = category.subcategories
        .filter((sub: any) => sub.is_active)
        .map((sub: any) => sub.id);

      originalSubCategories.value = [...selectedSubCategories.value];

      newSubCategory.value = "";
      showDialog.value = true;
    };

    // View Category Dialog
    const handleViewCategory = (category: any) => {
      selectedViewCategory.value = {
        categoryName: category.name,
        subcategories: Array.isArray(category.subcategories)
          ? category.subcategories
              .filter((sub: any) => sub.is_active)
              .map((sub: any) => sub.subcat_name)
          : [],
      };
      showViewDialog.value = true;
    };

    // Delete Category Dialog
    const handleToggleCategoryStatus = (category: any) => {
      selectedCategory.value = category;
      selectedCategoryId.value = category.id;
      showDeleteDialog.value = true;
    };

    // Close Dialog
    const handleCloseDialog = () => {
      showDialog.value = false;
      isEditing.value = false;
      selectedCategoryId.value = null;
      newCategory.value = { categoryName: "" };
      existingSubCategories.value = [];
      selectedSubCategories.value = [];
      newSubCategory.value = "";
    };

    // Confirm Delete Category
    const confirmDeleteCategory = async () => {
      if (!selectedCategoryId.value || !selectedCategory.value) {
        toast.error("Invalid category selected!");
        return;
      }

      isDeleting.value = true;

      try {
        const categoryName = selectedCategory.value.name;

        if (selectedCategory.value.is_active) {
          // Deactivate
          await deleteCategory(selectedCategoryId.value);
          toast.success(
            `Category "${categoryName}" has been deactivated successfully.`
          );
        } else {
          // Activate
          await activeCategory(selectedCategoryId.value);
          toast.success(
            `Category "${categoryName}" has been activated successfully.`
          );
        }

        await fetchCategories(true);
        showDeleteDialog.value = false;
      } catch (error: any) {
        toast.error("Failed to update category status. Please try again.");
        console.error("Toggle status error:", error);
      } finally {
        isDeleting.value = false;
      }
    };

    // Create Category: After a successful API call, we reset the page and call fetchCategories(true)
    const handleCreateCategory = async () => {
      if (isFormValid.value) {
        try {
          loading.value = true;
          const categoryName = newCategory.value.categoryName.trim();
          const subCategoryName = newSubCategory.value.trim();
          if (!categoryName) {
            toast.warning("Category name is required!");
            return;
          }
          if (!subCategoryName) {
            toast.warning("Sub Category name is required for adding!");
            return;
          }
          const payload = {
            cat_name: categoryName,
            subcat_name: subCategoryName,
            created_by: user.id,
            updated_by: user.id,
          };
          const response = await createCategory(payload);
          if (response && response.message) {
            toast.success(response.message);
            showDialog.value = false;
            nextTick(() => {
              fetchCategories(true);
            });
          } else {
            toast.error(response.error);
          }
        } catch (error: any) {
          console.error("Error adding category:", error.response.data.error);
          toast.error(error.response.data.error);
        } finally {
          loading.value = false;
        }
      }
    };

    // Update Category
    const handleUpdateCategory = async () => {
      const categoryName = newCategory.value.categoryName.trim();
      const subCategoryName = newSubCategory.value.trim();
      if (!categoryName) {
        toast.warning("Category name is required!");
        return;
      }
      const payload = {
        cat_name: categoryName,
        existing_subcategories: selectedSubCategories.value,
        subcat_name: subCategoryName?.trim() || null,
        updated_by: user.id,
      };
      try {
        const response = await updateCategory(
          selectedCategoryId.value,
          payload
        );
        if (response && response.message) {
          toast.success(`${response.message}`);
          showDialog.value = false;
          currentPage.value = 1;
        } else {
          toast.error(response.error);
        }
        await fetchCategories(true);
      } catch (error: any) {
        toast.error("Failed to update category.");
        console.error("Error updating category:", error);
      }
    };

    // Infinite Scroll Observer setup
    const setupIntersectionObserver = () => {
      if (!bottomObserver.value) return;
      if (observer.value) observer.value.disconnect();
      observer.value = new IntersectionObserver(
        async (entries) => {
          const firstEntry = entries[0];
          if (
            firstEntry.isIntersecting &&
            hasMore.value &&
            !loadingMore.value
          ) {
            await fetchCategories(false);
          }
        },
        { rootMargin: "10px", threshold: 0.1 }
      );
      if (bottomObserver.value) {
        observer.value.observe(bottomObserver.value);
      }
    };

    const handleScroll = () => {
      if (!hasMore.value) return;
      if (scrollTimeout) return; // Prevent multiple calls

      scrollTimeout = window.setTimeout(() => {
        const scrollPosition = window.innerHeight + window.scrollY;
        const pageHeight = document.documentElement.scrollHeight;

        if (scrollPosition >= pageHeight - 300) {
          fetchCategories(false);
        }

        scrollTimeout = null; // Reset timeout
      }, 500); // 🔹 500ms debounce
    };

    const setupObserver = () => {
      if (!bottomObserver.value) return;

      const observer = new IntersectionObserver(
        async (entries) => {
          if (entries[0].isIntersecting && hasMore.value) {
            await fetchCategories(false);
          }
        },
        { rootMargin: "10px", threshold: 0.1 }
      );
      observer.observe(bottomObserver.value);
    };

    onMounted(async () => {
      await fetchCategories(true);
      nextTick(() => {
        setupObserver();
      });
      nextTick(() => {
        if (document.documentElement.scrollHeight <= window.innerHeight) {
          fetchCategories(false);
        }
      });
      window.addEventListener("scroll", handleScroll);
    });

    onUnmounted(() => {
      if (observer.value) observer.value.disconnect();
    });

    return {
      items,
      categories,
      totalCategory,
      loading,
      loadingMore,
      errorMessage,
      isFormValid,
      showDialog,
      isEditing,
      newCategory,
      selectedCategoryId,
      existingSubCategories,
      selectedSubCategories,
      newSubCategory,
      formRef,
      showViewDialog,
      selectedViewCategory,
      selectedCategory,
      showDeleteDialog,
      handleAddCategory,
      handleEditCategory,
      handleCloseDialog,
      handleCreateCategory,
      handleUpdateCategory,
      handleViewCategory,
      handleToggleCategoryStatus,
      confirmDeleteCategory,
      getFirstVisibleSubcategory,
      isDeleting,
      bottomObserver,
      isFormModified,
      strings,
    };
  },
});
</script>

<style scoped>
.user-active-badge {
  background-color: #fff;
  color: #2dcc70;
  border: 1px solid #2dcc70;
  border-radius: 30px;
  padding: 5px 10px;
  font-weight: 600;
}
.user-inactive-badge {
  background-color: #e8e8e8;
  color: #898888;
  border-radius: 30px;
  padding: 5px 10px;
  font-weight: 600;
}
.data-count {
  background-color: #026bb1;
  border-radius: 16px;
  color: #fff;
  padding: 4px 12px;
  font-size: 12px;
}
.breadcrumbs {
  padding: 0;
  font-size: 14px;
  color: #757575;
  margin-bottom: 1rem;
}
.add-user-btn {
  border: 1px solid #2dcc70;
  color: #2dcc70;
  text-transform: none;
}
.add-user-btn:hover {
  background-color: #2dcc70;
  color: #fff;
}
.category-card {
  margin: 5px;
  padding: 16px;
  width: 100%;
  border: 1px solid #d8d8d8;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
.category-card:hover {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.4);
}
.title {
  font-size: 12px;
  color: #555;
}
.value {
  font-size: 14px;
  font-weight: 500;
}
.sub-chip {
  font-size: 10px;
  background-color: #e0e0e0;
  border-radius: 50%;
  margin-left: 5px;
  cursor: pointer;
}
.delete-dialog-title {
  text-align: center;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.dialog-title-text {
  font-size: 30px;
  color: #333;
}
.dialog-content-text {
  font-size: 16px;
  text-align: center;
  color: #555;
  padding: 0;
}
.dialog-actions {
  display: flex;
  justify-content: space-evenly !important;
  gap: 6px;
  padding: 30px;
}
.dialog-confirmation-icon .mdi-check-circle-outline {
  color: #2dcc70;
}
.cancel-btn {
  background-color: #f1f1f1;
  color: #7d7d7d;
  border: none;
  text-transform: none;
}
.delete-dialog-btn {
  background-color: #ef4d56;
  color: #fff;
  text-transform: none;
}
.actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 5px;
}
.edit-btn {
  color: #026bb1;
  border: 1px solid #026bb1;
}
.edit-btn:hover {
  background-color: #026bb1;
  color: #fff;
}
.view-btn {
  color: #2dcc70;
  border: 1px solid #2dcc70;
}
.view-btn:hover {
  background-color: #2dcc70;
  color: white;
}
.delete-btn {
  color: red;
  border: 1px solid red;
}
.delete-btn:hover {
  background-color: red;
  color: #fff;
}
.tooltip-list {
  padding: 8px;
  border-radius: 4px;
}
.tooltip-list ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.tooltip-list li {
  padding: 4px 0;
  font-size: 14px;
  color: #fff;
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}
.error-message {
  text-align: center;
  color: red;
}
.no-data {
  text-align: center;
  margin-top: 20px;
}
.card-list-wrapper {
  padding-top: 15px;
}
.category-card {
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  margin-bottom: 10px;
  transition: box-shadow 0.3s;
}
.category-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.loading-more {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.observer-element {
  height: 10px;
  margin: 10px 0;
}
</style>
