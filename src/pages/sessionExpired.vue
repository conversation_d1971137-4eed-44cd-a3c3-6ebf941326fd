<template>
  <section class="page-misc page-not-found">
    <img
      src="../assets/images/page-session-expiry.svg"
      alt="401 Error - Session Expiry"
    >
    <div class="page-misc-content">
      <h1>{{ strings.sessionExpired.title }}</h1>
      <p class="p-0">
        {{ strings.sessionExpired.message }}
      </p>
      <div class="btn-toolbar">
        <v-btn
          variant="tonal"
          @click="logout"
        >
          {{ strings.sessionExpired.buttonText }}
        </v-btn>
      </div>
    </div>
  </section>
</template>
  
  <script setup lang="ts">
  import { useRouter } from "vue-router";
  import strings from "../assets/strings.json";

  
  const router = useRouter();
  
  const logout = async () => {
    localStorage.clear();
    await router.push("/login");
  };
  </script>
  
  <style scoped>
  /* You can add more styling here if needed */
  </style>
  