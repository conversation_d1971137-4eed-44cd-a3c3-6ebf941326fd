<template>
  <div class="page-wrapper page-change-password">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row
        align="center"
        justify="space-between"
      >
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">
              Change Password
            </h2>
          </div>
          <!-- <v-breadcrumbs :items="items" class="custom-breadcrumbs">
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs> -->
        </v-col>
      </v-row>
    </div>
    <section
      class="app-login-wrapper secondary-login-wrapper d-block"
      :class="{ 'centered-reset': fromInternalNavigation }"
    >
      <v-row
        align="center"
        justify="center"
      >
        <v-col
          sm="10"
          md="7"
          lg="auto"
        >
          <div class="right mt-5 text-left">
            <div
              v-if="linkExpired"
              class="expired-message"
            >
              <h2>Reset Link Expired</h2>
              <label class="mb-3">{{ expiredMessage }}</label>
            </div>
            <div v-else>
              <label class="mb-3 text-center d-block">{{ strings.resetPassword.displaymessage }}</label>

              <v-form
                ref="form"
                @submit.prevent="validateAndResetPassword"
              >
                <!-- Password Field -->
                <v-text-field
                  v-model="currentPassword"
                  placeholder="Current Password"
                  :type="currentPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="
                    currentPasswordVisible ? 'mdi-eye' : 'mdi-eye-off'
                  "
                  prepend-inner-icon="mdi-lock-outline"
                  density="compact"
                  variant="outlined"
                  class="mb-3"
                  @click:append-inner="
                    currentPasswordVisible = !currentPasswordVisible
                  "
                />

                  
                <v-text-field
                  v-model="password"
                  :placeholder="strings.resetPassword.passwordPlaceholder"
                  :error-messages="passwordErrors"
                  :type="passwordVisible ? 'text' : 'password'"
                  :append-inner-icon="passwordVisible ? 'mdi-eye' : 'mdi-eye-off'"
                  prepend-inner-icon="mdi-lock-outline"
                  density="compact"
                  variant="outlined"
                  class="mb-3"
                  @click:append-inner="passwordVisible = !passwordVisible"
                />

                <!-- Confirm Password Field -->
                <v-text-field
                  v-model="confirmPassword"
                  :placeholder="strings.resetPassword.confirmPasswordPlaceholder"
                  :error="confirmPasswordErrors.length > 0"
                  :error-messages="confirmPasswordErrors"
                  :type="confirmPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="
                    confirmPasswordVisible ? 'mdi-eye' : 'mdi-eye-off'
                  "
                  prepend-inner-icon="mdi-lock-outline"
                  density="compact"
                  variant="outlined"
                  class="mb-3"
                  @click:append-inner="
                    confirmPasswordVisible = !confirmPasswordVisible
                  "
                />

                <!-- Reset Password Button -->
                <div class="btn-toolbar">
                  <v-btn
                    variant="tonal"
                    block
                    :loading="loading"
                    :disabled="loading"
                    type="submit"
                  >
                    {{ strings.resetPassword.buttonMessage }}
                  </v-btn>
                </div>
              </v-form>
            </div>
          </div>
        </v-col>
      </v-row>
    </section>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import strings from "../../../src/assets/strings.json";
import { useToast } from "vue-toastification";
import type { VForm } from "vuetify/components";
import { ChangePassword, resetPassword } from "../../api/apiClient";

export default defineComponent({
  setup() {
    // Form data
    const password = ref<string>("");
    const confirmPassword = ref<string>("");
    const passwordVisible = ref<boolean>(false);
    const confirmPasswordVisible = ref<boolean>(false);
    const currentPasswordVisible = ref<boolean>(false);
    const currentPassword = ref<string>("")
    const loading = ref<boolean>(false);
    const token = ref<string>("");
    const form = ref<any>(null);
    const expiredMessage = ref("");
    const submitAttempted = ref(false);

    // Vue Router & Toast
    const router = useRouter();
    const route = useRoute();
    const toast = useToast();

    const linkExpired = ref<boolean>(false);

    // Password validation rules
    const passwordErrors = computed(() => {
      if (!submitAttempted.value) return [];

      const errors: string[] = [];

      if (!password.value) {
        errors.push("Password is required");
      } else {
        if (!/[A-Z]/.test(password.value)) {
          errors.push("Password must contain at least one uppercase letter");
        }
        if (!/[a-z]/.test(password.value)) {
          errors.push("Password must contain at least one lowercase letter");
        }
        if (!/\d/.test(password.value)) {
          errors.push("Password must contain at least one digit");
        }
        if (!/[^A-Za-z0-9]/.test(password.value)) {
          errors.push("Password must contain at least one special character");
        }
        if (password.value.length < 8) {
          errors.push("Password must be at least 8 characters");
        }
      }

      return errors;
    });

    const confirmPasswordErrors = computed(() => {
      if (!submitAttempted.value) return [];

      const errors: string[] = [];

      if (!confirmPassword.value) {
        errors.push("*Confirm password is required");
      } else {
        if (confirmPassword.value !== password.value) {
          errors.push("*Passwords do not match");
        }
      }

      return errors;
    });

    // Confirm password validation rule (checks if passwords match)
    // const confirmPasswordErrors = computed(() => {
    //   const errors: string[] = [];

    //   if (!confirmPassword.value) {
    //     errors.push("*Confirm password is required");
    //   } else {
    //     // Must match the 'password' length requirement, if that's your preference:
    //     if (confirmPassword.value.length < 8) {
    //       errors.push("*Password must be at least 8 characters");
    //     }
    //     if (confirmPassword.value !== password.value) {
    //       errors.push("*Passwords do not match");
    //     }
    //   }

    //   return errors;
    // });

  

 

    // Validate and Reset Password Function
    const validateAndResetPassword = async () => {
      submitAttempted.value = true;
      // If there are any errors in password or confirmPassword, do not proceed.
      if (passwordErrors.value.length || confirmPasswordErrors.value.length) {
        toast.error("Please fix the form errors before continuing.");
        return;
      }

      // Attempt to reset
      loading.value = true;
      try {
        const user = localStorage.getItem("authToken");
        const payload = {
          current_password: currentPassword.value,
          new_password: password.value,
          confirm_password: confirmPassword.value,
          new_user: 0,
        };

        // if (token.value) {
        //   payload.token = token.value;
        // } else if (user) {
        //   payload.user_id = user;
        // }

        const response = await ChangePassword(payload);

        if (response?.message) {
          toast.success(response.message);
          localStorage.clear();
          setTimeout(() => router.push("/login"), 2000);
        } else {
          toast.error(strings.resetPassword.errorMessage);
        }
      } catch (error: any) {
        console.error("Reset Password Error:", error);
        const errorMessage = error.response?.data?.error;
        if (errorMessage) {
          toast.error(errorMessage);
        }
      } finally {
        loading.value = false;
      }
    };

    return {
      password,
      confirmPassword,
      passwordVisible,
      confirmPasswordVisible,
      currentPassword,
      currentPasswordVisible,
      loading,
      validateAndResetPassword,
      strings,
      passwordErrors,
      confirmPasswordErrors,
      form, // Ensures form validation works
      linkExpired,
      expiredMessage,
      submitAttempted,
      fromInternalNavigation: computed(() => {
        return route.query.internal === "true";
      }), 
    };
  },
});
</script>

<style scoped>

.app-login-wrapper {
  display: flex;
  min-height: 100vh;
}

.left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.right {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.centered-reset {
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  display: flex;
  background-image:none
}

.centered-reset .right {
  max-width: 400px;
  width: 100%;
}

.centered-reset .left {
  display: none;
}

.centered-reset .logo {
  display: none;
}
</style>
