<template>
  <div class="app-layout">
    <section class="app-login-wrapper">
      <div class="left">
        <img
          src="../../assets/images/login-image.png"
          alt="image"
        >
      </div>
      <div class="right">
        <div class="logo">
          <img
            src="../../assets/icons/logo.svg"
            alt="image"
          >
        </div>
        <h2>{{ strings.forgotPassword.title }}</h2>
        <p>{{ strings.forgotPassword.displaymessage }}</p>
        <form @submit.prevent="handleSubmit">
          <v-text-field
            v-model="email"
            density="compact"
            :label="strings.forgotPassword.emailPlaceholder"
            :rules="[rules.required('Email'), rules.email]"
            prepend-inner-icon="mdi-email-outline"
            variant="outlined"
            class="mb-4"
            @focus="focused = true"
            @blur="onBlur"
          />

          <!-- Submit Button -->
          <!-- Show button or countdown -->
          <div class="btn-toolbar">
            <v-btn
              v-if="canResend"
              variant="tonal"
              block
              :loading="loading"
              @click.prevent="handleSubmit"
            >
              {{ strings.forgotPassword.submitButton }}
            </v-btn>
            <div
              v-else
              class="text-subtitle-2 text-grey"
            >
              Resend link in {{ countdown }}s
            </div>
          </div>
        </form>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed } from "vue";
import strings from "../../../src/assets/strings.json";
import { useToast } from "vue-toastification";
import { forgotpassword } from "../../api/apiClient";

export default defineComponent({
  name: "ForgotPassword",
  setup() {
    const email = ref<string>("");
    const focused = ref<boolean>(false);
    const loading = ref<boolean>(false);
    const submitted = ref<boolean>(false);
    const toast = useToast();

    const canResend = ref<boolean>(true);
    const countdown = ref<any>(15);
    let countdownTimer: any = null;

    const startResendTimer = () => {
      canResend.value = false;
      countdown.value = 15;

      countdownTimer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(countdownTimer);
          canResend.value = true;
        }
      }, 1000);
    };

    // Validation rules
    const rules = reactive({
      required: (fieldName: string) => (value: any) =>
        !!value || `${fieldName} is required`,
        email: (value: any) => {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.com$/;
        if (!value) return "Email is required";
        return emailRegex.test(value) || "E-mail must be valid";
      },
    });

    // Compute email error message when the field is empty
    const emailError = computed(() => {
      if (!email.value && focused.value) {
        return strings.forgotPassword.emailError || "Email is required.";
      }
      return "";
    });

    // Handle focus and blur to show error when necessary
    const onBlur = () => {
      focused.value = false;
    };

    // Handle form submission
    const handleSubmit = async (): Promise<void> => {
      // Ensure the email is valid and required
      if (
        !email.value ||
        !rules.email(email.value) ||
        !rules.required("Email")(email.value)
      ) {
        focused.value = true;
        return;
      }

      loading.value = true;

      try {
        const { data } = await forgotpassword({ email: email.value });

        if (data?.message) {
          toast.success(data.message);
          submitted.value = true;
          startResendTimer(); // Start the 15s countdown
        } else {
          toast.error(data?.detail || "Something went wrong.");
        }
      } catch (error: any) {
        toast.error(
          error.response?.data?.detail ||
            strings.forgotPassword.genericErrorMessage
        );
      } finally {
        loading.value = false;
      }
    };

    return {
      email,
      focused,
      loading,
      submitted,
      handleSubmit,
      strings,
      rules,
      onBlur,
      emailError,
      canResend,
      countdown,
      startResendTimer,
    };
  },
});
</script>

<style scoped></style>
