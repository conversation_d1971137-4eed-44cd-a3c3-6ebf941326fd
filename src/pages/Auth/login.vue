<template>
  <div class="app-layout">
    <section class="app-login-wrapper">
      <div class="left">
        <img src="../../assets/images/login-image.png" alt="image" />
      </div>
      <div class="right">
        <div class="logo">
          <img src="../../assets/icons/logo.svg" alt="image" />
        </div>
        <h2>{{ strings.login.welcomemessasge }}</h2>
        <p>{{ strings.login.displaymessage }}</p>

        <!-- Form -->
        <v-form ref="loginForm" @submit.prevent="validateAndLogin">
          <!-- Email Field -->
          <v-text-field
            v-model="email"
            :rules="[rules.required('Email'), rules.email]"
            :label="strings.login.emailPlaceholder"
            density="compact"
            prepend-inner-icon="mdi-email-outline"
            variant="outlined"
            class="mb-3"
          >
            <template #append-inner>
              <v-icon v-if="emailError" color="red"> mdi-alert-circle </v-icon>
            </template>
          </v-text-field>

          <!-- Password Field -->
          <v-text-field
            v-model="password"
            :rules="[rules.required('Password'), rules.password]"
            :append-inner-icon="visible ? 'mdi-eye' : 'mdi-eye-off'"
            :type="visible ? 'text' : 'password'"
            :label="strings.login.passwordPlaceholder"
            density="compact"
            prepend-inner-icon="mdi-lock-outline"
            variant="outlined"
            @click:append-inner="visible = !visible"
          >
            <template #append-inner>
              <v-icon v-if="passwordError" color="red">
                mdi-alert-circle
              </v-icon>
            </template>
          </v-text-field>

          <!-- Forgot Password Link -->
          <div class="login-link pt-2 pb-3">
            <a
              href="#"
              rel="noopener noreferrer"
              @click.prevent="forgotPassword"
            >
              {{ strings.login.forgotPassword }}
            </a>
          </div>

          <!-- Login Button -->
          <div class="btn-toolbar">
            <v-btn
              variant="tonal"
              block
              type="submit"
              :loading="loading"
              prepend-icon="mdi-login"
              :disabled="loading"
            >
              {{ strings.login.loginButton }}
            </v-btn>
          </div>
        </v-form>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  reactive,
  computed,
  onMounted,
  watch,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import strings from "../../../src/assets/strings.json";
import { useToast } from "vue-toastification";
import { initializeApp } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
import { useUserStore } from "../../stores/userStore"; // adjust path accordingly

import axios from "axios";
import apiClient, { validateResetToken } from "../../api/apiClient";
import { API_ENDPOINTS } from "../../api/apiconfig";
import { retrieveFcmToken, vapidKey } from "../../plugins/firebase";
//import { retrieveFcmToken, vapidKey } from "../../plugins/firebase";
export default defineComponent({
  name: "Login",
  setup() {
    const email = ref<string>("");
    const password = ref<string>("");
    const visible = ref<boolean>(false);
    const loading = ref<boolean>(false);
    const loginForm = ref<any>(null);
    const router = useRouter();
    const toast = useToast();
    const userStore = useUserStore();
    const route = useRoute();
    const token = ref<string>("");
    const userId = ref<string>("");
    const linkExpired = ref(false);
    const expiredMessage = ref("");
    const expireTime = ref("");

    // const params = route.params as { user_id?: string; token?: string };
    // token.value = params.token ?? "";
    console.log("expireTime:", expireTime.value);

    const rules = reactive({
      required: (fieldName: string) => (value: any) =>
        !!value || `${fieldName} is required`,
      email: (value: any) => {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.com$/;
        if (!value) return "Email is required";
        return emailRegex.test(value) || "E-mail must be valid";
      },
      password: (value: any) =>
        !!value
          ? value.length >= 6 || "Password must be at least 6 characters"
          : "Password is required",
    });

    // staging configuration

    // const firebaseConfig = {
    //   apiKey: "AIzaSyCMWc3XxRqiv1VVv71TrWg1ByjcfTAwP2c",
    //   authDomain: "nex-ticketting.firebaseapp.com",
    //   projectId: "nex-ticketting",
    //   storageBucket: "nex-ticketting.firebasestorage.app",
    //   messagingSenderId: "584583859864",
    //   appId: "1:584583859864:web:078f14e255049f66314aed",
    //   measurementId: "G-CVR3SWLH7X",
    // };
    // // // Initialize Firebase
    // const app = initializeApp(firebaseConfig);
    // const messaging = getMessaging(app);
    // const vapidKey =
    //   "BM-jPYgSGgdTqhErmnyVYYTQTS41PsPM_HuwsolB33WRCoLvTCEsih021EfuQDNSqdblDO9njZfSD45_8NGwlWc";
    // // // Initialize Firebase Cloud Messaging
    // console.log("messaging:", messaging);

    // async function retrieveFcmToken(vapidKey: string) {
    //   try {
    //     const currentToken = await getToken(messaging, { vapidKey });
    //     if (currentToken) {
    //       console.log("FCM Token retrieved:", currentToken);
    //       return currentToken;
    //     } else {
    //       console.error("No FCM Token available. Request user permission.");
    //       return null;
    //     }
    //   } catch (error: any) {
    //     console.error("Error retrieving FCM Token:", error);
    //     return null;
    //   }
    // }

    // Computed properties for error handling
    // const vapidKey = process.env.VITE_FIREBASE_VAPID_KEY;

    const emailError = computed(
      () => !email.value && loginForm.value?.errors?.email
    );
    const passwordError = computed(
      () => !password.value && loginForm.value?.errors?.password
    );

    const validateAndLogin = async () => {
      if (!loginForm.value) return;

      const { valid } = await loginForm.value.validate();
      if (!valid) return;

      loading.value = true;

      try {
        loading.value = true;

        const fcmToken = await retrieveFcmToken(vapidKey);
        console.log("fcmToken:", fcmToken);

        const payload = {
          email: email.value,
          password: password.value,
          fcm_token: fcmToken,
        };

        const response = await apiClient.post(API_ENDPOINTS.LOGIN, payload);

        if (response.status === 200) {
          const data = response.data;
          toast.success(data.message);
          console.log("data", data);

          // Store tokens and user info
          localStorage.setItem("authToken", data.access);
          localStorage.setItem("refreshToken", data.refresh);

          if (data.user) {
            localStorage.setItem("user", JSON.stringify(data.user));
            userStore.setUser(data.user);
          }

          if (data.user.new_user === true) {
            const token_url = data.access;
            console.log("token_url", token_url);

            const url = new URL(data.reset_url); // Break down the full URL

            await router.push({
              path: url.pathname, // This gives "/resetpassword/1/token/"
              query: {
                // ...Object.fromEntries(url.searchParams), // exp=xxxx
                new_user: "true", // add your own query param too
              },
            });
          } else {
            await router.push("/");
          }

          // Redirect to dashboard
        }
      } catch (error: any) {
        console.error("Login error:", error);

        // Extract error message from response
        const errorMessage = error.response?.data?.detail;

        if (errorMessage) {
          toast.error(errorMessage);
        }
      } finally {
        loading.value = false;
      }
    };

    // Logout Function
    const logout = async () => {
      try {
        await apiClient.post("/api/token/logout/", {
          refresh: localStorage.getItem("refreshToken"),
        });
      } catch (error: any) {
        console.error("Logout failed:", error);
      } finally {
        localStorage.removeItem("authToken");
        localStorage.removeItem("refreshToken");
        localStorage.removeItem("user");
        router.push("/login");
      }
    };

    const forgotPassword = () => {
      router.push("/forgotpassword");
    };

    return {
      email,
      password,
      visible,
      loading,
      loginForm,
      validateAndLogin,
      rules,
      strings,
      forgotPassword,
      emailError,
      passwordError,
    };
  },
});
</script>

<style scoped>
/* Add any necessary styles here */
</style>
