import { API_BASE_URL, API_ENDPOINTS } from "./apiconfig";
import type { Category, SubCategory } from "../stores/data";
import router from "@/router";
import axios, { AxiosError } from "axios";
import type { AxiosRequestConfig, InternalAxiosRequestConfig } from "axios";
import { useToast } from "vue-toastification";

interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean; // Extend Axios request config with _retry flag
}

const toast = useToast();

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
    // Authorization: `Bearer ${localStorage.getItem("authToken")}`,
  },
});
// // for Form data
export const apiClientFormData = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "multipart/form-data",
  },
});

// Function to refresh access token
const refreshAccessToken = async () => {
  try {
    const refreshToken = localStorage.getItem("refreshToken");
    if (!refreshToken) throw new Error("No refresh token available");

    const { data } = await axios.post(`${API_BASE_URL}token/refresh/`, {
      refresh: refreshToken,
    });

    localStorage.setItem("authToken", data.access);
    localStorage.setItem("refreshToken", data.refresh);
    console.log("Token refreshed:", data.access);
    return data.access;
  } catch (error: any) {
    console.error("Failed to refresh token:", error);
    logoutAndRedirect();
    throw error;
  }
};

// Function to log out and redirect
const logoutAndRedirect = () => {
  localStorage.removeItem("authToken");
  localStorage.removeItem("refreshToken");
  localStorage.removeItem("user");
  router.push("/login");
};

const attachAuthHeader = (config: any) => {
  const token = localStorage.getItem("authToken");
  if (token) {
    config.headers["Authorization"] = `Bearer ${token}`;
  }
  return config;
};

// // Add to both clients
apiClient.interceptors.request.use(attachAuthHeader, (error) =>
  Promise.reject(error)
);
apiClientFormData.interceptors.request.use(attachAuthHeader, (error) =>
  Promise.reject(error)
);

apiClient.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    if (!originalRequest || originalRequest._retry) {
      return Promise.reject(error);
    }

    if (error.response?.status === 401) {
      originalRequest._retry = true;
      try {
        const newToken = await refreshAccessToken();
        originalRequest.headers["Authorization"] = `Bearer ${newToken}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        return Promise.reject(refreshError);
      }
    }

    // Handle specific "Invalid Token" structure
    // if (
    //   error.response?.data &&
    //   typeof error.response.data === "object" &&
    //   "error" in error.response.data &&
    //   (error.response.data as { error?: string }).error === "Invalid Token"
    // ) {
    //   logoutAndRedirect();
    // }
    // Only logout if refresh fails
    if (
      error.response?.data &&
      typeof error.response.data === "object" &&
      "code" in error.response.data &&
      (error.response.data as { code?: string }).code === "token_not_valid" &&
      originalRequest._retry
    ) {
      logoutAndRedirect(); // Refresh also failed
    }

    return Promise.reject(error);
  }
);
// OLD
// Axios Interceptors
// apiClient.interceptors.request.use(
//   async (config) => {
//     const token = localStorage.getItem("authToken");

//     if (token) {
//       config.headers["Authorization"] = `Bearer ${token}`;
//     }

//     return config;
//   },
//   (error) => Promise.reject(error)
// );

// apiClient.interceptors.response.use(
//   (response) => response,
//   async (error: AxiosError) => {
//     const originalRequest = error.config as CustomAxiosRequestConfig;

//     if (!originalRequest) {
//       return Promise.reject(error);
//     }
//     if (!originalRequest) {
//       return Promise.reject(error);
//     }

//     if (error.response?.status === 401 && !originalRequest._retry) {
//       originalRequest._retry = true;
//       try {
//         const newToken = await refreshAccessToken();
//         originalRequest.headers["Authorization"] = `Bearer ${newToken}`;
//         return apiClient(originalRequest);
//       } catch (refreshError) {
//         return Promise.reject(refreshError);
//       }
//     }
//     if (error.response?.status === 401 && !originalRequest._retry) {
//       originalRequest._retry = true;
//       try {
//         const newToken = await refreshAccessToken();
//         originalRequest.headers["Authorization"] = `Bearer ${newToken}`;
//         return apiClient(originalRequest);
//       } catch (refreshError) {
//         return Promise.reject(refreshError);
//       }
//     }

//     return Promise.reject(error);
//   }
// );

// New
// Request interceptor for both clients

// apiClient.interceptors.request.use(
//   (config: CustomAxiosRequestConfig) => {
//     const token = localStorage.getItem("authToken");
//     if (token) {
//       config.headers["Authorization"] = `Bearer ${token}`;
//     }
//     return config;
//   },
//   (error) => Promise.reject(error)
// );

// apiClientFormData.interceptors.request.use(
//   (config: CustomAxiosRequestConfig) => {
//     const token = localStorage.getItem("authToken");
//     if (token) {
//       config.headers["Authorization"] = `Bearer ${token}`;
//     }
//     return config;
//   },
//   (error) => Promise.reject(error)
// );

// // // Response interceptor for the main apiClient
// apiClient.interceptors.response.use(
//   (response) => response,
//   (error: any) => {
//     // Check if we received an "Invalid Token" error from the backend.
//     if (error.response && error.response.data) {
//       const responseData = error.response.data;

//       // Assuming your API sends a JSON response with an 'error' field
//       if (
//         typeof responseData === "object" &&
//         responseData.error &&
//         responseData.error === "Invalid Token"
//       ) {
//         // Clear tokens and user info
//         localStorage.removeItem("authToken");
//         localStorage.removeItem("refreshToken");
//         localStorage.removeItem("user");
//         // Redirect to login page
//         router.push("/session-expired"); // Redirect to session expired page
//       }
//     }
//     return Promise.reject(error);
//   }
// );

export default apiClient;

// ... other exported functions (fetchTickets, getStatus, etc.)

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Function to fetch tickets with pagination
export const fetchTickets = async (
  page: number = 1
): Promise<PaginatedResponse<Ticket>> => {
  try {
    const response = await axios.get<PaginatedResponse<Ticket>>(
      `${API_BASE_URL}/tickets-all/?page=${page}`
    );
    return response.data;
  } catch (error: any) {
    console.error("Error fetching tickets:", error);
    throw error;
  }
};
//Get Status

export const getStatus = async () => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.GET_TICKET_STATUS);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};
//Get Notification
export const getNotification = async () => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.GET_NOTIFICATION);
    console.log(response);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Get Notification By Id
export const getNotificationById = async (payload: { id: number }) => {
  try {
    const api = `${API_ENDPOINTS.GET_NOTIFICATION_BYID}${payload.id}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

// Mark notifications as read
export const updateNotificationStatus = async (payload: { id: number }) => {
  try {
    const api = `${API_ENDPOINTS.GET_NOTIFICATION_BYID}${payload.id}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

// Clear notifications
export const clearUserNotifications = async (payload: { id: number }) => {
  try {
    const api = `${API_ENDPOINTS.GET_CLEAR_NOTIFICATION_BY_ID}${payload.id}`;
    const response = await apiClient.post(api); // Use POST instead of GET
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export const updateUserNotifications = async (payload: {
  notifications: number[];
}) => {
  try {
    const api = `${API_ENDPOINTS.UPDATE_NOTIFICATION}`;
    const response = await apiClient.post(api, payload);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

// get Status tracking
export const getStatusTracking = async (payload: { id: number }) => {
  try {
    const api = `${API_ENDPOINTS.GET_TICKET_STATUSTRACKING}${payload.id}`;

    const response = await apiClient.get(api);
    console.log(response);

    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};
//approval status
export const getApprovalStatus = async () => {
  try {
    const response = await apiClient.get(
      API_ENDPOINTS.GET_TICKET_APPROVAL_STATUS
    );
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export const getTicketApproveMail = async (payload: { id: number }) => {
  try {
    const api = `${API_ENDPOINTS.GET_TICKET_APPROVE_MAIL}${payload.id}`;
    const response = await apiClient.get(api);
    console.log(response.data);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export const getTickets = async (params?: {
  start_date?: string;
  end_date?: string;
}) => {
  try {
    const response = await apiClient.get<Ticket[]>(
      API_ENDPOINTS.GET_ALL_TICKETS,
      {
        params,
      }
    );
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export interface Ticket {
  ticket_id: number;
  attachement: any[]; // Adjust type if needed
  assigned_to_fullname: string | null;
  created_by_fullname: string;
  priority_names: string;
  category_name: string | null;
  subcategory_name: string | null;
  status_name: string;
  project_name: string | null;
  status: number;
  location_name: string;
  approved_by_name: string | null;
  cancelled_by_name: string | null;
  created_by: CreatedBy;
  title: string;
  type: string | null;
  description: string;
  watchers: number[];
  due_date: string | null;
  due_expiry_reason: string | null;
  created_at: string;
  updated_at: string;
  solved_at: string | null;
  closed_at: string | null;
  approvel_message: string | null;
  cancel_message: string | null;
  is_approved: boolean;
  justification: string;
  auto_closed: boolean;
  is_deleted: boolean;
  deleted_at: string | null;
  project: number;
  category: number | null;
  subcategory: number | null;
  priority: number;
  location: string;
  updated_by: number | null;
  assigned_to: number | null;
  approvel_status: string | null;
  approved_by: number | null;
  cancelled_by: number | null;
  deleted_by: number | null;
}

export interface CreatedBy {
  id: number;
  role: string;
  location: string;
  profile_pic: string | null;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  fcm_token: string;
  new_user: boolean;
  last_login: string | null;
  date_joined: string;
  is_staff: boolean;
  is_superuser: boolean;
  last_used_reset_token: string | null;
  created_by: number | null;
  updated_by: number;
  groups: any[]; // Adjust if you have a group type
  user_permissions: any[]; // Adjust if you have a permission type
}

interface SeriesItem {
  name: string;
  data: number[];
}

interface SeriesResult {
  series: SeriesItem[];
  categories: string[];
}

export function getTicketSeriesByHour(
  tickets: Ticket[],
  categoryFilter: string = "",
  projectFilter: string = ""
): SeriesResult {
  const grouped: Record<
    string,
    { Open: number; Solved: number; Closed: number }
  > = {};

  // Step 1: Filter tickets
  const filtered = tickets.filter(
    (ticket) =>
      (!categoryFilter || ticket.category_name === categoryFilter) &&
      (!projectFilter || ticket.project_name === projectFilter)
  );

  // Step 2: Group by hour
  filtered.forEach((ticket) => {
    const date = new Date(ticket.created_at);
    const hour = date.toISOString().slice(0, 13); // "YYYY-MM-DDTHH"

    if (!grouped[hour]) {
      grouped[hour] = { Open: 0, Solved: 0, Closed: 0 };
    }

    const status = ticket.status_name;
    if (status === "New" || status === "Open") grouped[hour].Open += 1;
    else if (status === "Solved") grouped[hour].Solved += 1;
    else if (status === "Closed") grouped[hour].Closed += 1;
  });

  // Step 3: Format series
  const categories: string[] = Object.keys(grouped).sort();
  const open: number[] = [];
  const solved: number[] = [];
  const closed: number[] = [];

  categories.forEach((hour) => {
    const { Open, Solved, Closed } = grouped[hour];
    open.push(Open);
    solved.push(Solved);
    closed.push(Closed);
  });

  const series: SeriesItem[] = [
    { name: "Open Tickets", data: open },
    { name: "Solved Tickets", data: solved },
    { name: "Closed Tickets", data: closed },
  ];

  return { series, categories };
}

//Get Tickets
export const getTicketById = async (payload: { id: number }) => {
  try {
    const api = `${API_ENDPOINTS.GET_TICKET_BY_ID}${payload.id}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};
// prioritys
export const getPriorityLevel = async () => {
  try {
    const api = `${API_ENDPOINTS.GET_PRIORITY}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export const getTicketByChat = async (
  ticket_id: number,
  data: {
    sender_id: number;
    message: string;
    attachement: { file_data: string; file_name: string }[];
    message_id?: number;
    is_edit?: boolean;
    timestamp?: string;
  }
) => {
  try {
    const formData = new FormData();
    formData.append("ticket_id", ticket_id.toString());
    formData.append("sender_id", data.sender_id.toString());
    formData.append("message", data.message || "");
    formData.append("message_id", data.message_id?.toString() || "");
    formData.append("is_edit", data.is_edit?.toString() || "false");
    formData.append("timestamp", data.timestamp || new Date().toISOString());

    for (const item of data.attachement) {
      const res = await fetch(item.file_data);
      const blob = await res.blob();
      const file = new File([blob], item.file_name);
      formData.append("attachement", file); // <-- note plural "files"
    }

    const api = API_ENDPOINTS.GET_TICKET_BY_CHAT_ID;
    const response = await apiClientFormData.post(api, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });

    console.log("API Response:", response.data);
    return response;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export const getTicketByChatMessage = async (payload: {
  ticket_id: number;
}) => {
  try {
    if (!payload || !payload.ticket_id) {
      throw new Error("Invalid payload: ticket_id is missing");
    }

    const api = `${API_ENDPOINTS}ticket/${payload.ticket_id}/messages/`; // Correct URL
    const response = await apiClient.get(api);

    console.log("API Response:", response.data);

    return response.data;
  } catch (error: any) {
    console.error("API Error:", error);
    throw error;
  }
};

//Create Roles
export const createRole = async (payload: {
  role_name: string;
  created_by: number;
  updated_by: number;
}) => {
  try {
    const response = await apiClient.post(API_ENDPOINTS.CREATE_USER_ROLE, {
      role_name: payload.role_name,
      created_by: payload.created_by,
      updated_by: payload.updated_by,
    });
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

// Get roles
export const getUserRoles = async () => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.GET_USER_ROLES);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

// Get locations
export const getLocations = async () => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.GET_LOCATIONS);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

// Add User
export const addUser = async (data: any) => {
  try {
    // Check if the data is an instance of FormData
    const headers =
      data instanceof FormData
        ? { "Content-Type": "multipart/form-data" }
        : { "Content-Type": "application/json" };

    const response = await apiClient.post(API_ENDPOINTS.POST_NEW_USER, data, {
      headers,
    });
    return response.data;
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error("Error:", error.message);
    }

    if ((error as any)?.response?.data) {
      console.error("API Error:", (error as any).response.data);
    }

    throw error; // Rethrow the error for the caller to handle
  }
};

//Get all Users
export const getAllUsers = async () => {
  try {
    const response = await apiClient.get(`${API_ENDPOINTS.GET_ALL_USERS}`);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    if (error.response?.status === 404) {
      // Redirect to custom 404 page
      router.push("/404");
    } else if (error.response?.status === 401) {
      // Optional: handle session expiration
      router.push("/session-expired");
    }
    throw error;
  }
};

//Get all Users Page
export const getAllUsersPage = async (page: number, page_size: number) => {
  try {
    const response = await apiClient.get(
      `${API_ENDPOINTS.GET_USERS_PAGE}?page=${page}&page_size=${page_size}`
    );
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Get User By Id
export const getUserById = async (payload: { id: number }) => {
  try {
    const api = `${API_ENDPOINTS.GET_USER_BY_ID}${payload.id}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    if (error.response?.status === 404) {
      // Redirect to custom 404 page
      router.push("/404");
    } else if (error.response?.status === 401) {
      // Optional: handle session expiration
      router.push("/session-expired");
    } else {
      toast.error("Error loading Get users.");
    }
    throw error;
  }
};

//Update User by Id
export const updateUserById = async (payload: { id: number; data: any }) => {
  try {
    const headers =
      payload.data instanceof FormData
        ? { "Content-Type": "multipart/form-data" }
        : { "Content-Type": "application/json" };

    const api = `${API_ENDPOINTS.UPDATE_USER_BY_ID}${payload.id}`;
    const response = await apiClient.put(api, payload.data, {
      headers,
    });
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    if (error.response?.status === 404) {
      // Redirect to custom 404 page
      router.push("/404");
    } else if (error.response?.status === 401) {
      // Optional: handle session expiration
      router.push("/session-expired");
    } else {
      toast.error("Error update user Details.");
    }
    throw error;
  }
};

// Block User By Id
export const blockUserById = async (payload: {
  id: number;
  action: string;
  updated_by: number;
}) => {
  try {
    const api = `${API_ENDPOINTS.BLOCK_USER}${payload.id}`;
    const response = await apiClient.patch(api, {
      action: payload.action,
      updated_by: payload.updated_by, // Include updated_by in the request payload
    });
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Remove User By Id
export const removeUserById = async (payload: {
  id: number;
  updated_by: number;
}) => {
  try {
    const api = `${API_ENDPOINTS.REMOVE_USER}${payload.id}`;
    const response = await apiClient.patch(api, {
      updated_by: payload.updated_by,
    });
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Search and filter
export const searchAndFilter = async (payload: {
  search?: string;
  status?: string;
  role?: string;
  location?: string;
  page?: number;
  page_size?: number;
}) => {
  try {
    // Construct query parameters dynamically
    const queryParams = new URLSearchParams({
      ...(payload.search && { search: payload.search }),
      ...(payload.status && { status: payload.status }),
      ...(payload.role && { role: payload.role }),
      ...(payload.location && { location: payload.location }),
      page: (payload.page || 1).toString(),
      page_size: (payload.page_size || 10).toString(),
    }).toString();

    // API request with pagination & filters
    const api = `${API_ENDPOINTS.SEARCH_AND_FILTER_USER}?${queryParams}`;
    const response = await apiClient.get(api);

    return response.data;
  } catch (error: any) {
    if (error instanceof AxiosError) {
      console.error("API Error:", error.response?.data || error.message);
    } else {
      console.error("Unexpected Error:", error);
    }
    if (error.response?.status === 404) {
      // Redirect to custom 404 page
      router.push("/404");
    } else if (error.response?.status === 401) {
      // Optional: handle session expiration
      router.push("/session-expired");
    } else {
      toast.error("Error loading Users.");
    }
    throw error;
  }
};

//Create Project
export const createProject = async (data: any) => {
  try {
    const api = `${API_ENDPOINTS.CREATE_PROJECT}`;
    const response = await apiClient.post(api, data);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Get All Project
export const getAllProject = async (page: number, page_size: number) => {
  try {
    const api = `${API_ENDPOINTS.GET_ALL_PROJECT}?page=${page}&page_size=${page_size}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    if (error.response?.status === 404) {
      // Redirect to custom 404 page
      router.push("/404");
    } else if (error.response?.status === 401) {
      // Optional: handle session expiration
      router.push("/session-expired");
    } else {
      toast.error("Error loading Projects.");
    }
    throw error;
  }
};

//Get project By Id
export const getProjectById = async (payload: { id: number }) => {
  try {
    const api = `${API_ENDPOINTS.GET_PROJECT_ID}${payload.id}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    if (error.response?.status === 404) {
      // Redirect to custom 404 page
      router.push("/404");
    } else if (error.response?.status === 401) {
      // Optional: handle session expiration
      router.push("/session-expired");
    } else {
      toast.error("Error loading Projects.");
    }
    throw error;
  }
};

//Get Project By User Id
export const getProjectByUserId = async (
  id: number,
  page: number,
  page_size: number
) => {
  try {
    const api = `${API_ENDPOINTS.GET_PROJECT_USER}${id}?page=${page}&page_size=${page_size}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Update Project Data
export const updateProjectById = async (
  projectId: string,
  payload: { project_manager: any; members: any[] }
) => {
  try {
    const api = `${API_ENDPOINTS.UPDATE_PROJECT_BY_ID}/${projectId}`;
    const response = await apiClient.put(api, payload);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Delete Project By Id
export const deleteProjectById = async (payload: {
  project_id: number;
  project_deleted_by: number;
}) => {
  try {
    const api = `${API_ENDPOINTS.DELETE_PROJECT_BY_ID}`;
    const response = await apiClient.patch(api, payload);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Location Report Data
export const getLocationReportData = async () => {
  try {
    const api = `${API_ENDPOINTS.LOCATION_REPORT}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Category Report Data
export const getCategoryReportData = async (payload: {
  start_date: string;
  end_date: string;
  role: string;
  user_id: number;
}) => {
  try {
    const api = `${API_ENDPOINTS.CATEGORY_REPORT}`;
    const response = await apiClient.get(api, {
      params: {
        start_date: payload.start_date,
        end_date: payload.end_date,
        role: payload.role,
        user_id: payload.user_id,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    if (error.response?.status === 404) {
      // Redirect to custom 404 page
      router.push("/404");
    } else if (error.response?.status === 401) {
      // Optional: handle session expiration
      router.push("/session-expired");
    } else {
      toast.error("Error loading Category.");
    }
    throw error;
  }
};

//Yearly report
export const getYearlyReportData = async (payload: {
  start_date: string;
  end_date: string;
  role: string;
  user_id: number;
}) => {
  try {
    // Remove the /{year} part from the endpoint if you're no longer using it
    const api = `${API_ENDPOINTS.YEARLY_REPORT}`;
    const response = await apiClient.get(api, {
      params: {
        start_date: payload.start_date,
        end_date: payload.end_date,
        role: payload.role,
        user_id: payload.user_id,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Employee Report
export const getEmployeeReportData = async (
  start_date: string,
  end_date: string,
  selectedOption: string
) => {
  try {
    const api = `${API_ENDPOINTS.EMPLOYEE_TICKET_REPORT}`;
    const response = await apiClient.get(api, {
      params: { start_date, end_date, status: selectedOption },
    });
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Export Employee Report
export const exportEmployeeReportData = async (
  start_date: string,
  end_date: string,
  selectedOption: string
) => {
  try {
    const api = `${API_ENDPOINTS.EXPORT_EMPLOYEE_TICKET_REPORT}`;
    const response = await apiClient.get(api, {
      params: { start_date, end_date, status: selectedOption },
    });
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Import File
export const importFile = async (formData: FormData) => {
  try {
    // Make the API request to upload the file
    const response = await apiClient.post(API_ENDPOINTS.IMPORT_FILE, formData, {
      headers: {
        "Content-Type": "multipart/form-data", // Set the content type for file upload
      },
    });
    return response.data; // Return the response data from the server
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error; // Rethrow the error to handle it in the calling function
  }
};

//Ticket Count
export const getTicketCount = async (body: {
  role: string;
  user_id: number;
  type: "my" | "team";
}) => {
  try {
    const api = `${API_ENDPOINTS.TICKETS_COUNT}`;
    const response = await apiClient.get(api, { params: body });
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//GET User Module Access
export const getUserModuleAccess = async (role_id: string) => {
  try {
    const api = `${API_ENDPOINTS.GET_USER_MODULE_ACCESS}${role_id}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Update User Module Access
export const updateUserModuleAccess = async (
  role_id: string,
  module_access: {
    modulesWithAccess: number[];
    subModulesWithAccess: number[];
    updatedBy: number;
  }
) => {
  try {
    const api = `${API_ENDPOINTS.UPDATE_USER_MODULE_ACCESS}${role_id}`;
    const response = await apiClient.put(api, module_access);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

//Technician category Report
export const getTechnicianCategoryReport = async () => {
  try {
    const api = `${API_ENDPOINTS.TECHNICIAN_CATEGORY_REPORT}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};
export const getCategorySubcategoryHoursReport = async () => {
  try {
    const api = `${API_ENDPOINTS.CATEGORY_SUBCATEGORY_HOURS_REPORT}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error:", error);
    throw error;
  }
};

//Technician Feedback
export const getTechnicianFeedback = async () => {
  try {
    const api = `${API_ENDPOINTS.TECHNICIAN_FEEDBACK}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

// Categories Feedback
export const getCategoriesFeedback = async () => {
  try {
    const api = `${API_ENDPOINTS.CATEGORIES_FEEDBACK}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export const createCategory = async (data: {
  cat_name: string;
  subcat_name: string;
  created_by: number;
  updated_by: number;
}) => {
  try {
    const api = `${API_ENDPOINTS.TICKET_CATEGORIES}`;
    const response = await apiClient.post(api, data);
    return response.data;
  } catch (error: any) {
    console.error("Error creating category:", error);
    throw error;
  }
};

export const getCategories = async (page: number, pageSize: number) => {
  try {
    const response = await apiClient.get(
      `${API_ENDPOINTS.GET_CATEGORIES}?page=${page}&page_size=${pageSize}`
    );
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export const getAllCategories = async () => {
  try {
    const response = await apiClient.get(`${API_ENDPOINTS.GET_ALLCATEGORIES}`);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export const updateCategory = async (
  id: number,
  data: {
    cat_name: string;
    existing_subcategories: number[]; // Array of IDs
    subcat_name?: string | null; // New subcategory name (optional)
    updated_by: number;
  }
) => {
  try {
    const api = `${API_ENDPOINTS.PUT_CATEGORIES_BY_ID}${id}/`;
    // Ensure correct structure before sending request
    const requestData = {
      cat_name: data.cat_name,
      existing_subcategories: data.existing_subcategories.filter(
        (id) => id !== null
      ), // Remove null values
      subcat_name: data.subcat_name || null, // Ensure correct field name
      updated_by: data.updated_by,
    };
    const response = await apiClient.put(api, requestData);
    return response.data;
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      console.error(
        "Update Category API Error:",
        error.response?.data || error.message
      );
    } else {
      console.error("Unexpected error:", error);
    }
    throw error;
  }
};

export const deleteCategory = async (id: number) => {
  try {
    const api = `${API_ENDPOINTS.DELETE_CATEGORIES_BY_ID}${id}/`;
    const response = await apiClient.delete(api);
    return response.data;
  } catch (error: any) {
    console.error("Delete Category API Error:", error);
    throw error;
  }
};

export const activeCategory = async (id: number) => {
  try {
    const api = `${API_ENDPOINTS.ACTIVE_CATEGORIES_BY_ID}${id}/activate/`;
    const response = await apiClient.post(api);
    return response.data;
  } catch (error: any) {
    console.error("Delete Category API Error:", error);
    throw error;
  }
};

export const createSubCategory = async (data: {
  subcat_name: string;
  category: number;
}) => {
  try {
    const response = await apiClient.post(
      API_ENDPOINTS.POST_SUBCATEGORIES,
      data
    );
    return response.data;
  } catch (error: any) {
    console.error("Create SubCategory API Error:", error);
    throw error;
  }
};

export const getSubCategories = async () => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.GET_SUBCATEGORIES);
    return response.data;
  } catch (error: any) {
    console.error("Get SubCategories API Error:", error);
    throw error;
  }
};

export const updateSubCategory = async (
  id: number,
  data: { subcat_name: string; category: number }
) => {
  try {
    const api = `${API_ENDPOINTS.PUT_SUBCATEGORIES_BY_ID}${id}/`;
    const response = await apiClient.put(api, data);
    return response.data;
  } catch (error: any) {
    console.error("Update SubCategory API Error:", error);
    throw error;
  }
};

export const deleteSubCategory = async (id: number) => {
  try {
    const api = `${API_ENDPOINTS.DELETE_SUBCATEGORIES_BY_ID}${id}/`;
    const response = await apiClient.delete(api);
    return response.data;
  } catch (error: any) {
    console.error("Delete SubCategory API Error:", error);
    throw error;
  }
};

export const login = async (data: any) => {
  try {
    const api = `${API_ENDPOINTS.LOGIN}`;
    const response = await apiClient.post(api, data);
    console.log("werer", response);
    return response;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export const forgotpassword = async (data: any) => {
  try {
    const api = `${API_ENDPOINTS.FORGOTPASSWORD}`;

    const response = await apiClient.post(api, data);
    return response;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export const resetPassword = async (data: any) => {
  try {
    const api = `${API_ENDPOINTS.RESET_PASSWORD}`;
    const response = await apiClient.post(api, data);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);

    throw error;
  }
};
export const ChangePassword = async (data: {
  current_password: string;
  new_password: string;
  confirm_password: string;
}) => {
  try {
    const api = `${API_ENDPOINTS.CHANGE_PASSWORD}`;
    const response = await apiClient.post(api, data);
    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);

    throw error;
  }
};

export const validateResetToken = async (data: {
  token: string;
  user_id: string;
}) => {
  try {
    const api = `${API_ENDPOINTS.VALIDATE_TOKEN}`;
    const response = await apiClient.post(api, data);
    console.log("validate-token:", response);

    return response.data;
  } catch (error: any) {
    console.error("API Error: ", error);

    throw error;
  }
};
// export const save_fcm_token = async (data: any) => {
//   try {
//     const api = `${API_ENDPOINTS.FCM_TOKEN}`;
//     const response = await apiClient.post(api, data);
//     console.log("save_fcm_token:", response);

//     return response.data;
//   } catch (error: any) {
//     console.error("API Error: ", error);

//     throw error;
//   }
// };
export const save_fcm_token = async (data: any) => {
  try {
    const response = await apiClient.post(API_ENDPOINTS.FCM_TOKEN, data);
    console.log("✅ FCM Token Saved:", response.data);
    return response.data;
  } catch (error) {
    console.error("❌ Error Saving FCM Token:", error);
    throw error;
  }
};

export const fetchStatus = async () => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.FETCHSTATUSES);
    return response.data;
  } catch (error: any) {
    console.error(error);
    throw error;
  }
};

export const users = async () => {
  try {
    const api = `${API_ENDPOINTS.USER}`;

    const response = await apiClient.get(api);
    return response;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};

export const tickets = async (data: any) => {
  try {
    const response = await apiClientFormData.post(API_ENDPOINTS.TICKETS, data);
    return response.data;
  } catch (error: any) {
    console.error(error);
    throw error;
  }
};
export const ticketStatus = async () => {
  try {
    const api = `${API_ENDPOINTS.TICKET_STATUS}`;

    const response = await apiClient.get(api);
    return response;
  } catch (error: any) {
    console.error("API Error: ", error);
    throw error;
  }
};
export const ticketCategories = async () => {
  try {
    const api = `${API_ENDPOINTS.TICKET_CATEGORIES}`;
    const response = await apiClient.get(api);
    console.log("API Response:", response);
    return response.data;
  } catch (error: any) {
    console.error("API Error:", error);
    throw error;
  }
};
export const ticketsubCategories = async () => {
  try {
    const api = `${API_ENDPOINTS.TICKET_CATEGORIES}`;
    const response = await apiClient.get(api);
    console.log("API Response:", response);
    return response.data;
  } catch (error: any) {
    console.error("API Error:", error);
    throw error;
  }
};

export const getFeedbackValues = async () => {
  try {
    const api = `${API_ENDPOINTS.FEEDBACK_VALUES}`;
    const response = await apiClient.get(api);
    return response.data;
  } catch (error: any) {
    console.error("API Error");
    throw error;
  }
};

export const createFeedback = async (payload: any) => {
  try {
    const api = `${API_ENDPOINTS.CREATE_FEEDBACK}`;
    const response = await apiClient.post(api, payload);
    return response.data;
  } catch (error: any) {
    console.error("API Error");
    throw error;
  }
};
