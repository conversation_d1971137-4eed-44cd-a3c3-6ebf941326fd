<script lang="ts" setup>
import { onMounted, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import { computed } from "vue";
import Default from "../src/layouts/default.vue";
import {
  messaging,
  onMessage,
  vapidKey,
  retrieveFcmToken,
} from "./plugins/firebase";
import { save_fcm_token } from "./api/apiClient";

const route = useRoute();
const requiresAuth = computed(() => route?.meta?.requiresAuth ?? false);

onMounted(async () => {
  if ("Notification" in window && Notification.permission !== "granted") {
    const permission = await Notification.requestPermission();
    if (permission === "granted") {
      const token = await retrieveFcmToken(vapidKey);
      if (token && !localStorage.getItem("fcm_token_saved")) {
        const user = JSON.parse(localStorage.getItem("user") || "{}");
        await save_fcm_token({ fcm_token: token, user_id: user?.id });
        localStorage.setItem("fcm_token_saved", "true");
      }
    } else {
      console.warn("🔕 Notification permission denied");
    }
  }

  // Foreground notification
  onMessage(messaging, (payload: any) => {
    console.log("📩 Foreground Message Received:", payload);
    const { title, body } = payload.notification || {};
    if (title && body) {
      new Notification(title, {
        body,
        icon: "/firebase-logo.png",
      });
    }
  });

  // Optional logout socket logic
  const user = JSON.parse(localStorage.getItem("user") || "{}");
  if (user?.id) {
    console.log("🔌 Connecting logout socket for user:", user.id);
  }
});

onUnmounted(() => {
  console.log("🔌 Disconnect logout socket");
});

window.addEventListener("storage", (event) => {
  if (event.key === "user") {
    const user = JSON.parse(event.newValue || "{}");
    if (user?.id) {
      console.log("👤 User changed, reconnecting logout socket");
    }
  }
});
</script>

<template>
  <div class="app-main">
    <div v-if="requiresAuth">
      <Default>
        <router-view />
      </Default>
    </div>
    <router-view v-else />
  </div>
</template>

<style>
@import "./assets/css/font.css";
@import "./styles/style.scss";
.app-page-layout {
  position: absolute;
  margin: 0 10px;
  width: 100%;
  border-radius: 5px;
}
</style>
