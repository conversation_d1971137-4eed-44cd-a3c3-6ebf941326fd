import { describe, it, expect } from 'vitest'

describe('Basic Tests', () => {
  it('should pass a basic test', () => {
    expect(1 + 1).toBe(2)
  })

  it('should test string operations', () => {
    const str = 'Hello World'
    expect(str).toContain('World')
    expect(str.length).toBe(11)
  })

  it('should test array operations', () => {
    const arr = [1, 2, 3, 4, 5]
    expect(arr).toHaveLength(5)
    expect(arr).toContain(3)
  })
})
