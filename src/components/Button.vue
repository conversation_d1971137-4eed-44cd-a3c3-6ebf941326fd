<template>
  <button
    :type="type"
    :class="['btn', `btn-${variant}`, size]"
    v-bind="rest"
  >
    <span
      v-if="leftIcon"
      class="btn-icon left"
    >
      <slot name="leftIcon" />
    </span>
    <slot />
    <span
      v-if="rightIcon"
      class="btn-icon right"
    >
      <slot name="rightIcon" />
    </span>
  </button>
</template>

<script lang="ts">
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
export default {
  name: "AppButton",
  components: { FontAwesomeIcon },
  inheritAttrs: false,
  props: {
    variant: {
      type: String,
      required: true,
      default: "primary",
      loading: Boolean,
      validator: (value:string) =>
        [
          "progress",
          "open",
          "closed",
          "solved",
          "submit",
          "register",
          "add",
          "view",
          "edit",
          "cancel",
          "back",
        ].includes(value),
    },
    size: {
      type: String,
      default: "medium",
      validator: (value:string) => ["small", "medium", "large"].includes(value),
    },
    type: {
      type: String as () => "button" | "submit" | "reset",
      default: "button",
    },
    leftIcon: {
      type: Array, // For FontAwesome, e.g., ['fas', 'eye']
      default: null,
    },
    rightIcon: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    rest() {
      const { variant, size, type, leftIcon, rightIcon, ...restAttrs } =
        this.$attrs;
      return restAttrs;
    },
  },
};
</script>

<style scoped>
.btn {
  border: none;
  outline: none;
  white-space: nowrap;
  cursor: pointer;
  box-shadow: none !important;
  text-transform: none;
  margin: 0;
  transition: background-color 0.3s, color 0.3s;
  width: 127px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
.small {
  /* padding: 8px; */
  width: 80px;
}
.btn-progress:hover {
  background-color: #fff;
  color: #fb8903;
  border: 1px solid #fb8903;
}

.btn-progress {
  background-color: #fb8903;
  border: 1px solid #ff7780;
  border-radius: 50px;
  font-size: 13px;
  color: #fff;
}
.btn-open {
  background-color: #a131b2;
  font-size: 13px;
  border-radius: 50px;
  color: #fff;
}
.btn-open:hover {
  color: #a131b2;
  background-color: #fff;
  border: 1px solid #a131b2;
}
.btn-closed {
  background-color: #ef4d56;
  font-size: 13px;
  border-radius: 50px;
  color: #fff;
}
.btn-solved {
  background-color: #28a745;
  font-size: 13px;
  border-radius: 50px;
  color: #fff;
}
.btn-view {
  background-color: transparent;
  border: 1px solid #026bb1;
  font-size: 13px;
  border-radius: 5px;
  color: #026bb1;
}
.btn-view:hover {
  background-color: #026bb1;
  color: #fff;
}

.btn-edit {
  border: 1px solid #2dcc70;
  border-radius: 5px;
  color: #2dcc70;
  font-size: 13px;
}
.btn-edit:hover {
  background: #2dcc70;
  color: #fff;
}
.btn-submit {
  background-color: #2dcc70;
  color: #fff;
}
.btn-submit:hover {
  background-color: #2dcc70;
  color: #fff;
}
.btn-cancel {
  background-color: #f1f1f1;
  color: #7d7d7d;
}
.btn-back {
  color: #7d7d7d;
  border: 1px solid #7d7d7d;
  background: transparent;
}
.btn-back:hover {
  color: #7d7d7d;
  border: 1px solid #7d7d7d;
  background: transparent;
}
.btn-add {
  background-color: #2dcc70;
  color: #fff;
}
</style>
