<template>
  <div class="chart-wrapper">
    <!-- Chart Header with Title and Dropdown -->
    <div
      v-if="showHeader"
      class="chart-header d-flex justify-content-between align-items-center mb-3"
    >
      <h4 class="chart-title">
        {{ title }}
      </h4>
      <select
        v-if="showYearDropdown && years.length > 0"
        v-model="selectedYear"
        class="native-dropdown"
        @change="onYearChange"
      >
        <option
          v-for="year in years"
          :key="year"
          :value="year"
        >
          {{ year }}
        </option>
      </select>
    </div>

    <!-- Chart Container -->
    <div
      class="chart-container"
      :style="containerStyle"
    >
      <!-- ApexCharts -->
      <apexchart
        v-if="chartType === 'apex'"
        :type="apexType"
        :height="height"
        :width="width"
        :options="apexOptions"
        :series="apexSeries"
      />

      <!-- Chart.js Canvas -->
      <canvas
        v-else-if="chartType === 'chartjs'"
        :ref="canvasRef"
        :style="canvasStyle"
      />

      <!-- Progress Bar -->
      <div
        v-else-if="chartType === 'progress'"
        class="progress-container"
      >
        <div
          v-for="(item, index) in progressData"
          :key="index"
          class="progress-item mb-3"
        >
          <div class="d-flex justify-content-between mb-1">
            <span class="progress-label">{{ item.label }}</span>
            <span class="progress-value">{{ item.value }}{{ item.unit || "%" }}</span>
          </div>
          <div
            class="progress"
            :style="{ height: progressHeight + 'px' }"
          >
            <div
              class="progress-bar"
              :style="{
                width: item.percentage + '%',
                backgroundColor:
                  item.color || defaultColors[index % defaultColors.length],
              }"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  onMounted,
  watch,
  nextTick,
  computed,
  
} from "vue";
import type { PropType } from "vue";

import Chart from "chart.js/auto";
import VueApexCharts from "vue3-apexcharts";


// Interfaces for type safety
interface ChartDataItem {
  label: string;
  value: number;
  color?: string;
  percentage?: number;
  unit?: string;
}

interface ApexSeriesItem {
  name: string;
  data: number[];
  type?: string;
}

interface ChartJSDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  type?: string;
  fill?: boolean;
}

export default defineComponent({
  name: "ChartComponent",
  components: {
    apexchart: VueApexCharts,
  },
  props: {
    // Chart configuration
    chartType: {
      type: String as PropType<"apex" | "chartjs" | "progress">,
      default: "apex",
      validator: (value: string) =>
        ["apex", "chartjs", "progress"].includes(value),
    },

    // Chart type for ApexCharts
    apexType: {
      type: String as PropType<
        | "line"
        | "area"
        | "bar"
        | "pie"
        | "donut"
        | "radialBar"
        | "scatter"
        | "bubble"
        | "heatmap"
      >,
      default: "bar",
    },

    // Chart type for Chart.js
    chartjsType: {
      type: String as PropType<
        "line" | "bar" | "pie" | "doughnut" | "radar" | "polarArea"
      >,
      default: "bar",
    },

    // Chart dimensions
    height: {
      type: [String, Number],
      default: 350,
    },
    width: {
      type: [String, Number],
      default: "100%",
    },

    // Chart data
    data: {
      type: Array as PropType<ChartDataItem[]>,
      default: () => [],
    },

    // Labels for charts
    labels: {
      type: Array as PropType<string[]>,
      default: () => [],
    },

    // Series data for ApexCharts
    series: {
      type: Array as PropType<ApexSeriesItem[]>,
      default: () => [],
    },

    // Datasets for Chart.js
    datasets: {
      type: Array as PropType<ChartJSDataset[]>,
      default: () => [],
    },

    // Chart options
    options: {
      type: Object,
      default: () => ({}),
    },

    // Header configuration
    title: {
      type: String,
      default: "",
    },
    showHeader: {
      type: Boolean,
      default: true,
    },

    // Year dropdown
    showYearDropdown: {
      type: Boolean,
      default: false,
    },
    years: {
      type: Array as PropType<number[]>,
      default: () => [],
    },
    defaultYear: {
      type: Number,
      default: () => new Date().getFullYear(),
    },

    // Progress bar specific
    progressHeight: {
      type: Number,
      default: 20,
    },

    // Colors
    colors: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
  },

  emits: ["year-changed", "chart-ready"],

  setup(props, { emit }) {
const canvasRef = ref<HTMLCanvasElement | null>(null) as any;
    const chartInstance = ref<Chart | null>(null);
    const selectedYear = ref(props.defaultYear);

    // Default color palette
    const defaultColors = [
      "#FF6384",
      "#36A2EB",
      "#FFCE56",
      "#4BC0C0",
      "#9966FF",
      "#FF9F40",
      "#FF6384",
      "#C9CBCF",
      "#4BC0C0",
      "#FF6384",
    ];

    // Computed properties
    const containerStyle = computed(() => ({
      height:
        typeof props.height === "number" ? `${props.height}px` : props.height,
      width: typeof props.width === "number" ? `${props.width}px` : props.width,
    }));

    const canvasStyle = computed(() => ({
      width: "100%",
      height: "100%",
    }));

    // ApexCharts configuration
    const apexOptions = computed(() => {
      const baseOptions = {
        chart: {
          id: `chart-${Date.now()}`,
          toolbar: {
            show: true,
          },
        },
        colors: props.colors.length > 0 ? props.colors : defaultColors,
        xaxis: {
          categories: props.labels,
        },
        responsive: [
          {
            breakpoint: 480,
            options: {
              chart: {
                width: 200,
              },
              legend: {
                position: "bottom",
              },
            },
          },
        ],
      };

      return { ...baseOptions, ...props.options };
    });

    const apexSeries = computed(() => {
      if (props.series.length > 0) {
        return props.series;
      }

      // Convert data to series format if needed
      if (props.data.length > 0) {
        return [
          {
            name: "Data",
            data: props.data.map((item) => item.value),
          },
        ];
      }

      return [];
    });

    // Progress bar data
    const progressData = computed(() => {
      return props.data.map((item, index) => ({
        ...item,
        percentage:
          item.percentage ||
          (item.value / Math.max(...props.data.map((d) => d.value))) * 100,
        color:
          item.color ||
          props.colors[index] ||
          defaultColors[index % defaultColors.length],
      }));
    });

    // Chart.js methods
    const createChartJSChart = async () => {
      if (props.chartType !== "chartjs" || !canvasRef.value) return;

      await nextTick();

      // Destroy existing chart
      if (chartInstance.value) {
        chartInstance.value.destroy();
      }

      const ctx = canvasRef.value.getContext("2d");
      if (!ctx) return;

      let chartData;

      if (props.datasets.length > 0) {
        // Use provided datasets (cast to any to satisfy Chart.js typing)
        chartData = {
          labels: props.labels,
          datasets: props.datasets as any,
        };
      } else {
        // Convert data to Chart.js format
        const colors = props.colors.length > 0 ? props.colors : defaultColors;
        chartData = {
          labels: props.data.map((item) => item.label),
          datasets: [
            {
              label: "Data",
              data: props.data.map((item) => item.value),
              backgroundColor: colors.slice(0, props.data.length),
              borderColor: colors.slice(0, props.data.length),
              borderWidth: 1,
            },
          ],
        };
      }

      const defaultOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "top" as const,
          },
        },
      };

      chartInstance.value = new Chart(ctx, {
        type: props.chartjsType,
        data: chartData,
        options: { ...defaultOptions, ...props.options },
      });

      emit("chart-ready", chartInstance.value);
    };

    // Event handlers
    const onYearChange = () => {
      emit("year-changed", selectedYear.value);
    };

    // Watchers
    watch(
      () => props.data,
      () => {
        if (props.chartType === "chartjs") {
          createChartJSChart();
        }
      },
      { deep: true }
    );

    watch(
      () => props.datasets,
      () => {
        if (props.chartType === "chartjs") {
          createChartJSChart();
        }
      },
      { deep: true }
    );

    watch(
      () => props.labels,
      () => {
        if (props.chartType === "chartjs") {
          createChartJSChart();
        }
      },
      { deep: true }
    );

    // Lifecycle
    onMounted(() => {
      if (props.chartType === "chartjs") {
        createChartJSChart();
      }
      emit("chart-ready");
    });

    return {
      canvasRef,
      selectedYear,
      defaultColors,
      containerStyle,
      canvasStyle,
      apexOptions,
      apexSeries,
      progressData,
      onYearChange,
    };
  },
});
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}

.chart-header {
  padding: 10px 0;
}

.chart-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.native-dropdown {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  padding: 8px 16px;
  font-size: 14px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  outline: none;
  min-width: 100px;
}

.native-dropdown:hover {
  border-color: #999;
}

.native-dropdown:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.chart-container {
  position: relative;
  width: 100%;
}

.progress-container {
  padding: 20px;
}

.progress-item {
  margin-bottom: 15px;
}

.progress-label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.progress-value {
  font-weight: 600;
  color: #666;
  font-size: 14px;
}

.progress {
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .native-dropdown {
    width: 100%;
  }
}
</style>
