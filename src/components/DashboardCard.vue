<template>
  <section class="widget-stats-wrapper">
    <v-row class="card-section">
      <v-col
        v-for="card in filteredCards"
        :key="card.label"
        cols="12"
        sm="6"
        md="4"
        lg="3"
        xl="auto"
        xxl="auto"
      >
        <div
          class="widget-stats"
          :class="card.widgetStatsName"
          @click="$emit('card-clicked', card)"
        >
          <v-avatar class="widget-stats-icon">
            <v-icon>{{ card.icon }}</v-icon>
          </v-avatar>
          <div class="widget-stats-info">
            <h6>{{ card.label }}</h6>
            <h3>
              {{ ticketCount[card.countKey] || 0 }}
            </h3>
          </div>
          <div class="widget-more-info">
            <v-icon icon="mdi-arrow-right" />
          </div>
        </div>
      </v-col>
    </v-row>
  </section>
</template>

<script lang="ts">
import { defineComponent, computed } from "vue";

export default defineComponent({
  name: "DashboardCard",
  props: {
    ticketCount: {
      type: Object,
      required: true,
    },
    user: {
      type: Object,
      required: true,
    },
    isMyTickets: {
      type: Boolean,
      required: true,
    },
  },
  setup(props) {
    const cardData = [
      {
        label: "Total Tickets",
        countKey: "total_tickets",
        icon: "mdi-tray-full",
        bgColor: "#f8eaff",
        iconBgColor: "#9c27b0",
        widgetStatsName: "widget-stats-tickets",
      },
      {
        label: "Open Tickets",
        countKey: "open_tickets",
        icon: "mdi-file-document-check",
        bgColor: "#f8eaff",
        iconBgColor: "#9c27b0",
        widgetStatsName: "widget-stats-open",
      },
      {
        label: "Unassigned",
        countKey: "unassigned_tickets",
        icon: "mdi-account-question",
        bgColor: "#f0f3fc",
        iconBgColor: "#1e49d3",
        widgetStatsName: "widget-stats-unassigned",
        // roles: ["R001", "R002"],
      },
      {
        label: "In Progress",
        countKey: "pending_tickets",
        status: 2,
        icon: "mdi-progress-alert",
        bgColor: "#fff5ea",
        iconBgColor: "#fb8903",
        widgetStatsName: "widget-stats-pending",
      },
      {
        label: "Solved",
        countKey: "solved_tickets",
        status: 6,
        icon: "mdi-clipboard-check",
        bgColor: "#e5fdea",
        iconBgColor: "#28a745",
        widgetStatsName: "widget-stats-solved",
      },
      {
        label: "Closed",
        countKey: "closed_tickets",
        status: 7,
        icon: "mdi-check-decagram",
        bgColor: "#ffeced",
        iconBgColor: "#ef4d56",
        widgetStatsName: "widget-stats-closed",
      },
      {
        label: "Awaiting Approval",
        countKey: "awaiting_approval",
        status: 8,
        icon: "mdi-clock-alert",
        bgColor: "#ffeced",
        iconBgColor: "#ef4d56",
        widgetStatsName: "widget-stats-approval",
        // roles: ["R001", "R002", "R006"],
      },
      {
        label: "Awaiting User Response",
        countKey: "awaiting_response",
        status: 9,
        icon: "mdi-file-document-refresh",
        bgColor: "#ffeced",
        iconBgColor: "#ef4d56",
        widgetStatsName: "widget-stats-response",
        // roles: ["R001", "R002", "R006"],
      },
      {
        label: "Under Observation",
        countKey: "under_observer",
        status: 4,
        icon: "mdi-tray-alert",
        bgColor: "#ffeced",
        iconBgColor: "#ef4d56",
        widgetStatsName: "widget-stats-observation",
        // roles: ["R001", "R002", "R006"],
      },
      {
        label: "Pending Approval",
        countKey: "pending_approval",
        icon: "mdi-account-check",
        bgColor: "#ffeced",
        iconBgColor: "#ef4d56",
        widgetStatsName: "widget-stats-pending-approval",
        // roles: ["R003"],
      },
      {
        label: "My Tickets",
        countKey: "my_tickets_count",
        icon: "mdi-account",
        bgColor: "#f1c232",
        iconBgColor: "#f7bb00",
        widgetStatsName: "widget-stats-my-ticket",
      },
      {
        label: "Team Tickets",
        countKey: "team_members_tickets_count",
        icon: "mdi-account-group",
        bgColor: "#fff3e0",
        iconBgColor: "#ef6c00",
        widgetStatsName: "widget-stats-team-ticket",
      },
      {
        label: "Cancel Tickets",
        countKey: "cancel_tickets",
        icon: "mdi-file-document-remove",
        status: 10,
        bgColor: "#fff3e0",
        iconBgColor: "#ef6c00",
        widgetStatsName: "widget-stats-cancel-tickets",
        // roles: ["R001", "R002"],
      },
    ];

    const filteredCards = computed(() => {
      return cardData.filter((card) => {
        if (card.label !== "My Tickets" && card.label !== "Team Tickets") {
          return !card.roles || card.roles.includes(props.user.role);
        }

        if (card.label === "My Tickets" && props.isMyTickets) return true;
        if (card.label === "Team Tickets" && !props.isMyTickets) return true;

        return false;
      });
    });

    return { filteredCards };
  },
});
</script>
