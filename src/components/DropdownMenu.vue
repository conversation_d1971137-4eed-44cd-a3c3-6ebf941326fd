<template>
  <v-list-group
    v-model="isOpen"
    :class="{ 'active-group': activeItem === activeKey }"
    @click="setActive(activeKey || '')"
  >
    <template #activator="{ props }">
      <v-list-item
        v-bind="props"
        class="text-size"
      >
        <template #prepend>
          <img
            :src="icon"
            :alt="title + ' Icon'"
            class="custom-icon"
          >
        </template>
        <v-list-item-title> {{ title }} </v-list-item-title>
      </v-list-item>
    </template>

    <v-list-group
      v-for="(subItem, index) in subItems"
      :key="index"
      v-model="subItem.isOpen"
      :class="{ 'active-group': activeItem === subItem.key }"
      @click="setActive(subItem.key)"
    >
      <template #activator="{ props }">
        <v-list-item
          v-bind="props"
          class="custom-item"
        >
          <v-list-item-title> {{ subItem.title }} </v-list-item-title>
        </v-list-item>
      </template>
    </v-list-group>
  </v-list-group>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits } from "vue";

// Props passed to the component
defineProps({
  title: String,
  icon: String,
  activeKey: String,
  subItems: {
    type: Array as () => Array<{ key: string; title: string; isOpen: boolean }>,
    default: () => [],
  },
  activeItem: String,
});

// Emit event to update the active item in the parent component
const emit = defineEmits<{
  (event: "setActive", key: string): void;
}>();

const isOpen = ref(false);

// Emit active key to the parent component
const setActive = (key:string) => {
  emit("setActive", key ?? "");
};
</script>

<style scoped>
.text-size {
  font-size: 14px !important;
}
.custom-item {
  font-size: 14px !important;
  color: rgb(167, 166, 166); /* Gray text */
}
.custom-icon {
  width: 20px;
  height: 20px;
}
.active-group {
  background-color: #2dcc70 !important; /* Primary color for active item */
  color: white !important;
  border-radius: 12px;
}
</style>

function emit(arg0: string, key: any) {
  throw new Error("Function not implemented.");
}

function emit(arg0: string, key: any) {
  throw new Error("Function not implemented.");
}
