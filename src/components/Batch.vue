<template>
  <label
    :type="type"
    :class="['batch', `batch-${variant}`, size]"
    v-bind="rest"
  >
    <slot />
  </label>
</template>

<script lang="ts">
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
export default {
  name: "Batch",
  components: { FontAwesomeIcon },
  inheritAttrs: false,
  props: {
    variant: {
      type: String,
      required: true,
      default: "primary",
      validator: (value:string) =>
        [
          "ticket-all",
          "high",
          "open",
          "document",
          "ticket-view",
          "ticket-edit",
          "conversation",
        ].includes(value),
    },
    size: {
      type: String,
      default: "medium",
      validator: (value:string) => ["small", "medium", "large"].includes(value),
    },
    type: {
      type: String,
      default: "button",
    },
    leftIcon: {
      type: Array, // For FontAwesome, e.g., ['fas', 'eye']
      default: null,
    },
    rightIcon: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    rest() {
      const { variant, size, type, leftIcon, rightIcon, ...restAttrs } =
        this.$attrs;
      return restAttrs;
    },
  },
};
</script>

<style lang="scss" scoped>
.batch {
  padding: 4px 8px;
  font-size: 12px;
}
.batch-ticket-all {
  background: #fff;
  color: #026bb1;
  border: 1px solid #026bb1;
  border-radius: 5px;
}
.batch-ticket-view {
  background: #026bb1;
  color: #fff;
  border-radius: 25px;
}
.batch-ticket-edit {
  background: #e9e9e9;
  color: #000;
  border-radius: 25px;
}
.batch-documents {
  background: transparent;
  color: #026bb1;
  border-radius: 25px;
  border: 1px solid #026bb1;
  & > a{
    color: #026bb1;
  }
}
.batch-conversation {
  background: #f3fff8;
  color: #2dcc70;
  border-radius: 25px;
  border: 1px solid #bfe9d1;
  & > a{
    color: #2dcc70;
  }
}
</style>
