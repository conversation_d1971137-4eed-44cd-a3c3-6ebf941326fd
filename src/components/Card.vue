<template>
  <v-card class="user-data-card">
    <v-row class="user-data">
      <v-col cols="10">
        <v-row>
          <v-col
            v-for="(field, index) in fields"
            :key="index"
            cols="3"
            class="user-data-details"
          >
            <p class="user-data-header">
              {{ field.label }}
            </p>
            <p class="user-data-value">
              {{ field.value }}
            </p>
          </v-col>
        </v-row>
      </v-col>
      <v-col
        cols="2"
        class="user-data-btn"
      >
        <slot name="actions" />
      </v-col>
    </v-row>
  </v-card>
</template>
  
  <script lang="ts">
  import { defineComponent, type PropType } from "vue";
  
  // Define the type for fields
  interface Field {
    label: string;
    value: string | number | null;
  }
  
  export default defineComponent({
    name: "ProjectCard",
    props: {
      fields: {
        type: Array as PropType<Field[]>, // Specify the type for fields as an array of Field
        required: true,
      },
    },
  });
  </script>
  
  <style scoped>
  /* Scoped styles specific to ProjectCard */
  </style>
  