#!/bin/bash

# Script to generate environment files for different environments
# Usage: ./generate-env.sh [environment]
# Example: ./generate-env.sh staging

# Default to development if no environment is specified
ENV=${1:-development}

echo "Generating environment file for $ENV environment..."

case $ENV in
  development)
    echo "VITE_API_BASE_URL=http://localhost:8000/api/" > .env.development
    echo "VITE_WS_URL=ws://localhost:8000/ws/ticket/" >> .env.development
    echo "Generated .env.development file"
    ;;

  staging)
    echo "VITE_API_BASE_URL=http://internal-project.nexware-global.com:9019/api/" > .env.staging
    echo "VITE_WS_URL=ws://localhost:8000/ws/ticket/" >> .env.staging
    echo "Generated .env.staging file"
    ;;

  production)
    echo "VITE_API_BASE_URL=https://ticket.nexware-global.com:9049/api/" > .env.production
    echo "VITE_WS_URL=wss://internal-project.nexware-global.com:8001/ws/ticket/" >> .env.production
    echo "Generated .env.production file"
    ;;

  *)
    echo "Unknown environment: $ENV"
    echo "Available environments: development, staging, production"
    exit 1
    ;;
esac

echo "Environment file generation complete!"
