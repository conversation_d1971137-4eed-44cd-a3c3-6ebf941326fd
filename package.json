{"name": "ticket-management", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "vite", "dev:staging": "vite --mode staging", "dev:prod": "vite --mode production", "build": "run-p type-check \"build-only {@}\" --", "build:staging": "run-p type-check \"build-only:staging {@}\" --", "build:prod": "run-p type-check \"build-only:prod {@}\" --", "preview": "vite preview", "build-only": "vite build", "build-only:staging": "vite build --mode staging", "build-only:prod": "vite build --mode production", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix", "test": "jest", "generate-env:dev": "bash generate-env.sh development", "generate-env:staging": "bash generate-env.sh staging", "generate-env:prod": "bash generate-env.sh production"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@mdi/font": "^7.4.47", "@popperjs/core": "^2.11.8", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vuepic/vue-datepicker": "^11.0.1", "apexcharts": "^4.7.0", "axios": "^1.7.9", "bootstrap": "^5.3.3", "chart.js": "^4.1.1", "core-js": "^3.37.1", "exceljs": "^4.4.0", "firebase": "^11.3.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "moment": "^2.30.1", "pinia-plugin-persistedstate": "^4.2.0", "quill": "^2.0.3", "roboto-fontface": "*", "socket.io-client": "^4.8.1", "vue": "^3.5.13", "vue-draggable-next": "^2.2.1", "vue-toastification": "^2.0.0-rc.5", "vue3-apexcharts": "^1.8.0", "vue3-quill": "^0.3.1", "vuetify": "^3.7.13", "webpack-bundle-tracker": "^3.1.1", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@eslint/js": "^9.14.0", "@fortawesome/fontawesome-free": "^6.7.2", "@tsconfig/node20": "^20.1.6", "@types/jwt-decode": "^2.2.1", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-typescript": "^14.1.3", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.1", "pinia": "^2.1.7", "sass": "^1.77.8", "sass-embedded-darwin-arm64": "^1.77.8", "sass-embedded-linux-x64": "^1.77.8", "typescript": "~5.6.3", "unplugin-auto-import": "^0.17.6", "unplugin-fonts": "^1.1.1", "unplugin-vue-components": "^0.27.2", "unplugin-vue-router": "^0.10.0", "vite": "^5.4.10", "vite-plugin-vue-layouts": "^0.11.0", "vite-plugin-vuetify": "^2.1.0", "vitest": "^3.2.4", "vue-router": "^4.4.0", "vue-tsc": "^2.1.10"}}