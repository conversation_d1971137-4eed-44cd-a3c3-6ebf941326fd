// importScripts("https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js");
// importScripts("https://www.gstatic.com/firebasejs/9.6.1/firebase-messaging-compat.js");

// const firebaseConfig = {
//     apiKey: "AIzaSyCMWc3XxRqiv1VVv71TrWg1ByjcfTAwP2c",
//     authDomain: "nex-ticketting.firebaseapp.com",
//     projectId: "nex-ticketting",
//     storageBucket: "nex-ticketting.firebasestorage.app",

//     messagingSenderId: "584583859864",
//     appId: "1:584583859864:web:078f14e255049f66314aed",
//     measurementId: "G-CVR3SWLH7X"
//   };

// // Initialize Firebase
// firebase.initializeApp(firebaseConfig);
// const messaging = firebase.messaging();

// // Handle background notifications
// // messaging.onBackgroundMessage((payload) => {
// //   console.log("Received background message: ", payload);

// //   self.registration.showNotification(payload.notification.title, {
// //     body: payload.notification.body,
// //     icon: "/firebase-logo.png"
// //   });

// // });
// messaging.onBackgroundMessage(function(payload) {
//   const { title, body } = payload.notification;
//   self.registration.showNotification(title, { body });
// });
importScripts(
  "https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"
);
importScripts(
  "https://www.gstatic.com/firebasejs/9.6.1/firebase-messaging-compat.js"
);

const firebaseConfig = {
  apiKey: "AIzaSyCMWc3XxRqiv1VVv71TrWg1ByjcfTAwP2c",
  authDomain: "nex-ticketting.firebaseapp.com",
  projectId: "nex-ticketting",
  storageBucket: "nex-ticketting.appspot.com",
  messagingSenderId: "584583859864",
  appId: "1:584583859864:web:078f14e255049f66314aed",
  measurementId: "G-CVR3SWLH7X",
};

firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

messaging.onBackgroundMessage(function (payload) {
  console.log("📥 Background Message:", payload);
  const { title, body } = payload.notification;
  self.registration.showNotification(title, { body });
});
