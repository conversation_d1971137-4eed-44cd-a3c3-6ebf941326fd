image: node:22 # Use Node.js 22 image

# Global before_script for common setup
before_script:
  - echo "Starting CI/CD Pipeline for commit $CI_COMMIT_SHA"
  - echo "Branch $CI_COMMIT_BRANCH"
  - node --version
  - npm --version

stages:
  - test
  - security
  - build

variables:
  NODE_OPTIONS: "--max-old-space-size=4096" # Increase memory for large builds
  NPM_CONFIG_CACHE: "$CI_PROJECT_DIR/.npm"

# Linting & Unit Tests
lint-test-job:
  stage: test
  cache:
    key: 
      files:
        - package-lock.json
    paths:
      - node_modules/
  script:
    - echo "Installing dependencies for linting and testing..."
    - npm ci --legacy-peer-deps || (echo "npm ci failed, falling back to npm install" && npm install --legacy-peer-deps)
    - echo "Checking installed dependencies..."
    - npm list @vue/test-utils || echo "vue/test-utils not found, continuing..."
    - echo "Running ESLint..."
    - npm run lint
    - echo "Running Unit Tests..."
    - npm run test:coverage || npm run test || echo "Tests not configured, skipping..."
    - echo "Running Security Audit..."
    - npm audit --audit-level=high || echo "Security audit completed with warnings"
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == "develop" || $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master"

# Security Scanning
security-scan-job:
  stage: security
  cache:
    key: 
      files:
        - package-lock.json
    paths:
      - node_modules/
  script:
    - echo "Installing dependencies for security scanning..."
    - npm ci --legacy-peer-deps || (echo "npm ci failed, falling back to npm install" && npm install --legacy-peer-deps)
    - echo "Running dependency vulnerability scan..."
    - npm audit --audit-level=moderate --json > audit-report.json || true
    - echo "Checking for known security issues..."
    - npx audit-ci --moderate || echo "Security scan completed with warnings"
  artifacts:
    reports:
      dependency_scanning: audit-report.json
    paths:
      - audit-report.json
    expire_in: 1 week
    when: always
  rules:
    - if: $CI_COMMIT_BRANCH == "develop" || $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master"
  allow_failure: true

# Build Vue.js Project for Staging
build-staging-job:
  stage: build
  cache:
    key: 
      files:
        - package-lock.json
    paths:
      - node_modules/
  script:
    - echo "Installing dependencies..."
    - npm ci --legacy-peer-deps || (echo "npm ci failed, falling back to npm install" && npm install --legacy-peer-deps)
    - echo "Creating environment file for staging..."
    - echo "VITE_API_BASE_URL=http://internal-project.nexware-global.com:9019/api/" > .env.staging
    - echo "VITE_WS_URL=ws://localhost:8000/ws/ticket/" >> .env.staging
    - echo "Building Vue.js project for staging..."
    - npm run build:staging
    - echo "Analyzing bundle size..."
    - du -sh dist/
    - echo "Installing zip utility..."
    - apt-get update && apt-get install -y zip
    - echo "Zipping build files..."
    - zip -r build.zip dist/ # Create build.zip
  artifacts:
    paths:
      - dist/ # Store the build files
      - build.zip # Ensure build.zip is available for deployment
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"

# Build Vue.js Project for Production
build-production-job:
  stage: build
  cache:
    key: 
      files:
        - package-lock.json
    paths:
      - node_modules/
  script:
    - echo "Installing dependencies..."
    - npm ci --legacy-peer-deps || (echo "npm ci failed, falling back to npm install" && npm install --legacy-peer-deps)
    - echo "Creating environment file for production..."
    - echo "VITE_API_BASE_URL=https://ticket.nexware-global.com:9049/api/" > .env.production
    - echo "VITE_WS_URL=wss://internal-project.nexware-global.com:8001/ws/ticket/" >> .env.production
    - echo "Building Vue.js project for production..."
    - npm run build:prod
    - echo "Analyzing bundle size..."
    - du -sh dist/
    - echo "Installing zip utility..."
    - apt-get update && apt-get install -y zip
    - echo "Zipping build files..."
    - zip -r build.zip dist/ # Create build.zip
  artifacts:
    paths:
      - dist/ # Store the build files
      - build.zip # Ensure build.zip is available for deployment
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master"


